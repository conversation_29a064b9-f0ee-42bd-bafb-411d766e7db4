/**
 * VirtualX Pro - Training Standards Presentation
 * Interactive slide deck with animations and multimedia elements
 */

class TrainingPresentation {
    constructor() {
        this.currentSlide = 0;
        this.totalSlides = 9;
        this.isAutoplay = false;
        this.autoplayInterval = null;
        this.autoplayDelay = 8000; // 8 seconds per slide
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.updateSlideIndicators();
        this.updateNavigationButtons();
        this.animateCounters();
        this.startParticleAnimation();
    }

    setupEventListeners() {
        // Keyboard navigation
        document.addEventListener('keydown', (e) => {
            switch(e.key) {
                case 'ArrowLeft':
                    this.changeSlide(-1);
                    break;
                case 'ArrowRight':
                case ' ':
                    this.changeSlide(1);
                    break;
                case 'Home':
                    this.goToSlide(0);
                    break;
                case 'End':
                    this.goToSlide(this.totalSlides - 1);
                    break;
                case 'Escape':
                    this.stopAutoplay();
                    break;
            }
        });

        // Touch/swipe support for mobile
        let startX = 0;
        let startY = 0;
        
        document.addEventListener('touchstart', (e) => {
            startX = e.touches[0].clientX;
            startY = e.touches[0].clientY;
        });

        document.addEventListener('touchend', (e) => {
            if (!startX || !startY) return;
            
            const endX = e.changedTouches[0].clientX;
            const endY = e.changedTouches[0].clientY;
            
            const diffX = startX - endX;
            const diffY = startY - endY;
            
            // Only trigger if horizontal swipe is more significant than vertical
            if (Math.abs(diffX) > Math.abs(diffY) && Math.abs(diffX) > 50) {
                if (diffX > 0) {
                    this.changeSlide(1); // Swipe left - next slide
                } else {
                    this.changeSlide(-1); // Swipe right - previous slide
                }
            }
            
            startX = 0;
            startY = 0;
        });

        // Auto-hide navigation after inactivity
        let inactivityTimer;
        const hideNavigation = () => {
            document.querySelector('.nav-controls').style.opacity = '0.3';
            document.querySelector('.slide-indicators').style.opacity = '0.3';
        };
        
        const showNavigation = () => {
            document.querySelector('.nav-controls').style.opacity = '1';
            document.querySelector('.slide-indicators').style.opacity = '1';
            clearTimeout(inactivityTimer);
            inactivityTimer = setTimeout(hideNavigation, 3000);
        };

        document.addEventListener('mousemove', showNavigation);
        document.addEventListener('click', showNavigation);
    }

    changeSlide(direction) {
        const newSlide = this.currentSlide + direction;
        
        if (newSlide >= 0 && newSlide < this.totalSlides) {
            this.goToSlide(newSlide);
        }
    }

    goToSlide(slideIndex) {
        if (slideIndex < 0 || slideIndex >= this.totalSlides) return;
        
        const slides = document.querySelectorAll('.slide');
        const currentSlideElement = slides[this.currentSlide];
        const newSlideElement = slides[slideIndex];
        
        // Remove active class from current slide
        currentSlideElement.classList.remove('active');
        
        // Add transition classes
        if (slideIndex > this.currentSlide) {
            currentSlideElement.classList.add('prev');
            newSlideElement.classList.remove('prev');
        } else {
            currentSlideElement.classList.remove('prev');
            newSlideElement.classList.add('prev');
        }
        
        // Activate new slide
        setTimeout(() => {
            newSlideElement.classList.add('active');
            this.currentSlide = slideIndex;
            this.updateSlideIndicators();
            this.updateNavigationButtons();
            this.triggerSlideAnimations();
            this.animateCounters();
        }, 100);
    }

    updateSlideIndicators() {
        const indicators = document.querySelectorAll('.indicator');
        indicators.forEach((indicator, index) => {
            indicator.classList.toggle('active', index === this.currentSlide);
        });
    }

    updateNavigationButtons() {
        const prevBtn = document.getElementById('prevBtn');
        const nextBtn = document.getElementById('nextBtn');
        
        prevBtn.disabled = this.currentSlide === 0;
        nextBtn.disabled = this.currentSlide === this.totalSlides - 1;
        
        prevBtn.style.opacity = this.currentSlide === 0 ? '0.5' : '1';
        nextBtn.style.opacity = this.currentSlide === this.totalSlides - 1 ? '0.5' : '1';
    }

    toggleAutoplay() {
        const playBtn = document.getElementById('playBtn');
        const icon = playBtn.querySelector('i');
        
        if (this.isAutoplay) {
            this.stopAutoplay();
            icon.className = 'fas fa-play';
        } else {
            this.startAutoplay();
            icon.className = 'fas fa-pause';
        }
    }

    startAutoplay() {
        this.isAutoplay = true;
        this.autoplayInterval = setInterval(() => {
            if (this.currentSlide < this.totalSlides - 1) {
                this.changeSlide(1);
            } else {
                this.stopAutoplay();
            }
        }, this.autoplayDelay);
    }

    stopAutoplay() {
        this.isAutoplay = false;
        if (this.autoplayInterval) {
            clearInterval(this.autoplayInterval);
            this.autoplayInterval = null;
        }
        
        const playBtn = document.getElementById('playBtn');
        const icon = playBtn.querySelector('i');
        icon.className = 'fas fa-play';
    }

    triggerSlideAnimations() {
        const currentSlide = document.querySelectorAll('.slide')[this.currentSlide];
        const animatedElements = currentSlide.querySelectorAll('.interactive-card, .module-card, .stat-item');
        
        animatedElements.forEach((element, index) => {
            element.style.animation = 'none';
            setTimeout(() => {
                element.style.animation = `zoomIn 0.6s ease-out ${index * 0.1}s both`;
            }, 100);
        });
    }

    animateCounters() {
        const currentSlide = document.querySelectorAll('.slide')[this.currentSlide];
        const counters = currentSlide.querySelectorAll('[data-count]');
        
        counters.forEach(counter => {
            const target = parseInt(counter.dataset.count);
            const duration = 2000; // 2 seconds
            const increment = target / (duration / 16); // 60 FPS
            let current = 0;
            
            const updateCounter = () => {
                current += increment;
                if (current < target) {
                    counter.textContent = Math.floor(current);
                    requestAnimationFrame(updateCounter);
                } else {
                    counter.textContent = target;
                }
            };
            
            setTimeout(updateCounter, 500); // Delay start
        });
    }

    startParticleAnimation() {
        // Create floating particles for visual enhancement
        const particleContainer = document.createElement('div');
        particleContainer.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        `;
        document.body.appendChild(particleContainer);

        for (let i = 0; i < 20; i++) {
            const particle = document.createElement('div');
            particle.style.cssText = `
                position: absolute;
                width: 4px;
                height: 4px;
                background: rgba(255, 255, 255, 0.3);
                border-radius: 50%;
                animation: float ${5 + Math.random() * 10}s linear infinite;
                left: ${Math.random() * 100}%;
                top: ${Math.random() * 100}%;
                animation-delay: ${Math.random() * 5}s;
            `;
            particleContainer.appendChild(particle);
        }
    }
}

// Module interaction functions
function highlightLevel(level) {
    const cards = document.querySelectorAll('.interactive-card');
    cards.forEach(card => {
        card.style.transform = 'scale(0.95)';
        card.style.opacity = '0.7';
    });
    
    // Find and highlight the selected level
    const levelMap = {
        'foundation': 0,
        'intermediate': 1,
        'advanced': 2,
        'expert': 3
    };
    
    const selectedCard = cards[levelMap[level]];
    if (selectedCard) {
        selectedCard.style.transform = 'scale(1.1)';
        selectedCard.style.opacity = '1';
        selectedCard.style.background = 'rgba(255, 255, 255, 0.3)';
    }
    
    // Reset after 2 seconds
    setTimeout(() => {
        cards.forEach(card => {
            card.style.transform = '';
            card.style.opacity = '';
            card.style.background = '';
        });
    }, 2000);
}

function showModuleDetails(moduleId) {
    const moduleData = {
        'physics': {
            title: 'X-ray Physics Fundamentals',
            description: 'Master X-ray production, interaction, and safety principles',
            features: ['3D Electron-Target Simulator', 'Spectrum Analysis', 'Safety Calculations']
        },
        'components': {
            title: 'System Components & Architecture',
            description: 'Explore 3D system models and virtual assembly',
            features: ['Photorealistic Models', 'Virtual Assembly', 'Multi-vendor Support']
        },
        'safety': {
            title: 'Safety & Radiation Protection',
            description: 'ALARA principles and emergency procedures',
            features: ['Radiation Visualizer', 'Emergency Simulator', 'Compliance Tools']
        },
        'generators': {
            title: 'Generator Systems & Power Electronics',
            description: 'IGBT circuits and control systems analysis',
            features: ['Virtual Oscilloscope', 'Circuit Simulation', 'Power Analysis']
        },
        'ai-diagnostics': {
            title: 'AI-Powered Diagnostics',
            description: 'Machine learning for fault detection',
            features: ['Neural Networks', '10,000+ Cases', '95.2% Accuracy']
        }
    };
    
    const module = moduleData[moduleId];
    if (module) {
        // Create modal or tooltip with module details
        const modal = document.createElement('div');
        modal.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 2rem;
            border-radius: 15px;
            z-index: 10000;
            max-width: 500px;
            text-align: center;
            animation: zoomIn 0.3s ease-out;
        `;
        
        modal.innerHTML = `
            <h3>${module.title}</h3>
            <p style="margin: 1rem 0;">${module.description}</p>
            <div style="display: flex; flex-wrap: wrap; gap: 0.5rem; justify-content: center;">
                ${module.features.map(feature => 
                    `<span style="background: rgba(255,255,255,0.2); padding: 0.25rem 0.75rem; border-radius: 12px; font-size: 0.8rem;">${feature}</span>`
                ).join('')}
            </div>
            <button onclick="this.parentElement.remove()" style="margin-top: 1rem; padding: 0.5rem 1rem; background: #0066cc; color: white; border: none; border-radius: 5px; cursor: pointer;">Close</button>
        `;
        
        document.body.appendChild(modal);
        
        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (modal.parentElement) {
                modal.remove();
            }
        }, 5000);
    }
}

// Call-to-action functions
function startDemo() {
    window.open('training-modules.html', '_blank');
}

function scheduleConsultation() {
    alert('Consultation scheduling feature would integrate with calendar system');
}

function downloadBrochure() {
    alert('Brochure download feature would generate PDF with detailed specifications');
}

// Global functions for HTML onclick handlers
function changeSlide(direction) {
    if (window.presentation) {
        window.presentation.changeSlide(direction);
    }
}

function goToSlide(index) {
    if (window.presentation) {
        window.presentation.goToSlide(index);
    }
}

function toggleAutoplay() {
    if (window.presentation) {
        window.presentation.toggleAutoplay();
    }
}

// Initialize presentation when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.presentation = new TrainingPresentation();
    
    // Add additional CSS for particle animation
    const style = document.createElement('style');
    style.textContent = `
        @keyframes float {
            0% { transform: translateY(100vh) rotate(0deg); opacity: 0; }
            10% { opacity: 1; }
            90% { opacity: 1; }
            100% { transform: translateY(-100vh) rotate(360deg); opacity: 0; }
        }
    `;
    document.head.appendChild(style);
});
