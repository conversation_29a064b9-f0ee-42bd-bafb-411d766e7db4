/* CSS for X-ray System Troubleshooting Guide Module */

/* Overview Section */
.overview {
    padding: 3rem 0;
    background-color: #f8f9fa;
}

.info-box {
    display: flex;
    background-color: white;
    border-radius: 8px;
    padding: 1.5rem;
    margin-top: 2rem;
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1);
    gap: 1.5rem;
}

.info-icon {
    font-size: 2.5rem;
    color: #3498db;
    flex-shrink: 0;
}

.info-content h3 {
    margin-top: 0;
    color: #2c3e50;
    margin-bottom: 1rem;
}

.info-content ol, .info-content ul {
    margin-left: 1.5rem;
}

.info-content li {
    margin-bottom: 0.5rem;
}

/* Troubleshooting Tools */
.troubleshooting-tools {
    margin-top: 3rem;
    background-color: white;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1);
}

.troubleshooting-tools h3 {
    margin-top: 0;
    color: #2c3e50;
    margin-bottom: 1.5rem;
}

.tools-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1.5rem;
}

.tool-item {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 1.5rem;
    text-align: center;
    transition: transform 0.3s ease;
}

.tool-item:hover {
    transform: translateY(-5px);
}

.tool-icon {
    font-size: 2.5rem;
    color: #3498db;
    margin-bottom: 1rem;
}

.tool-item h4 {
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.tool-item p {
    color: #7f8c8d;
    font-size: 0.9rem;
}

/* Troubleshooting Sections */
.troubleshooting-section {
    padding: 4rem 0;
}

.troubleshooting-section:nth-child(odd) {
    background-color: white;
}

.troubleshooting-section:nth-child(even) {
    background-color: #f8f9fa;
}

.issue-container {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    margin-top: 2rem;
}

.issue-card {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.issue-header {
    padding: 1.5rem;
    background-color: #f8f9fa;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
}

.issue-header h3 {
    margin: 0;
    color: #2c3e50;
}

.expand-btn {
    background: none;
    border: none;
    color: #3498db;
    font-size: 1.2rem;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.expand-btn.active {
    transform: rotate(180deg);
}

.issue-content {
    padding: 0;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.5s ease, padding 0.5s ease;
}

.issue-content.active {
    padding: 1.5rem;
    max-height: 2000px; /* Large enough to accommodate content */
}

.symptoms, .possible-causes, .troubleshooting-steps, .solution {
    margin-bottom: 1.5rem;
}

.symptoms h4, .possible-causes h4, .troubleshooting-steps h4, .solution h4 {
    color: #3498db;
    margin-bottom: 0.5rem;
}

.symptoms ul, .possible-causes ul, .troubleshooting-steps ol, .solution ul {
    margin-left: 1.5rem;
}

.symptoms li, .possible-causes li, .troubleshooting-steps li, .solution li {
    margin-bottom: 0.5rem;
}

.caution {
    background-color: #ffecb3;
    color: #e67e22;
    padding: 0.75rem;
    border-radius: 4px;
    margin-top: 1rem;
    font-weight: 600;
}

/* Simulator Section */
.simulator-section {
    padding: 4rem 0;
    background-color: white;
}

.simulator-container {
    margin-top: 2rem;
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1);
}

.scenario-selection {
    margin-bottom: 2rem;
}

.scenario-selection h3 {
    margin-top: 0;
    color: #2c3e50;
    margin-bottom: 1rem;
}

.scenario-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
}

.scenario-btn {
    padding: 0.75rem 1.5rem;
    background-color: #3498db;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.scenario-btn:hover {
    background-color: #2980b9;
}

.scenario-btn.active {
    background-color: #2c3e50;
}

.simulator-interface {
    display: flex;
    flex-wrap: wrap;
    gap: 1.5rem;
}

.simulator-display {
    flex: 1 1 400px;
    background-color: white;
    border-radius: 8px;
    padding: 1.5rem;
    min-height: 300px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: center;
    align-items: center;
}

.default-message {
    color: #7f8c8d;
    text-align: center;
    font-style: italic;
}

.simulator-controls {
    flex: 1 1 300px;
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.action-buttons {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 1rem;
}

.action-btn {
    padding: 0.75rem 1rem;
    background-color: #3498db;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.action-btn:hover:not(:disabled) {
    background-color: #2980b9;
}

.action-btn:disabled {
    background-color: #bdc3c7;
    cursor: not-allowed;
}

.simulator-log {
    background-color: white;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.simulator-log h4 {
    margin-top: 0;
    color: #2c3e50;
    margin-bottom: 1rem;
    border-bottom: 2px solid #3498db;
    padding-bottom: 0.5rem;
}

.log-content {
    max-height: 200px;
    overflow-y: auto;
    font-family: monospace;
    font-size: 0.9rem;
    line-height: 1.5;
    padding: 0.5rem;
    background-color: #f8f9fa;
    border-radius: 4px;
}

/* Navigation Buttons */
.navigation-buttons {
    padding: 2rem 0;
    background-color: #f8f9fa;
    border-top: 1px solid #e9ecef;
}

.nav-buttons {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 1rem;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .info-box {
        flex-direction: column;
    }
    
    .simulator-interface {
        flex-direction: column;
    }
    
    .simulator-display, .simulator-controls {
        flex: none;
        width: 100%;
    }
    
    .action-buttons {
        grid-template-columns: 1fr 1fr;
    }
    
    .nav-buttons {
        justify-content: center;
    }
    
    .nav-buttons .btn {
        width: 100%;
        text-align: center;
        margin-bottom: 0.5rem;
    }
}
