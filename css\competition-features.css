/* Competition-Winning Features Styles */

/* Systems Showcase Section */
.systems-showcase {
    padding: 4rem 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
}

.section-header {
    text-align: center;
    margin-bottom: 3rem;
}

.section-header h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--enterprise-dark);
    margin-bottom: 1rem;
}

.section-subtitle {
    display: block;
    font-size: 1.2rem;
    color: #666;
    font-weight: 400;
    margin-top: 0.5rem;
}

.systems-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.system-category {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-enterprise);
    overflow: hidden;
    transition: var(--transition);
}

.system-category:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0, 102, 204, 0.15);
}

.category-header {
    background: var(--enterprise-gradient);
    color: white;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.category-header img {
    height: 40px;
    width: auto;
    filter: brightness(0) invert(1);
}

.category-header h3 {
    margin: 0;
    font-size: 1.3rem;
    font-weight: 600;
}

.model-count {
    background: rgba(255, 255, 255, 0.2);
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
}

.system-models {
    padding: 1.5rem;
    display: grid;
    gap: 1rem;
}

.model-card {
    border: 1px solid #e9ecef;
    border-radius: var(--border-radius);
    padding: 1rem;
    transition: var(--transition);
}

.model-card:hover {
    border-color: var(--enterprise-primary);
    box-shadow: 0 2px 10px rgba(0, 102, 204, 0.1);
}

.model-card img {
    width: 100%;
    height: 120px;
    object-fit: cover;
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
}

.model-card h4 {
    margin: 0 0 0.5rem 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--enterprise-dark);
}

.model-card p {
    margin: 0 0 1rem 0;
    color: #666;
    font-size: 0.9rem;
}

.model-features {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.feature-tag {
    background: var(--enterprise-light);
    color: var(--enterprise-dark);
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
}

.systems-cta {
    text-align: center;
    display: flex;
    justify-content: center;
    gap: 1rem;
}

/* Learning Paths */
.learning-paths {
    margin-bottom: 2rem;
}

.path-selector {
    display: flex;
    justify-content: center;
    gap: 1rem;
    background: white;
    padding: 1rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-enterprise);
}

.path-btn {
    padding: 0.75rem 1.5rem;
    border: 2px solid transparent;
    background: transparent;
    color: #666;
    border-radius: var(--border-radius);
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
}

.path-btn.active,
.path-btn:hover {
    background: var(--enterprise-primary);
    color: white;
    border-color: var(--enterprise-primary);
}

/* Enterprise Modules */
.enterprise-modules {
    padding: 4rem 0;
    background: #f8f9fa;
}

.enterprise-modules .module-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

/* AI Diagnostics Section */
.ai-diagnostics {
    padding: 4rem 0;
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    color: white;
}

.ai-features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.ai-feature-card {
    background: rgba(255, 255, 255, 0.1);
    padding: 2rem;
    border-radius: var(--border-radius);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.ai-feature-card h3 {
    color: var(--enterprise-accent);
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.ai-feature-card i {
    font-size: 1.5rem;
}

/* Collaboration Tools */
.collaboration-section {
    padding: 4rem 0;
    background: white;
}

.collaboration-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.collaboration-card {
    text-align: center;
    padding: 2rem;
    border: 2px solid #e9ecef;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.collaboration-card:hover {
    border-color: var(--enterprise-primary);
    transform: translateY(-5px);
}

.collaboration-icon {
    width: 80px;
    height: 80px;
    background: var(--enterprise-gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    font-size: 2rem;
    color: white;
}

/* Analytics Dashboard Preview */
.analytics-preview {
    padding: 4rem 0;
    background: #f8f9fa;
}

.dashboard-mockup {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-enterprise);
    padding: 2rem;
    margin-top: 2rem;
}

.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e9ecef;
}

.dashboard-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.dashboard-stat {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: var(--border-radius);
    text-align: center;
}

.dashboard-stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: var(--enterprise-primary);
}

.dashboard-stat-label {
    color: #666;
    font-size: 0.9rem;
    margin-top: 0.5rem;
}

.chart-placeholder {
    height: 300px;
    background: #f8f9fa;
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #666;
    font-size: 1.1rem;
}

/* Competition Highlights */
.competition-highlights {
    padding: 4rem 0;
    background: var(--enterprise-gradient);
    color: white;
    text-align: center;
}

.highlights-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.highlight-item {
    padding: 2rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius);
    backdrop-filter: blur(10px);
}

.highlight-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: var(--enterprise-accent);
}

.highlight-number {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.highlight-text {
    font-size: 1.1rem;
    opacity: 0.9;
}

/* Call to Action Section */
.enterprise-cta {
    padding: 4rem 0;
    background: white;
    text-align: center;
}

.cta-content {
    max-width: 600px;
    margin: 0 auto;
}

.cta-buttons {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-top: 2rem;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .systems-grid {
        grid-template-columns: 1fr;
    }
    
    .path-selector {
        flex-wrap: wrap;
    }
    
    .hero-stats {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .highlights-grid {
        grid-template-columns: 1fr;
    }
    
    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .dashboard-stats {
        grid-template-columns: 1fr;
    }
}

/* Animation Classes */
.fade-in {
    opacity: 0;
    transform: translateY(20px);
    animation: fadeInUp 0.6s ease forwards;
}

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-in-left {
    opacity: 0;
    transform: translateX(-50px);
    animation: slideInLeft 0.6s ease forwards;
}

@keyframes slideInLeft {
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.scale-in {
    opacity: 0;
    transform: scale(0.8);
    animation: scaleIn 0.6s ease forwards;
}

@keyframes scaleIn {
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* Loading States */
.loading-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* Demo Presentation Styles */
.demo-trigger {
    animation: pulse 2s infinite;
    box-shadow: 0 0 20px rgba(0, 102, 204, 0.5);
}

@keyframes pulse {
    0% {
        transform: scale(1);
        box-shadow: 0 0 20px rgba(0, 102, 204, 0.5);
    }
    50% {
        transform: scale(1.05);
        box-shadow: 0 0 30px rgba(0, 102, 204, 0.8);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 0 20px rgba(0, 102, 204, 0.5);
    }
}

.demo-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.95);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.demo-container {
    width: 90%;
    max-width: 1200px;
    height: 90%;
    background: white;
    border-radius: var(--border-radius);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.demo-header {
    background: var(--enterprise-gradient);
    color: white;
    padding: 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.demo-progress {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.demo-content {
    flex: 1;
    padding: 2rem;
    overflow-y: auto;
}

.demo-controls {
    padding: 1.5rem;
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.demo-slide {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

/* Slide-specific styles */
.welcome-slide {
    text-align: center;
}

.slide-hero h1 {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: var(--enterprise-primary);
}

.slide-subtitle {
    font-size: 1.5rem;
    color: #666;
    margin-bottom: 2rem;
}

.welcome-features {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin: 2rem 0;
}

.slide-stats {
    display: flex;
    justify-content: center;
    gap: 3rem;
    margin-top: 3rem;
}

.vendor-comparison {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    margin: 2rem 0;
}

.vendor-column {
    padding: 2rem;
    border-radius: var(--border-radius);
    text-align: center;
}

.vendor-column:first-child {
    background: #fff5f5;
    border: 2px solid #fed7d7;
}

.vendor-column:last-child {
    background: #f0fff4;
    border: 2px solid #9ae6b4;
}

.limitation-item,
.advantage-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin: 1rem 0;
    padding: 0.5rem;
}

.text-danger {
    color: #dc3545;
}

.text-success {
    color: #28a745;
}

.vendor-showcase {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin-top: 2rem;
}

.vendor-item {
    padding: 1rem;
    border: 2px solid #e9ecef;
    border-radius: var(--border-radius);
    text-align: center;
    cursor: pointer;
    transition: var(--transition);
}

.vendor-item.active {
    border-color: var(--enterprise-primary);
    background: var(--enterprise-light);
}

/* Modal Styles */
.preview-modal,
.system-details-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    z-index: 10001;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: white;
    border-radius: var(--border-radius);
    max-width: 600px;
    width: 90%;
    max-height: 80%;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.modal-content.large {
    max-width: 900px;
}

.modal-header {
    background: var(--enterprise-gradient);
    color: white;
    padding: 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-close {
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
}

.modal-body {
    padding: 2rem;
    overflow-y: auto;
    flex: 1;
}

.modal-footer {
    padding: 1.5rem;
    border-top: 1px solid #e9ecef;
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
}

/* Toast Notifications */
.language-loading-toast,
.language-success-toast {
    position: fixed;
    top: 20px;
    right: 20px;
    background: white;
    padding: 1rem 1.5rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-enterprise);
    z-index: 10002;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.language-success-toast {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}
