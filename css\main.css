/* Main CSS for X-ray System Interactive Training Module */

:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --accent-color: #e74c3c;
    --light-color: #ecf0f1;
    --dark-color: #2c3e50;
    --text-color: #333;
    --light-text: #f8f9fa;
    --border-color: #ddd;
    --shadow: 0 2px 15px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    background-color: #f8f9fa;
}

.container {
    width: 90%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    margin-bottom: 0.5em;
    font-weight: 600;
    line-height: 1.3;
}

h1 {
    font-size: 2.5rem;
}

h2 {
    font-size: 2rem;
    margin-bottom: 1rem;
}

h3 {
    font-size: 1.5rem;
}

p {
    margin-bottom: 1rem;
}

a {
    color: var(--secondary-color);
    text-decoration: none;
    transition: var(--transition);
}

a:hover {
    color: var(--primary-color);
}

/* Buttons */
.btn {
    display: inline-block;
    padding: 0.6rem 1.2rem;
    background-color: var(--secondary-color);
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: var(--transition);
    text-align: center;
    font-weight: 500;
}

.btn:hover {
    background-color: #2980b9;
    color: white;
    transform: translateY(-2px);
}

.btn-primary {
    background-color: var(--primary-color);
    padding: 0.8rem 1.5rem;
    font-size: 1.1rem;
}

.btn-primary:hover {
    background-color: #1a252f;
}

.btn-small {
    padding: 0.4rem 0.8rem;
    font-size: 0.9rem;
}

/* Header */
header {
    background-color: var(--primary-color);
    color: var(--light-text);
    padding: 2rem 0;
    text-align: center;
}

header p {
    font-size: 1.2rem;
    opacity: 0.9;
}

/* Navigation */
nav {
    background-color: white;
    box-shadow: var(--shadow);
    position: sticky;
    top: 0;
    z-index: 100;
}

nav .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

nav ul {
    display: flex;
    list-style: none;
    justify-content: center;
}

nav ul li {
    margin: 0;
}

nav ul li a {
    display: block;
    padding: 1rem 1.5rem;
    color: var(--dark-color);
    font-weight: 500;
    transition: var(--transition);
}

nav ul li a:hover, nav ul li a.active {
    color: var(--secondary-color);
    background-color: rgba(52, 152, 219, 0.1);
}

/* User Profile */
.user-profile {
    display: flex;
    align-items: center;
}

.user-profile-logged-in {
    display: flex;
    align-items: center;
    gap: 1rem;
    color: var(--dark-color);
}

.user-info {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
}

.username {
    font-weight: 600;
    font-size: 0.9rem;
}

.role-badge {
    font-size: 0.7rem;
    background-color: var(--secondary-color);
    color: white;
    padding: 0.1rem 0.5rem;
    border-radius: 10px;
    margin-top: 0.2rem;
}

.user-actions {
    display: flex;
    gap: 0.5rem;
}

/* Hero Section */
.hero {
    padding: 4rem 0;
    background-color: #f8f9fa;
}

.hero .container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 2rem;
}

.hero-content {
    flex: 1;
}

.hero-image {
    flex: 1;
    text-align: center;
}

.hero-image img {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    box-shadow: var(--shadow);
}

.hero h2 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: var(--primary-color);
}

.hero p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
}

/* About Section */
.about {
    padding: 4rem 0;
    background-color: white;
}

.about h2 {
    text-align: center;
    margin-bottom: 2rem;
}

.about p {
    text-align: center;
    max-width: 800px;
    margin: 0 auto 3rem;
    font-size: 1.1rem;
}

.features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.feature {
    text-align: center;
    padding: 1.5rem;
    border-radius: 8px;
    background-color: #f8f9fa;
    box-shadow: var(--shadow);
    transition: var(--transition);
}

.feature:hover {
    transform: translateY(-5px);
}

.feature i {
    font-size: 2.5rem;
    color: var(--secondary-color);
    margin-bottom: 1rem;
}

/* Modules Section */
.modules {
    padding: 4rem 0;
    background-color: #f8f9fa;
}

.modules h2 {
    text-align: center;
    margin-bottom: 2rem;
}

.module-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.module-card {
    background-color: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: var(--shadow);
    transition: var(--transition);
    padding: 2rem;
    display: flex;
    flex-direction: column;
}

.module-card:hover {
    transform: translateY(-5px);
}

.module-icon {
    font-size: 2.5rem;
    color: var(--secondary-color);
    margin-bottom: 1rem;
    text-align: center;
}

.module-card h3 {
    margin-bottom: 1rem;
    color: var(--primary-color);
}

.module-card p {
    margin-bottom: 1.5rem;
    flex-grow: 1;
}

.module-card .btn {
    align-self: flex-start;
}

/* Premium Content Section */
.premium-content-section {
    margin-top: 3rem;
    background-color: white;
    border-radius: 8px;
    padding: 2rem;
    box-shadow: var(--shadow);
}

.premium-content-section h3 {
    display: flex;
    align-items: center;
    margin-bottom: 1.5rem;
}

.content-access-badge {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    margin-left: 1rem;
}

.access-level-basic {
    background-color: #e9f7fe;
    color: #3498db;
    border: 1px solid #3498db;
}

.access-level-advanced {
    background-color: #fef5e9;
    color: #e67e22;
    border: 1px solid #e67e22;
}

.access-level-premium {
    background-color: #f9e9f9;
    color: #9b59b6;
    border: 1px solid #9b59b6;
}

/* Resources Section */
.resources {
    padding: 4rem 0;
    background-color: white;
}

.resources h2 {
    text-align: center;
    margin-bottom: 2rem;
}

.resource-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.resource-item {
    padding: 1.5rem;
    border-radius: 8px;
    background-color: #f8f9fa;
    box-shadow: var(--shadow);
    transition: var(--transition);
}

.resource-item:hover {
    transform: translateY(-5px);
}

.resource-item i {
    font-size: 2rem;
    color: var(--secondary-color);
    margin-bottom: 1rem;
}

/* Footer */
footer {
    background-color: var(--dark-color);
    color: var(--light-text);
    padding: 3rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3 {
    margin-bottom: 1rem;
    color: var(--light-text);
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    color: var(--light-text);
    opacity: 0.8;
}

.footer-section ul li a:hover {
    opacity: 1;
}

.footer-bottom {
    text-align: center;
    padding-top: 1rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* Responsive Design */
@media (max-width: 768px) {
    h1 {
        font-size: 2rem;
    }

    h2 {
        font-size: 1.8rem;
    }

    .hero .container {
        flex-direction: column;
    }

    .hero-content, .hero-image {
        flex: none;
        width: 100%;
    }

    nav .container {
        flex-direction: column;
        padding: 0.5rem 0;
    }

    nav ul {
        flex-wrap: wrap;
        justify-content: center;
        margin-bottom: 0.5rem;
    }

    .user-profile {
        margin: 0.5rem 0;
        justify-content: center;
    }

    .module-grid, .features, .resource-list {
        grid-template-columns: 1fr;
    }

    /* Premium content section responsive */
    .premium-content-section {
        padding: 1rem;
    }
}
