/**
 * VirtualX Pro - Training Modules Manager
 * Comprehensive learning management system for X-ray training
 */

class TrainingModulesManager {
    constructor() {
        this.currentPath = 'foundation';
        this.moduleData = {};
        this.userProgress = {};
        this.init();
    }

    init() {
        this.loadModuleData();
        this.loadUserProgress();
        this.setupEventListeners();
        this.updateModuleDisplay();
        this.initializeProgressTracking();
    }

    setupEventListeners() {
        // Learning path selection
        document.querySelectorAll('.path-card').forEach(card => {
            card.addEventListener('click', (e) => {
                const path = e.currentTarget.dataset.path;
                this.selectPath(path);
            });
        });

        // Module interactions
        document.addEventListener('click', (e) => {
            if (e.target.closest('.btn-start')) {
                const moduleId = e.target.closest('.btn-start').onclick?.toString().match(/'([^']+)'/)?.[1];
                if (moduleId) this.startModule(moduleId);
            }
            
            if (e.target.closest('.btn-preview')) {
                const moduleId = e.target.closest('.btn-preview').onclick?.toString().match(/'([^']+)'/)?.[1];
                if (moduleId) this.previewModule(moduleId);
            }
        });
    }

    loadModuleData() {
        this.moduleData = {
            'components': {
                id: 'components',
                title: 'X-ray System Components',
                subtitle: 'Interactive 3D Component Exploration',
                path: 'foundation',
                difficulty: 'beginner',
                duration: '4 hours',
                icon: 'fas fa-sitemap',
                description: 'Master the fundamental components of X-ray systems through immersive 3D exploration and hands-on learning.',
                objectives: [
                    'Identify all major X-ray system components',
                    'Understand component functions and interactions',
                    'Practice virtual assembly procedures',
                    'Compare multi-vendor system designs'
                ],
                prerequisites: [],
                features: ['3D Interactive', 'Multi-vendor', 'Virtual Assembly', 'Component Quiz'],
                content: {
                    lessons: [
                        {
                            title: 'X-ray Tube Fundamentals',
                            duration: '45 min',
                            type: '3D Interactive',
                            description: 'Explore the X-ray tube structure and function'
                        },
                        {
                            title: 'Generator Systems',
                            duration: '60 min',
                            type: 'Simulation',
                            description: 'Understand high-frequency generator operation'
                        },
                        {
                            title: 'Detector Technologies',
                            duration: '50 min',
                            type: '3D Interactive',
                            description: 'Compare different detector types and technologies'
                        },
                        {
                            title: 'System Integration',
                            duration: '45 min',
                            type: 'Virtual Lab',
                            description: 'Practice complete system assembly'
                        }
                    ],
                    assessments: [
                        {
                            type: 'Component Identification Quiz',
                            questions: 25,
                            passingScore: 80
                        },
                        {
                            type: 'Virtual Assembly Test',
                            tasks: 5,
                            passingScore: 85
                        }
                    ]
                }
            },
            'ai-diagnostics': {
                id: 'ai-diagnostics',
                title: 'AI-Powered Diagnostics',
                subtitle: 'Intelligent Fault Detection and Analysis',
                path: 'intermediate',
                difficulty: 'intermediate',
                duration: '6 hours',
                icon: 'fas fa-brain',
                description: 'Learn to leverage AI diagnostic tools for efficient fault detection and system troubleshooting.',
                objectives: [
                    'Master AI diagnostic tools and interfaces',
                    'Interpret AI analysis results accurately',
                    'Combine AI insights with manual inspection',
                    'Validate AI recommendations in practice'
                ],
                prerequisites: ['components'],
                features: ['AI Simulation', 'Real Cases', 'Pattern Recognition', 'Decision Trees'],
                content: {
                    lessons: [
                        {
                            title: 'AI Diagnostic Fundamentals',
                            duration: '60 min',
                            type: 'Theory + Practice',
                            description: 'Understanding machine learning in medical equipment'
                        },
                        {
                            title: 'Symptom Analysis Techniques',
                            duration: '90 min',
                            type: 'AI Simulation',
                            description: 'Practice with real-world diagnostic scenarios'
                        },
                        {
                            title: 'Confidence Level Interpretation',
                            duration: '45 min',
                            type: 'Case Studies',
                            description: 'Learn when to trust AI recommendations'
                        },
                        {
                            title: 'Human-AI Collaboration',
                            duration: '75 min',
                            type: 'Interactive Lab',
                            description: 'Optimize the combination of AI and human expertise'
                        }
                    ],
                    assessments: [
                        {
                            type: 'Diagnostic Accuracy Test',
                            scenarios: 15,
                            passingScore: 85
                        },
                        {
                            type: 'AI Tool Proficiency',
                            tasks: 10,
                            passingScore: 90
                        }
                    ]
                }
            },
            'optimization': {
                id: 'optimization',
                title: 'System Optimization',
                subtitle: 'Performance Tuning and Advanced Configuration',
                path: 'advanced',
                difficulty: 'advanced',
                duration: '8 hours',
                icon: 'fas fa-cogs',
                description: 'Advanced techniques for optimizing X-ray system performance and implementing custom configurations.',
                objectives: [
                    'Optimize system performance parameters',
                    'Implement advanced calibration procedures',
                    'Configure custom imaging protocols',
                    'Analyze system performance metrics'
                ],
                prerequisites: ['components', 'ai-diagnostics'],
                features: ['Performance Lab', 'Custom Protocols', 'Advanced Calibration', 'Metrics Analysis'],
                content: {
                    lessons: [
                        {
                            title: 'Performance Metrics Analysis',
                            duration: '90 min',
                            type: 'Data Analysis',
                            description: 'Understanding key performance indicators'
                        },
                        {
                            title: 'Advanced Calibration Techniques',
                            duration: '120 min',
                            type: 'Virtual Lab',
                            description: 'Precision calibration procedures'
                        },
                        {
                            title: 'Custom Protocol Development',
                            duration: '100 min',
                            type: 'Configuration Lab',
                            description: 'Creating optimized imaging protocols'
                        },
                        {
                            title: 'System Validation Testing',
                            duration: '90 min',
                            type: 'Testing Lab',
                            description: 'Comprehensive system validation procedures'
                        }
                    ],
                    assessments: [
                        {
                            type: 'Optimization Challenge',
                            scenarios: 8,
                            passingScore: 88
                        },
                        {
                            type: 'Protocol Design Test',
                            tasks: 6,
                            passingScore: 85
                        }
                    ]
                }
            }
        };
    }

    loadUserProgress() {
        this.userProgress = JSON.parse(localStorage.getItem('virtualx-training-progress') || '{}');
        
        // Initialize default progress if not exists
        if (!this.userProgress.modules) {
            this.userProgress = {
                modules: {
                    'components': { progress: 75, completed: false, score: 0 },
                    'ai-diagnostics': { progress: 0, completed: false, score: 0 },
                    'optimization': { progress: 0, completed: false, score: 0 }
                },
                currentPath: 'foundation',
                totalStudyTime: 45,
                certificatesEarned: 3,
                averageScore: 87,
                retentionRate: 95,
                streakDays: 7
            };
            this.saveProgress();
        }
    }

    saveProgress() {
        localStorage.setItem('virtualx-training-progress', JSON.stringify(this.userProgress));
    }

    selectPath(pathName) {
        this.currentPath = pathName;
        
        // Update active path card
        document.querySelectorAll('.path-card').forEach(card => {
            card.classList.remove('active');
        });
        document.querySelector(`[data-path="${pathName}"]`).classList.add('active');
        
        // Update modules display
        this.updateModuleDisplay();
        
        // Update recommended path if needed
        this.updateRecommendedPath(pathName);
    }

    updateModuleDisplay() {
        const modulesGrid = document.getElementById('modules-grid');
        const moduleCards = modulesGrid.querySelectorAll('.module-card');
        
        moduleCards.forEach(card => {
            const cardPath = card.dataset.path;
            if (this.currentPath === 'all' || cardPath === this.currentPath) {
                card.style.display = 'block';
                card.classList.add('animate__animated', 'animate__fadeInUp');
            } else {
                card.style.display = 'none';
            }
        });
    }

    startModule(moduleId) {
        const moduleInfo = this.moduleData[moduleId];
        if (!moduleInfo) {
            console.error('Module not found:', moduleId);
            return;
        }

        // Check prerequisites
        if (!this.checkPrerequisites(moduleInfo.prerequisites)) {
            this.showPrerequisiteWarning(moduleInfo);
            return;
        }

        // Update progress
        if (!this.userProgress.modules[moduleId]) {
            this.userProgress.modules[moduleId] = { progress: 0, completed: false, score: 0 };
        }

        // Simulate starting the module
        this.showModuleStarted(moduleInfo);
        
        // Update progress (simulate some progress)
        this.userProgress.modules[moduleId].progress = Math.min(
            this.userProgress.modules[moduleId].progress + 10, 
            100
        );
        this.saveProgress();
        this.updateProgressDisplay();
    }

    previewModule(moduleId) {
        const moduleInfo = this.moduleData[moduleId];
        if (!moduleInfo) {
            console.error('Module not found:', moduleId);
            return;
        }

        this.showModulePreview(moduleInfo);
    }

    showModulePreview(moduleInfo) {
        const modal = document.getElementById('module-modal');
        const modalTitle = document.getElementById('modal-title');
        const modalBody = document.getElementById('modal-body');

        modalTitle.textContent = `${moduleInfo.title} - Preview`;

        let contentHtml = `
            <div style="margin-bottom: 2rem;">
                <h4 style="color: var(--enterprise-primary); margin-bottom: 1rem;">Module Overview</h4>
                <p>${moduleInfo.description}</p>
            </div>

            <div style="margin-bottom: 2rem;">
                <h4 style="color: var(--enterprise-primary); margin-bottom: 1rem;">Learning Objectives</h4>
                <ul style="list-style: none; padding: 0;">
        `;

        moduleInfo.objectives.forEach(objective => {
            contentHtml += `
                <li style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 0.5rem;">
                    <i class="fas fa-check" style="color: var(--enterprise-success);"></i>
                    <span>${objective}</span>
                </li>
            `;
        });

        contentHtml += `
                </ul>
            </div>

            <div style="margin-bottom: 2rem;">
                <h4 style="color: var(--enterprise-primary); margin-bottom: 1rem;">Course Content</h4>
                <div style="background: #f8f9fa; padding: 1.5rem; border-radius: 8px;">
        `;

        moduleInfo.content.lessons.forEach((lesson, index) => {
            contentHtml += `
                <div style="display: flex; justify-content: space-between; align-items: center; padding: 1rem 0; border-bottom: 1px solid #eee;">
                    <div>
                        <strong>${index + 1}. ${lesson.title}</strong>
                        <div style="font-size: 0.9rem; color: #666; margin-top: 0.25rem;">
                            ${lesson.description}
                        </div>
                    </div>
                    <div style="text-align: right;">
                        <div style="font-size: 0.9rem; color: var(--enterprise-primary); font-weight: 600;">
                            ${lesson.duration}
                        </div>
                        <div style="font-size: 0.8rem; color: #666;">
                            ${lesson.type}
                        </div>
                    </div>
                </div>
            `;
        });

        contentHtml += `
                </div>
            </div>

            <div style="margin-bottom: 2rem;">
                <h4 style="color: var(--enterprise-primary); margin-bottom: 1rem;">Assessments</h4>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1rem;">
        `;

        moduleInfo.content.assessments.forEach(assessment => {
            contentHtml += `
                <div style="background: #f8f9fa; padding: 1rem; border-radius: 8px;">
                    <strong>${assessment.type}</strong>
                    <div style="font-size: 0.9rem; color: #666; margin-top: 0.5rem;">
                        ${assessment.questions || assessment.tasks || assessment.scenarios} items
                    </div>
                    <div style="font-size: 0.9rem; color: var(--enterprise-primary); margin-top: 0.25rem;">
                        Passing Score: ${assessment.passingScore}%
                    </div>
                </div>
            `;
        });

        contentHtml += `
                </div>
            </div>

            <div style="text-align: center; padding: 2rem 0; border-top: 1px solid #eee;">
                <button type="button" class="btn-start" onclick="startModule('${moduleInfo.id}')" style="padding: 1rem 2rem; margin-right: 1rem;">
                    <i class="fas fa-play"></i>
                    Start This Module
                </button>
                <button type="button" class="btn-preview" onclick="closeModal()" style="padding: 1rem 2rem;">
                    <i class="fas fa-times"></i>
                    Close Preview
                </button>
            </div>
        `;

        modalBody.innerHTML = contentHtml;
        modal.style.display = 'flex';
    }

    showModuleStarted(moduleInfo) {
        // Create a success notification
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--enterprise-success);
            color: white;
            padding: 1rem 1.5rem;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
            z-index: 10001;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            animation: slideInRight 0.3s ease;
        `;
        
        notification.innerHTML = `
            <i class="fas fa-check-circle"></i>
            <span>Starting "${moduleInfo.title}" module...</span>
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.remove();
        }, 3000);
    }

    checkPrerequisites(prerequisites) {
        if (!prerequisites || prerequisites.length === 0) {
            return true;
        }

        return prerequisites.every(prereq => {
            const progress = this.userProgress.modules[prereq];
            return progress && progress.completed;
        });
    }

    showPrerequisiteWarning(moduleInfo) {
        const warning = document.createElement('div');
        warning.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--enterprise-warning);
            color: white;
            padding: 1rem 1.5rem;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
            z-index: 10001;
            max-width: 300px;
        `;
        
        warning.innerHTML = `
            <div style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 0.5rem;">
                <i class="fas fa-exclamation-triangle"></i>
                <strong>Prerequisites Required</strong>
            </div>
            <div style="font-size: 0.9rem;">
                Complete these modules first: ${moduleInfo.prerequisites.join(', ')}
            </div>
        `;
        
        document.body.appendChild(warning);
        
        setTimeout(() => {
            warning.remove();
        }, 5000);
    }

    updateProgressDisplay() {
        // Update progress bars in module cards
        Object.keys(this.userProgress.modules).forEach(moduleId => {
            const progress = this.userProgress.modules[moduleId];
            const moduleCard = document.querySelector(`[data-module="${moduleId}"]`);
            
            if (moduleCard) {
                const progressBar = moduleCard.querySelector('.progress-fill');
                const progressText = moduleCard.querySelector('.progress-percentage');
                
                if (progressBar) {
                    progressBar.style.width = `${progress.progress}%`;
                }
                if (progressText) {
                    progressText.textContent = `${progress.progress}%`;
                }
            }
        });

        // Update analytics
        this.updateAnalytics();
    }

    updateAnalytics() {
        const analytics = document.querySelectorAll('.analytics-number');
        if (analytics.length >= 6) {
            analytics[0].textContent = Object.keys(this.userProgress.modules).filter(
                id => this.userProgress.modules[id].completed
            ).length;
            analytics[1].textContent = `${this.userProgress.averageScore}%`;
            analytics[2].textContent = `${this.userProgress.totalStudyTime}h`;
            analytics[3].textContent = this.userProgress.certificatesEarned;
            analytics[4].textContent = `${this.userProgress.retentionRate}%`;
            analytics[5].textContent = this.userProgress.streakDays;
        }
    }

    updateRecommendedPath(pathName) {
        const recommendedSection = document.querySelector('.recommended-path');
        if (recommendedSection) {
            const pathNames = {
                'foundation': 'Foundation Level',
                'intermediate': 'Intermediate Level',
                'advanced': 'Advanced Level',
                'specialist': 'Specialist Level'
            };
            
            const pathDescription = recommendedSection.querySelector('p');
            pathDescription.innerHTML = `Based on your profile and progress, we recommend the <strong>${pathNames[pathName]}</strong> path to continue your learning journey.`;
        }
    }

    initializeProgressTracking() {
        // Set up periodic progress updates
        setInterval(() => {
            this.updateProgressDisplay();
        }, 30000); // Update every 30 seconds

        // Initialize analytics display
        this.updateAnalytics();
    }
}

// Global functions for HTML onclick handlers
function selectPath(pathName) {
    if (window.trainingModulesManager) {
        window.trainingModulesManager.selectPath(pathName);
    }
}

function startModule(moduleId) {
    if (window.trainingModulesManager) {
        window.trainingModulesManager.startModule(moduleId);
    }
}

function previewModule(moduleId) {
    if (window.trainingModulesManager) {
        window.trainingModulesManager.previewModule(moduleId);
    }
}

function closeModal() {
    const modal = document.getElementById('module-modal');
    if (modal) {
        modal.style.display = 'none';
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.trainingModulesManager = new TrainingModulesManager();
    
    // Add animation styles
    const animationStyle = document.createElement('style');
    animationStyle.textContent = `
        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }
        
        .nav-brand {
            display: flex;
            align-items: center;
            gap: 1rem;
            text-decoration: none;
        }
        
        .nav-logo {
            height: 40px;
            width: auto;
        }
        
        .brand-text {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--enterprise-primary);
        }
        
        .nav-brand:hover .brand-text {
            color: var(--enterprise-secondary);
        }
    `;
    document.head.appendChild(animationStyle);
});
