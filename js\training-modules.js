/**
 * VirtualX Pro - Training Modules Manager
 * Comprehensive learning management system for X-ray training
 */

class TrainingModulesManager {
    constructor() {
        this.currentPath = 'foundation';
        this.moduleData = {};
        this.userProgress = {};
        this.init();
    }

    init() {
        this.loadModuleData();
        this.loadUserProgress();
        this.setupEventListeners();
        this.updateModuleDisplay();
        this.initializeProgressTracking();
    }

    setupEventListeners() {
        // Learning path selection
        document.querySelectorAll('.path-card').forEach(card => {
            card.addEventListener('click', (e) => {
                const path = e.currentTarget.dataset.path;
                this.selectPath(path);
            });
        });

        // Module interactions
        document.addEventListener('click', (e) => {
            if (e.target.closest('.btn-start')) {
                const moduleId = e.target.closest('.btn-start').onclick?.toString().match(/'([^']+)'/)?.[1];
                if (moduleId) this.startModule(moduleId);
            }
            
            if (e.target.closest('.btn-preview')) {
                const moduleId = e.target.closest('.btn-preview').onclick?.toString().match(/'([^']+)'/)?.[1];
                if (moduleId) this.previewModule(moduleId);
            }
        });
    }

    loadModuleData() {
        this.moduleData = {
            'physics-fundamentals': {
                id: 'physics-fundamentals',
                title: 'X-ray Physics Fundamentals',
                subtitle: 'Interactive Physics Simulation & Theory',
                path: 'foundation',
                difficulty: 'beginner',
                duration: '6 hours',
                icon: 'fas fa-atom',
                description: 'Master the fundamental physics principles of X-ray production, interaction, and detection through interactive simulations and real-world applications.',
                objectives: [
                    'Master X-ray production mechanisms (Bremsstrahlung & Characteristic)',
                    'Calculate beam attenuation using Beer-Lambert law',
                    'Understand photoelectric effect and Compton scattering',
                    'Apply radiation safety principles (ALARA, shielding calculations)'
                ],
                prerequisites: [],
                features: ['Physics Simulation', 'Interactive Calculators', 'Real-time Analysis', 'Safety Training'],
                technicalContent: {
                    visualAids: [
                        '3D Electron-Target Interaction Simulator',
                        'Real-time Spectrum Analysis Tool',
                        'HVL & Filtration Calculator',
                        'Radiation Shielding Designer'
                    ],
                    realEnvironment: true,
                    certificationLevel: 'IEC 60601 Compliant'
                },
                content: {
                    lessons: [
                        {
                            title: 'X-ray Production Physics',
                            duration: '90 min',
                            type: 'Interactive Simulation',
                            description: 'Explore electron-target interactions and X-ray spectrum generation'
                        },
                        {
                            title: 'Beam Attenuation & Filtration',
                            duration: '75 min',
                            type: 'Virtual Laboratory',
                            description: 'Calculate HVL and optimize beam filtration'
                        },
                        {
                            title: 'Radiation Interactions',
                            duration: '60 min',
                            type: 'Physics Simulator',
                            description: 'Understand photoelectric, Compton, and coherent scattering'
                        },
                        {
                            title: 'Radiation Safety & Shielding',
                            duration: '75 min',
                            type: 'Safety Calculator',
                            description: 'Design radiation shielding and apply ALARA principles'
                        }
                    ],
                    assessments: [
                        {
                            type: 'Physics Calculation Test',
                            questions: 30,
                            passingScore: 85
                        },
                        {
                            type: 'Safety Protocol Assessment',
                            scenarios: 10,
                            passingScore: 90
                        }
                    ]
                }
            },
            'components': {
                id: 'components',
                title: 'System Components & Architecture',
                subtitle: 'Interactive 3D Component Exploration',
                path: 'foundation',
                difficulty: 'beginner',
                duration: '8 hours',
                icon: 'fas fa-sitemap',
                description: 'Master X-ray system components and architecture through immersive 3D exploration and hands-on learning with real equipment simulations.',
                objectives: [
                    'Identify all major X-ray system components and subsystems',
                    'Understand signal flow and control system architecture',
                    'Practice virtual assembly/disassembly procedures',
                    'Compare multi-vendor system designs and specifications'
                ],
                prerequisites: ['physics-fundamentals'],
                features: ['3D Interactive', 'Multi-vendor', 'Virtual Assembly', 'Component Quiz'],
                technicalContent: {
                    visualAids: [
                        'Photorealistic 3D System Models',
                        'Virtual Assembly Workbench',
                        'Interactive Block Diagrams',
                        'Component Identification Quiz'
                    ],
                    realEnvironment: true,
                    certificationLevel: 'Component Specialist'
                },
                content: {
                    lessons: [
                        {
                            title: 'X-ray Tube Fundamentals',
                            duration: '45 min',
                            type: '3D Interactive',
                            description: 'Explore the X-ray tube structure and function'
                        },
                        {
                            title: 'Generator Systems',
                            duration: '60 min',
                            type: 'Simulation',
                            description: 'Understand high-frequency generator operation'
                        },
                        {
                            title: 'Detector Technologies',
                            duration: '50 min',
                            type: '3D Interactive',
                            description: 'Compare different detector types and technologies'
                        },
                        {
                            title: 'System Integration',
                            duration: '45 min',
                            type: 'Virtual Lab',
                            description: 'Practice complete system assembly'
                        }
                    ],
                    assessments: [
                        {
                            type: 'Component Identification Quiz',
                            questions: 25,
                            passingScore: 80
                        },
                        {
                            type: 'Virtual Assembly Test',
                            tasks: 5,
                            passingScore: 85
                        }
                    ]
                }
            },
            'safety-radiation': {
                id: 'safety-radiation',
                title: 'Safety & Radiation Protection',
                subtitle: 'Comprehensive Safety Protocol Training',
                path: 'foundation',
                difficulty: 'beginner',
                duration: '5 hours',
                icon: 'fas fa-shield-alt',
                description: 'Comprehensive radiation safety training covering ALARA principles, regulatory compliance, and emergency procedures.',
                objectives: [
                    'Master ALARA principles and dose optimization techniques',
                    'Calculate shielding requirements per IEC 60601-2-43',
                    'Implement emergency procedures and incident response',
                    'Perform radiation surveys and compliance testing'
                ],
                prerequisites: [],
                features: ['Safety Simulation', 'Compliance Tools', 'Emergency Training', 'Regulatory Standards'],
                technicalContent: {
                    visualAids: [
                        'Radiation Field Visualizer',
                        'Dose Rate Calculator',
                        'Emergency Response Simulator',
                        'Compliance Checklist Tool'
                    ],
                    realEnvironment: true,
                    certificationLevel: 'Safety Officer Certification'
                },
                content: {
                    lessons: [
                        {
                            title: 'ALARA Principles & Implementation',
                            duration: '75 min',
                            type: 'Interactive Training',
                            description: 'Master As Low As Reasonably Achievable principles'
                        },
                        {
                            title: 'Shielding Design & Calculations',
                            duration: '90 min',
                            type: 'Engineering Calculator',
                            description: 'Design radiation shielding per IEC standards'
                        },
                        {
                            title: 'Emergency Response Procedures',
                            duration: '60 min',
                            type: 'Simulation Training',
                            description: 'Practice emergency scenarios and incident response'
                        },
                        {
                            title: 'Regulatory Compliance Testing',
                            duration: '75 min',
                            type: 'Compliance Suite',
                            description: 'Perform radiation surveys and documentation'
                        }
                    ],
                    assessments: [
                        {
                            type: 'Safety Certification Exam',
                            questions: 50,
                            passingScore: 90
                        },
                        {
                            type: 'Emergency Response Drill',
                            scenarios: 8,
                            passingScore: 95
                        }
                    ]
                }
            },
            'generator-systems': {
                id: 'generator-systems',
                title: 'Generator Systems & Power Electronics',
                subtitle: 'High-Frequency Generator Technology & Control',
                path: 'intermediate',
                difficulty: 'intermediate',
                duration: '12 hours',
                icon: 'fas fa-bolt',
                description: 'Advanced training on high-frequency generators, power electronics, and control systems with hands-on circuit analysis.',
                objectives: [
                    'Analyze IGBT switching circuits and high-frequency inverters',
                    'Troubleshoot generator control systems and feedback loops',
                    'Perform precision calibration using oscilloscope measurements',
                    'Implement power factor correction and harmonic analysis'
                ],
                prerequisites: ['physics-fundamentals', 'components'],
                features: ['Circuit Simulation', 'Oscilloscope Training', 'Power Analysis', 'Control Systems'],
                technicalContent: {
                    visualAids: [
                        'Virtual Oscilloscope & Signal Analysis',
                        'IGBT Circuit Simulator',
                        'Power Quality Analyzer',
                        'Control System Debugger'
                    ],
                    realEnvironment: true,
                    certificationLevel: 'Power Systems Certification'
                },
                content: {
                    lessons: [
                        {
                            title: 'High-Frequency Generator Principles',
                            duration: '120 min',
                            type: 'Circuit Analysis',
                            description: 'Understand IGBT switching and inverter operation'
                        },
                        {
                            title: 'Control Systems & Feedback',
                            duration: '150 min',
                            type: 'Control Lab',
                            description: 'Analyze PID controllers and feedback systems'
                        },
                        {
                            title: 'Power Quality & Harmonics',
                            duration: '90 min',
                            type: 'Power Analysis',
                            description: 'Measure and correct power factor and harmonics'
                        },
                        {
                            title: 'Calibration & Troubleshooting',
                            duration: '180 min',
                            type: 'Hands-on Lab',
                            description: 'Precision calibration and fault diagnosis'
                        }
                    ],
                    assessments: [
                        {
                            type: 'Circuit Analysis Test',
                            questions: 25,
                            passingScore: 85
                        },
                        {
                            type: 'Practical Calibration',
                            tasks: 8,
                            passingScore: 90
                        }
                    ]
                }
            },
            'ai-diagnostics': {
                id: 'ai-diagnostics',
                title: 'AI-Powered Diagnostics',
                subtitle: 'Machine Learning for Intelligent Fault Detection',
                path: 'intermediate',
                difficulty: 'intermediate',
                duration: '10 hours',
                icon: 'fas fa-brain',
                description: 'Master AI diagnostic algorithms and machine learning techniques for intelligent fault detection and predictive maintenance.',
                objectives: [
                    'Master AI diagnostic algorithms and pattern recognition',
                    'Interpret confidence levels and statistical analysis',
                    'Validate AI recommendations with manual verification',
                    'Train custom models for specific equipment types'
                ],
                prerequisites: ['generator-systems'],
                features: ['AI Simulation', 'Neural Networks', 'Pattern Recognition', 'Model Training'],
                technicalContent: {
                    visualAids: [
                        'Neural Network Visualizer',
                        '10,000+ Case Database',
                        'Statistical Analysis Tools',
                        'Model Training Laboratory'
                    ],
                    realEnvironment: true,
                    certificationLevel: 'AI Diagnostics Certification'
                },
                content: {
                    lessons: [
                        {
                            title: 'AI Diagnostic Fundamentals',
                            duration: '60 min',
                            type: 'Theory + Practice',
                            description: 'Understanding machine learning in medical equipment'
                        },
                        {
                            title: 'Symptom Analysis Techniques',
                            duration: '90 min',
                            type: 'AI Simulation',
                            description: 'Practice with real-world diagnostic scenarios'
                        },
                        {
                            title: 'Confidence Level Interpretation',
                            duration: '45 min',
                            type: 'Case Studies',
                            description: 'Learn when to trust AI recommendations'
                        },
                        {
                            title: 'Human-AI Collaboration',
                            duration: '75 min',
                            type: 'Interactive Lab',
                            description: 'Optimize the combination of AI and human expertise'
                        }
                    ],
                    assessments: [
                        {
                            type: 'Diagnostic Accuracy Test',
                            scenarios: 15,
                            passingScore: 85
                        },
                        {
                            type: 'AI Tool Proficiency',
                            tasks: 10,
                            passingScore: 90
                        }
                    ]
                }
            },
            'image-processing-qa': {
                id: 'image-processing-qa',
                title: 'Advanced Image Processing & QA',
                subtitle: 'Digital Image Enhancement & Quality Control',
                path: 'advanced',
                difficulty: 'advanced',
                duration: '15 hours',
                icon: 'fas fa-image',
                description: 'Master advanced image processing algorithms and comprehensive quality assurance testing procedures.',
                objectives: [
                    'Implement advanced image processing algorithms (FFT, convolution)',
                    'Perform comprehensive QA testing per IEC 61223-2-6',
                    'Optimize detector calibration and flat-field correction',
                    'Analyze MTF, DQE, and noise power spectrum measurements'
                ],
                prerequisites: ['ai-diagnostics'],
                features: ['Image Processing', 'QA Testing', 'MTF Analysis', 'DQE Measurement'],
                technicalContent: {
                    visualAids: [
                        'MTF Analysis Workstation',
                        'DQE Measurement Suite',
                        'Flat-Field Correction Tool',
                        'Noise Analysis Platform'
                    ],
                    realEnvironment: true,
                    certificationLevel: 'QA Specialist Certification'
                },
                content: {
                    lessons: [
                        {
                            title: 'Advanced Image Processing Algorithms',
                            duration: '180 min',
                            type: 'Algorithm Lab',
                            description: 'Implement FFT, convolution, and enhancement algorithms'
                        },
                        {
                            title: 'MTF & Spatial Resolution Analysis',
                            duration: '150 min',
                            type: 'QA Laboratory',
                            description: 'Measure and analyze modulation transfer function'
                        },
                        {
                            title: 'DQE & Detective Quantum Efficiency',
                            duration: '120 min',
                            type: 'Performance Testing',
                            description: 'Comprehensive detector performance evaluation'
                        },
                        {
                            title: 'Noise Analysis & Optimization',
                            duration: '180 min',
                            type: 'Signal Processing',
                            description: 'Analyze noise power spectrum and optimization'
                        }
                    ],
                    assessments: [
                        {
                            type: 'QA Comprehensive Exam',
                            questions: 40,
                            passingScore: 88
                        },
                        {
                            type: 'Practical QA Testing',
                            tasks: 12,
                            passingScore: 90
                        }
                    ]
                }
            },
            'system-integration': {
                id: 'system-integration',
                title: 'System Integration & Network Architecture',
                subtitle: 'DICOM, HL7, and Enterprise Integration',
                path: 'advanced',
                difficulty: 'advanced',
                duration: '18 hours',
                icon: 'fas fa-network-wired',
                description: 'Master enterprise-level system integration including DICOM, HL7, and secure network architectures.',
                objectives: [
                    'Configure DICOM services and workflow optimization',
                    'Implement HL7 interfaces and data exchange protocols',
                    'Design secure network architectures with VPN/firewall',
                    'Troubleshoot enterprise-level connectivity issues'
                ],
                prerequisites: ['image-processing-qa'],
                features: ['DICOM Configuration', 'HL7 Integration', 'Network Security', 'Enterprise Architecture'],
                technicalContent: {
                    visualAids: [
                        'DICOM Server Simulator',
                        'HL7 Message Builder',
                        'Network Security Tester',
                        'Architecture Design Tool'
                    ],
                    realEnvironment: true,
                    certificationLevel: 'Integration Expert Certification'
                },
                content: {
                    lessons: [
                        {
                            title: 'DICOM Configuration & Workflow',
                            duration: '240 min',
                            type: 'Integration Lab',
                            description: 'Configure DICOM services and optimize workflows'
                        },
                        {
                            title: 'HL7 Interface Development',
                            duration: '180 min',
                            type: 'Protocol Lab',
                            description: 'Implement HL7 v2.5.1 and FHIR R4 interfaces'
                        },
                        {
                            title: 'Network Security & Architecture',
                            duration: '150 min',
                            type: 'Security Lab',
                            description: 'Design secure enterprise network architectures'
                        },
                        {
                            title: 'Enterprise Troubleshooting',
                            duration: '210 min',
                            type: 'Problem Solving',
                            description: 'Advanced troubleshooting of integration issues'
                        }
                    ],
                    assessments: [
                        {
                            type: 'Integration Project',
                            scenarios: 6,
                            passingScore: 85
                        },
                        {
                            type: 'Security Assessment',
                            tasks: 10,
                            passingScore: 90
                        }
                    ]
                }
            },
            'certified-engineer': {
                id: 'certified-engineer',
                title: 'Certified Technical Engineer Program',
                subtitle: 'Professional Certification & Leadership Training',
                path: 'specialist',
                difficulty: 'expert',
                duration: '25 hours',
                icon: 'fas fa-crown',
                description: 'Comprehensive professional certification program for technical leadership and engineering excellence.',
                objectives: [
                    'Master all IEC 60601 series standards and compliance testing',
                    'Lead complex multi-system installations and commissioning',
                    'Develop custom training programs and technical documentation',
                    'Achieve professional engineer certification (PE/CEng equivalent)'
                ],
                prerequisites: ['system-integration'],
                features: ['Professional Certification', 'Leadership Training', 'Standards Mastery', 'Project Management'],
                technicalContent: {
                    visualAids: [
                        'Certification Exam Simulator',
                        'Compliance Testing Suite',
                        'Training Development Kit',
                        'Professional Portfolio Builder'
                    ],
                    realEnvironment: true,
                    certificationLevel: 'Professional Engineer Certification'
                },
                content: {
                    lessons: [
                        {
                            title: 'IEC 60601 Standards Mastery',
                            duration: '300 min',
                            type: 'Standards Training',
                            description: 'Complete mastery of all IEC 60601 series standards'
                        },
                        {
                            title: 'Project Leadership & Management',
                            duration: '240 min',
                            type: 'Leadership Lab',
                            description: 'Lead complex installation and commissioning projects'
                        },
                        {
                            title: 'Training Program Development',
                            duration: '180 min',
                            type: 'Curriculum Design',
                            description: 'Develop custom training programs and documentation'
                        },
                        {
                            title: 'Professional Certification Exam',
                            duration: '180 min',
                            type: 'Comprehensive Exam',
                            description: 'Final certification examination and portfolio review'
                        }
                    ],
                    assessments: [
                        {
                            type: 'Professional Engineer Exam',
                            questions: 100,
                            passingScore: 90
                        },
                        {
                            type: 'Capstone Project',
                            projects: 1,
                            passingScore: 95
                        }
                    ]
                }
            },
            'optimization': {
                id: 'optimization',
                title: 'System Optimization',
                subtitle: 'Performance Tuning and Advanced Configuration',
                path: 'advanced',
                difficulty: 'advanced',
                duration: '8 hours',
                icon: 'fas fa-cogs',
                description: 'Advanced techniques for optimizing X-ray system performance and implementing custom configurations.',
                objectives: [
                    'Optimize system performance parameters',
                    'Implement advanced calibration procedures',
                    'Configure custom imaging protocols',
                    'Analyze system performance metrics'
                ],
                prerequisites: ['components', 'ai-diagnostics'],
                features: ['Performance Lab', 'Custom Protocols', 'Advanced Calibration', 'Metrics Analysis'],
                content: {
                    lessons: [
                        {
                            title: 'Performance Metrics Analysis',
                            duration: '90 min',
                            type: 'Data Analysis',
                            description: 'Understanding key performance indicators'
                        },
                        {
                            title: 'Advanced Calibration Techniques',
                            duration: '120 min',
                            type: 'Virtual Lab',
                            description: 'Precision calibration procedures'
                        },
                        {
                            title: 'Custom Protocol Development',
                            duration: '100 min',
                            type: 'Configuration Lab',
                            description: 'Creating optimized imaging protocols'
                        },
                        {
                            title: 'System Validation Testing',
                            duration: '90 min',
                            type: 'Testing Lab',
                            description: 'Comprehensive system validation procedures'
                        }
                    ],
                    assessments: [
                        {
                            type: 'Optimization Challenge',
                            scenarios: 8,
                            passingScore: 88
                        },
                        {
                            type: 'Protocol Design Test',
                            tasks: 6,
                            passingScore: 85
                        }
                    ]
                }
            }
        };
    }

    loadUserProgress() {
        this.userProgress = JSON.parse(localStorage.getItem('virtualx-training-progress') || '{}');
        
        // Initialize default progress if not exists
        if (!this.userProgress.modules) {
            this.userProgress = {
                modules: {
                    'physics-fundamentals': { progress: 100, completed: true, score: 92 },
                    'components': { progress: 75, completed: false, score: 0 },
                    'safety-radiation': { progress: 45, completed: false, score: 0 },
                    'generator-systems': { progress: 0, completed: false, score: 0 },
                    'ai-diagnostics': { progress: 0, completed: false, score: 0 },
                    'image-processing-qa': { progress: 0, completed: false, score: 0 },
                    'system-integration': { progress: 0, completed: false, score: 0 },
                    'certified-engineer': { progress: 0, completed: false, score: 0 }
                },
                currentPath: 'foundation',
                totalStudyTime: 45,
                certificatesEarned: 3,
                averageScore: 87,
                retentionRate: 95,
                streakDays: 7,
                completedModules: 1,
                totalModules: 8
            };
            this.saveProgress();
        }
    }

    saveProgress() {
        localStorage.setItem('virtualx-training-progress', JSON.stringify(this.userProgress));
    }

    selectPath(pathName) {
        this.currentPath = pathName;
        
        // Update active path card
        document.querySelectorAll('.path-card').forEach(card => {
            card.classList.remove('active');
        });
        document.querySelector(`[data-path="${pathName}"]`).classList.add('active');
        
        // Update modules display
        this.updateModuleDisplay();
        
        // Update recommended path if needed
        this.updateRecommendedPath(pathName);
    }

    updateModuleDisplay() {
        const modulesGrid = document.getElementById('modules-grid');
        const moduleCards = modulesGrid.querySelectorAll('.module-card');
        
        moduleCards.forEach(card => {
            const cardPath = card.dataset.path;
            if (this.currentPath === 'all' || cardPath === this.currentPath) {
                card.style.display = 'block';
                card.classList.add('animate__animated', 'animate__fadeInUp');
            } else {
                card.style.display = 'none';
            }
        });
    }

    startModule(moduleId) {
        const moduleInfo = this.moduleData[moduleId];
        if (!moduleInfo) {
            console.error('Module not found:', moduleId);
            return;
        }

        // Check prerequisites
        if (!this.checkPrerequisites(moduleInfo.prerequisites)) {
            this.showPrerequisiteWarning(moduleInfo);
            return;
        }

        // Update progress
        if (!this.userProgress.modules[moduleId]) {
            this.userProgress.modules[moduleId] = { progress: 0, completed: false, score: 0 };
        }

        // Simulate starting the module
        this.showModuleStarted(moduleInfo);
        
        // Update progress (simulate some progress)
        this.userProgress.modules[moduleId].progress = Math.min(
            this.userProgress.modules[moduleId].progress + 10, 
            100
        );
        this.saveProgress();
        this.updateProgressDisplay();
    }

    previewModule(moduleId) {
        const moduleInfo = this.moduleData[moduleId];
        if (!moduleInfo) {
            console.error('Module not found:', moduleId);
            return;
        }

        this.showModulePreview(moduleInfo);
    }

    showModulePreview(moduleInfo) {
        const modal = document.getElementById('module-modal');
        const modalTitle = document.getElementById('modal-title');
        const modalBody = document.getElementById('modal-body');

        modalTitle.textContent = `${moduleInfo.title} - Preview`;

        let contentHtml = `
            <div style="margin-bottom: 2rem;">
                <h4 style="color: var(--enterprise-primary); margin-bottom: 1rem;">Module Overview</h4>
                <p>${moduleInfo.description}</p>
            </div>

            <div style="margin-bottom: 2rem;">
                <h4 style="color: var(--enterprise-primary); margin-bottom: 1rem;">Learning Objectives</h4>
                <ul style="list-style: none; padding: 0;">
        `;

        moduleInfo.objectives.forEach(objective => {
            contentHtml += `
                <li style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 0.5rem;">
                    <i class="fas fa-check" style="color: var(--enterprise-success);"></i>
                    <span>${objective}</span>
                </li>
            `;
        });

        contentHtml += `
                </ul>
            </div>

            <div style="margin-bottom: 2rem;">
                <h4 style="color: var(--enterprise-primary); margin-bottom: 1rem;">Course Content</h4>
                <div style="background: #f8f9fa; padding: 1.5rem; border-radius: 8px;">
        `;

        moduleInfo.content.lessons.forEach((lesson, index) => {
            contentHtml += `
                <div style="display: flex; justify-content: space-between; align-items: center; padding: 1rem 0; border-bottom: 1px solid #eee;">
                    <div>
                        <strong>${index + 1}. ${lesson.title}</strong>
                        <div style="font-size: 0.9rem; color: #666; margin-top: 0.25rem;">
                            ${lesson.description}
                        </div>
                    </div>
                    <div style="text-align: right;">
                        <div style="font-size: 0.9rem; color: var(--enterprise-primary); font-weight: 600;">
                            ${lesson.duration}
                        </div>
                        <div style="font-size: 0.8rem; color: #666;">
                            ${lesson.type}
                        </div>
                    </div>
                </div>
            `;
        });

        contentHtml += `
                </div>
            </div>

            <div style="margin-bottom: 2rem;">
                <h4 style="color: var(--enterprise-primary); margin-bottom: 1rem;">Assessments</h4>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1rem;">
        `;

        moduleInfo.content.assessments.forEach(assessment => {
            contentHtml += `
                <div style="background: #f8f9fa; padding: 1rem; border-radius: 8px;">
                    <strong>${assessment.type}</strong>
                    <div style="font-size: 0.9rem; color: #666; margin-top: 0.5rem;">
                        ${assessment.questions || assessment.tasks || assessment.scenarios} items
                    </div>
                    <div style="font-size: 0.9rem; color: var(--enterprise-primary); margin-top: 0.25rem;">
                        Passing Score: ${assessment.passingScore}%
                    </div>
                </div>
            `;
        });

        contentHtml += `
                </div>
            </div>

            <div style="text-align: center; padding: 2rem 0; border-top: 1px solid #eee;">
                <button type="button" class="btn-start" onclick="startModule('${moduleInfo.id}')" style="padding: 1rem 2rem; margin-right: 1rem;">
                    <i class="fas fa-play"></i>
                    Start This Module
                </button>
                <button type="button" class="btn-preview" onclick="closeModal()" style="padding: 1rem 2rem;">
                    <i class="fas fa-times"></i>
                    Close Preview
                </button>
            </div>
        `;

        modalBody.innerHTML = contentHtml;
        modal.style.display = 'flex';
    }

    showModuleStarted(moduleInfo) {
        // Create a success notification
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--enterprise-success);
            color: white;
            padding: 1rem 1.5rem;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
            z-index: 10001;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            animation: slideInRight 0.3s ease;
        `;
        
        notification.innerHTML = `
            <i class="fas fa-check-circle"></i>
            <span>Starting "${moduleInfo.title}" module...</span>
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.remove();
        }, 3000);
    }

    checkPrerequisites(prerequisites) {
        if (!prerequisites || prerequisites.length === 0) {
            return true;
        }

        return prerequisites.every(prereq => {
            const progress = this.userProgress.modules[prereq];
            return progress && progress.completed;
        });
    }

    showPrerequisiteWarning(moduleInfo) {
        const warning = document.createElement('div');
        warning.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--enterprise-warning);
            color: white;
            padding: 1rem 1.5rem;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
            z-index: 10001;
            max-width: 300px;
        `;
        
        warning.innerHTML = `
            <div style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 0.5rem;">
                <i class="fas fa-exclamation-triangle"></i>
                <strong>Prerequisites Required</strong>
            </div>
            <div style="font-size: 0.9rem;">
                Complete these modules first: ${moduleInfo.prerequisites.join(', ')}
            </div>
        `;
        
        document.body.appendChild(warning);
        
        setTimeout(() => {
            warning.remove();
        }, 5000);
    }

    updateProgressDisplay() {
        // Update progress bars in module cards
        Object.keys(this.userProgress.modules).forEach(moduleId => {
            const progress = this.userProgress.modules[moduleId];
            const moduleCard = document.querySelector(`[data-module="${moduleId}"]`);
            
            if (moduleCard) {
                const progressBar = moduleCard.querySelector('.progress-fill');
                const progressText = moduleCard.querySelector('.progress-percentage');
                
                if (progressBar) {
                    progressBar.style.width = `${progress.progress}%`;
                }
                if (progressText) {
                    progressText.textContent = `${progress.progress}%`;
                }
            }
        });

        // Update analytics
        this.updateAnalytics();
    }

    updateAnalytics() {
        const analytics = document.querySelectorAll('.analytics-number');
        if (analytics.length >= 6) {
            analytics[0].textContent = Object.keys(this.userProgress.modules).filter(
                id => this.userProgress.modules[id].completed
            ).length;
            analytics[1].textContent = `${this.userProgress.averageScore}%`;
            analytics[2].textContent = `${this.userProgress.totalStudyTime}h`;
            analytics[3].textContent = this.userProgress.certificatesEarned;
            analytics[4].textContent = `${this.userProgress.retentionRate}%`;
            analytics[5].textContent = this.userProgress.streakDays;
        }
    }

    updateRecommendedPath(pathName) {
        const recommendedSection = document.querySelector('.recommended-path');
        if (recommendedSection) {
            const pathNames = {
                'foundation': 'Foundation Level',
                'intermediate': 'Intermediate Level',
                'advanced': 'Advanced Level',
                'specialist': 'Specialist Level'
            };
            
            const pathDescription = recommendedSection.querySelector('p');
            pathDescription.innerHTML = `Based on your profile and progress, we recommend the <strong>${pathNames[pathName]}</strong> path to continue your learning journey.`;
        }
    }

    initializeProgressTracking() {
        // Set up periodic progress updates
        setInterval(() => {
            this.updateProgressDisplay();
        }, 30000); // Update every 30 seconds

        // Initialize analytics display
        this.updateAnalytics();
    }
}

// Global functions for HTML onclick handlers
function selectPath(pathName) {
    if (window.trainingModulesManager) {
        window.trainingModulesManager.selectPath(pathName);
    }
}

function startModule(moduleId) {
    if (window.trainingModulesManager) {
        window.trainingModulesManager.startModule(moduleId);
    }
}

function previewModule(moduleId) {
    if (window.trainingModulesManager) {
        window.trainingModulesManager.previewModule(moduleId);
    }
}

function closeModal() {
    const modal = document.getElementById('module-modal');
    if (modal) {
        modal.style.display = 'none';
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.trainingModulesManager = new TrainingModulesManager();
    
    // Add animation styles
    const animationStyle = document.createElement('style');
    animationStyle.textContent = `
        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }
        
        .nav-brand {
            display: flex;
            align-items: center;
            gap: 1rem;
            text-decoration: none;
        }
        
        .nav-logo {
            height: 40px;
            width: auto;
        }
        
        .brand-text {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--enterprise-primary);
        }
        
        .nav-brand:hover .brand-text {
            color: var(--enterprise-secondary);
        }
    `;
    document.head.appendChild(animationStyle);
});
