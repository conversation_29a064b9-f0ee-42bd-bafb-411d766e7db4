/* Enterprise X-ray Training Platform Styles */

/* Global Enterprise Theme */
:root {
    --enterprise-primary: #0066cc;
    --enterprise-secondary: #004499;
    --enterprise-accent: #00aaff;
    --enterprise-success: #28a745;
    --enterprise-warning: #ffc107;
    --enterprise-danger: #dc3545;
    --enterprise-dark: #1a1a1a;
    --enterprise-light: #f8f9fa;
    --enterprise-gradient: linear-gradient(135deg, #0066cc 0%, #004499 100%);
    --shadow-enterprise: 0 4px 20px rgba(0, 102, 204, 0.15);
    --border-radius: 8px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Enterprise Header */
.enterprise-header {
    background: var(--enterprise-gradient);
    color: white;
    padding: 2rem 0;
    position: relative;
    overflow: hidden;
}

.enterprise-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 2;
}

.logo-section {
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

.platform-logo {
    height: 60px;
    width: auto;
}

.title-group h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.enterprise-badge {
    background: var(--enterprise-accent);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.certification-badges {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.badge-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(255, 255, 255, 0.1);
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    backdrop-filter: blur(10px);
    font-size: 0.9rem;
}

/* Enterprise Navigation */
.enterprise-nav {
    background: white;
    box-shadow: var(--shadow-enterprise);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.nav-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
}

.main-nav {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    gap: 2rem;
}

.main-nav a {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--enterprise-dark);
    text-decoration: none;
    font-weight: 500;
    padding: 0.75rem 1rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.main-nav a:hover,
.main-nav a.active {
    background: var(--enterprise-primary);
    color: white;
    transform: translateY(-2px);
}

.nav-tools {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.language-selector select {
    padding: 0.5rem 1rem;
    border: 2px solid var(--enterprise-light);
    border-radius: var(--border-radius);
    background: white;
    font-size: 0.9rem;
    cursor: pointer;
    transition: var(--transition);
}

.language-selector select:focus {
    outline: none;
    border-color: var(--enterprise-primary);
    box-shadow: 0 0 0 3px rgba(0, 102, 204, 0.1);
}

/* Enterprise Hero Section */
.enterprise-hero {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 4rem 0;
    position: relative;
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.hero-main h2 {
    font-size: 3rem;
    font-weight: 700;
    color: var(--enterprise-dark);
    margin-bottom: 1.5rem;
    line-height: 1.2;
}

.highlight {
    color: var(--enterprise-primary);
    display: block;
    font-size: 0.8em;
    margin-top: 0.5rem;
}

.hero-stats {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 2rem;
    margin: 2rem 0;
}

.stat-item {
    text-align: center;
    padding: 1.5rem;
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-enterprise);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--enterprise-primary);
    display: block;
}

.stat-label {
    font-size: 0.9rem;
    color: #666;
    margin-top: 0.5rem;
}

.hero-actions {
    display: flex;
    gap: 1rem;
    margin-top: 2rem;
}

.btn-large {
    padding: 1rem 2rem;
    font-size: 1.1rem;
    font-weight: 600;
}

.btn-outline {
    background: transparent;
    border: 2px solid var(--enterprise-primary);
    color: var(--enterprise-primary);
}

.btn-outline:hover {
    background: var(--enterprise-primary);
    color: white;
}

/* Interactive Preview */
.interactive-preview {
    position: relative;
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-enterprise);
    overflow: hidden;
}

#hero-canvas {
    width: 100%;
    height: auto;
    display: block;
}

.preview-overlay {
    position: absolute;
    top: 1rem;
    right: 1rem;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.feature-highlight {
    background: rgba(0, 102, 204, 0.9);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    backdrop-filter: blur(10px);
}

/* Vendor Support */
.vendor-support {
    text-align: center;
    margin-top: 3rem;
    padding: 2rem;
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-enterprise);
}

.vendor-logos {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 3rem;
    margin-top: 1.5rem;
    flex-wrap: wrap;
}

.vendor-logo {
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0.7;
    transition: var(--transition);
    filter: grayscale(100%);
}

.vendor-logo:hover {
    opacity: 1;
    filter: grayscale(0%);
    transform: scale(1.1);
}

.vendor-logo img {
    height: 100%;
    width: auto;
}

.vendor-logo span {
    font-weight: 600;
    color: var(--enterprise-dark);
    font-size: 1.1rem;
}

/* Progress Bars */
.progress-bar {
    width: 100%;
    height: 6px;
    background: #e9ecef;
    border-radius: 3px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: var(--enterprise-success);
    border-radius: 3px;
    transition: width 0.3s ease;
}

.progress-0 { width: 0%; }
.progress-25 { width: 25%; }
.progress-50 { width: 50%; }
.progress-75 { width: 75%; }
.progress-100 { width: 100%; }

/* Module Cards Enhancement */
.enterprise-card {
    border: none;
    box-shadow: var(--shadow-enterprise);
    transition: var(--transition);
    overflow: hidden;
}

.enterprise-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0, 102, 204, 0.2);
}

.module-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.module-badges {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.difficulty-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.difficulty-badge.foundation {
    background: var(--enterprise-success);
    color: white;
}

.difficulty-badge.advanced {
    background: var(--enterprise-warning);
    color: white;
}

.difficulty-badge.expert {
    background: var(--enterprise-danger);
    color: white;
}

.duration-badge {
    background: var(--enterprise-light);
    color: var(--enterprise-dark);
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
}

.module-features {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin: 1rem 0;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    color: #666;
}

.feature-item i {
    color: var(--enterprise-primary);
    width: 16px;
}

.module-progress {
    margin: 1rem 0;
}

.progress-text {
    font-size: 0.8rem;
    color: #666;
    margin-top: 0.5rem;
    display: block;
}

.module-actions {
    display: flex;
    gap: 0.5rem;
    margin-top: 1rem;
}

.btn-small {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 2rem;
        text-align: center;
    }
    
    .hero-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .hero-stats {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .main-nav {
        flex-wrap: wrap;
        gap: 1rem;
    }
    
    .vendor-logos {
        gap: 1.5rem;
    }
    
    .hero-actions {
        flex-direction: column;
    }
}
