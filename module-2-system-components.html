<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Module 2: System Components & Architecture - VirtualX Pro</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --napkin-primary: #2563eb;
            --napkin-secondary: #7c3aed;
            --napkin-accent: #06b6d4;
            --napkin-success: #10b981;
            --napkin-warning: #f59e0b;
            --napkin-danger: #ef4444;
            --napkin-dark: #1f2937;
            --napkin-light: #f8fafc;
            --napkin-gradient: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
            --napkin-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            --napkin-shadow-lg: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #eff6ff 0%, #f3e8ff 100%);
            min-height: 100vh;
            line-height: 1.6;
        }

        .training-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }

        .module-header {
            text-align: center;
            margin-bottom: 3rem;
            background: var(--napkin-gradient);
            color: white;
            padding: 3rem 2rem;
            border-radius: 20px;
            box-shadow: var(--napkin-shadow-lg);
            position: relative;
            overflow: hidden;
        }

        .module-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><rect x="10" y="10" width="20" height="20" fill="rgba(255,255,255,0.05)"/><rect x="70" y="30" width="15" height="15" fill="rgba(255,255,255,0.05)"/><rect x="30" y="70" width="25" height="25" fill="rgba(255,255,255,0.05)"/></svg>');
            animation: float 15s ease-in-out infinite;
        }

        .module-title {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 1rem;
            position: relative;
            z-index: 1;
        }

        .module-subtitle {
            font-size: 1.3rem;
            opacity: 0.9;
            margin-bottom: 2rem;
            position: relative;
            z-index: 1;
        }

        /* System Architecture Mind Map */
        .system-mindmap {
            position: relative;
            background: white;
            border-radius: 20px;
            padding: 4rem;
            margin: 2rem 0;
            box-shadow: var(--napkin-shadow);
            overflow: hidden;
            min-height: 700px;
        }

        .system-core {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 220px;
            height: 220px;
            background: var(--napkin-gradient);
            border-radius: 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 1.1rem;
            text-align: center;
            box-shadow: var(--napkin-shadow-lg);
            cursor: pointer;
            transition: all 0.3s ease;
            z-index: 10;
        }

        .system-core:hover {
            transform: translate(-50%, -50%) scale(1.05);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
        }

        .core-icon {
            font-size: 3.5rem;
            margin-bottom: 0.5rem;
        }

        .component-node {
            position: absolute;
            width: 160px;
            height: 160px;
            background: white;
            border: 3px solid var(--napkin-primary);
            border-radius: 15px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: var(--napkin-shadow);
        }

        .component-node:hover {
            transform: scale(1.1);
            background: var(--napkin-primary);
            color: white;
            box-shadow: var(--napkin-shadow-lg);
        }

        .component-icon {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
            color: var(--napkin-primary);
        }

        .component-node:hover .component-icon {
            color: white;
        }

        /* Component positioning */
        .tube-node { top: 5%; left: 15%; }
        .generator-node { top: 5%; right: 15%; }
        .detector-node { bottom: 5%; left: 15%; }
        .collimator-node { bottom: 5%; right: 15%; }
        .control-node { top: 50%; left: 5%; transform: translateY(-50%); }
        .table-node { top: 50%; right: 5%; transform: translateY(-50%); }

        /* Connection lines for mind map */
        .connection-line {
            position: absolute;
            background: var(--napkin-primary);
            height: 3px;
            transform-origin: left center;
            opacity: 0.6;
            z-index: 1;
            border-radius: 2px;
        }

        .line-tube { top: 25%; left: 50%; width: 180px; transform: rotate(-35deg); }
        .line-generator { top: 25%; right: 50%; width: 180px; transform: rotate(35deg); transform-origin: right center; }
        .line-detector { bottom: 25%; left: 50%; width: 180px; transform: rotate(35deg); }
        .line-collimator { bottom: 25%; right: 50%; width: 180px; transform: rotate(-35deg); transform-origin: right center; }
        .line-control { top: 50%; left: 25%; width: 120px; }
        .line-table { top: 50%; right: 25%; width: 120px; transform-origin: right center; }

        /* 3D Model Viewer */
        .model-viewer-section {
            background: var(--napkin-dark);
            border-radius: 20px;
            padding: 2rem;
            margin: 3rem 0;
            color: white;
            position: relative;
            overflow: hidden;
        }

        .viewer-header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .viewer-controls {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin-bottom: 2rem;
            flex-wrap: wrap;
        }

        .viewer-btn {
            background: var(--napkin-primary);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 25px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .viewer-btn:hover {
            background: var(--napkin-secondary);
            transform: translateY(-2px);
        }

        .viewer-btn.active {
            background: var(--napkin-accent);
        }

        .model-viewport {
            width: 100%;
            height: 500px;
            background: #111827;
            border-radius: 15px;
            position: relative;
            overflow: hidden;
            border: 2px solid #374151;
        }

        .viewport-overlay {
            position: absolute;
            top: 1rem;
            left: 1rem;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 1rem;
            border-radius: 8px;
            font-family: monospace;
            font-size: 0.9rem;
        }

        /* Vendor Comparison */
        .vendor-comparison {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            margin: 3rem 0;
            box-shadow: var(--napkin-shadow);
        }

        .vendor-tabs {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin-bottom: 2rem;
            flex-wrap: wrap;
        }

        .vendor-tab {
            background: var(--napkin-light);
            color: var(--napkin-dark);
            border: 2px solid transparent;
            padding: 1rem 2rem;
            border-radius: 25px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .vendor-tab:hover,
        .vendor-tab.active {
            background: var(--napkin-primary);
            color: white;
            border-color: var(--napkin-primary);
        }

        .vendor-content {
            display: none;
            animation: fadeInUp 0.5s ease;
        }

        .vendor-content.active {
            display: block;
        }

        .specs-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-top: 2rem;
        }

        .spec-card {
            background: var(--napkin-light);
            padding: 1.5rem;
            border-radius: 12px;
            border-left: 4px solid var(--napkin-primary);
        }

        .spec-title {
            font-weight: 600;
            color: var(--napkin-dark);
            margin-bottom: 0.5rem;
        }

        .spec-value {
            color: var(--napkin-primary);
            font-weight: 500;
        }

        /* Assembly Simulator */
        .assembly-simulator {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            margin: 3rem 0;
            box-shadow: var(--napkin-shadow);
        }

        .assembly-steps {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-top: 2rem;
        }

        .step-card {
            background: var(--napkin-light);
            padding: 1.5rem;
            border-radius: 12px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .step-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--napkin-shadow);
        }

        .step-number {
            width: 40px;
            height: 40px;
            background: var(--napkin-primary);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin: 0 auto 1rem;
        }

        .step-title {
            font-weight: 600;
            color: var(--napkin-dark);
            margin-bottom: 0.5rem;
        }

        .step-description {
            color: #6b7280;
            font-size: 0.9rem;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .module-title {
                font-size: 2rem;
            }
            
            .system-mindmap {
                padding: 2rem;
                min-height: 500px;
            }
            
            .system-core {
                width: 150px;
                height: 150px;
                font-size: 0.9rem;
            }
            
            .component-node {
                width: 100px;
                height: 100px;
                font-size: 0.8rem;
            }
            
            .component-icon {
                font-size: 1.5rem;
            }
        }

        /* Animations */
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-15px); }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }
    </style>
</head>
<body>
    <div class="training-container">
        <!-- Module Header -->
        <div class="module-header animate__animated animate__fadeInDown">
            <h1 class="module-title">
                <i class="fas fa-sitemap"></i>
                System Components & Architecture
            </h1>
            <p class="module-subtitle">Interactive 3D Component Exploration</p>
            <p>Master X-ray system components and architecture through immersive 3D exploration and hands-on learning</p>
            
            <div class="module-stats" style="display: flex; justify-content: center; gap: 3rem; margin-top: 2rem;">
                <div class="stat-item">
                    <span style="font-size: 2.5rem; font-weight: bold; display: block;">8</span>
                    <span style="font-size: 0.9rem; opacity: 0.8;">Hours</span>
                </div>
                <div class="stat-item">
                    <span style="font-size: 2.5rem; font-weight: bold; display: block;">6</span>
                    <span style="font-size: 0.9rem; opacity: 0.8;">Components</span>
                </div>
                <div class="stat-item">
                    <span style="font-size: 2.5rem; font-weight: bold; display: block;">3D</span>
                    <span style="font-size: 0.9rem; opacity: 0.8;">Interactive</span>
                </div>
                <div class="stat-item">
                    <span style="font-size: 2.5rem; font-weight: bold; display: block;">Multi</span>
                    <span style="font-size: 0.9rem; opacity: 0.8;">Vendor</span>
                </div>
            </div>
        </div>

        <!-- System Architecture Mind Map -->
        <div class="system-mindmap">
            <h2 style="text-align: center; margin-bottom: 3rem; color: var(--napkin-dark); font-size: 1.8rem;">
                X-ray System Architecture Mind Map
            </h2>
            
            <!-- Central System Core -->
            <div class="system-core" onclick="showSystemOverview()">
                <div class="core-icon">
                    <i class="fas fa-microchip"></i>
                </div>
                <div>X-ray System<br>Architecture</div>
            </div>
            
            <!-- Component Nodes -->
            <div class="component-node tube-node" onclick="showComponent('tube')">
                <div class="component-icon">
                    <i class="fas fa-radiation"></i>
                </div>
                <div>X-ray Tube</div>
            </div>
            
            <div class="component-node generator-node" onclick="showComponent('generator')">
                <div class="component-icon">
                    <i class="fas fa-bolt"></i>
                </div>
                <div>Generator</div>
            </div>
            
            <div class="component-node detector-node" onclick="showComponent('detector')">
                <div class="component-icon">
                    <i class="fas fa-tv"></i>
                </div>
                <div>Detector</div>
            </div>
            
            <div class="component-node collimator-node" onclick="showComponent('collimator')">
                <div class="component-icon">
                    <i class="fas fa-adjust"></i>
                </div>
                <div>Collimator</div>
            </div>
            
            <div class="component-node control-node" onclick="showComponent('control')">
                <div class="component-icon">
                    <i class="fas fa-cogs"></i>
                </div>
                <div>Control System</div>
            </div>
            
            <div class="component-node table-node" onclick="showComponent('table')">
                <div class="component-icon">
                    <i class="fas fa-bed"></i>
                </div>
                <div>Patient Table</div>
            </div>
            
            <!-- Connection Lines -->
            <div class="connection-line line-tube"></div>
            <div class="connection-line line-generator"></div>
            <div class="connection-line line-detector"></div>
            <div class="connection-line line-collimator"></div>
            <div class="connection-line line-control"></div>
            <div class="connection-line line-table"></div>
        </div>

        <!-- 3D Model Viewer -->
        <div class="model-viewer-section">
            <div class="viewer-header">
                <h3 style="font-size: 1.8rem; margin-bottom: 1rem;">
                    <i class="fas fa-cube"></i>
                    Interactive 3D Model Viewer
                </h3>
                <p style="opacity: 0.8;">Explore photorealistic 3D models of X-ray systems</p>
            </div>
            
            <div class="viewer-controls">
                <button type="button" class="viewer-btn active" onclick="loadModel('ge-discovery')">
                    <i class="fas fa-hospital"></i> GE Discovery
                </button>
                <button type="button" class="viewer-btn" onclick="loadModel('philips-digitaldiagnost')">
                    <i class="fas fa-hospital"></i> Philips DigitalDiagnost
                </button>
                <button type="button" class="viewer-btn" onclick="loadModel('siemens-ysio')">
                    <i class="fas fa-hospital"></i> Siemens Ysio
                </button>
                <button type="button" class="viewer-btn" onclick="toggleExplodedView()">
                    <i class="fas fa-expand-arrows-alt"></i> Exploded View
                </button>
                <button type="button" class="viewer-btn" onclick="toggleXrayView()">
                    <i class="fas fa-eye"></i> X-ray View
                </button>
            </div>
            
            <div class="model-viewport" id="modelViewport">
                <div class="viewport-overlay">
                    <div>Model: GE Discovery XR656</div>
                    <div>Components: 6 visible</div>
                    <div>View: Standard</div>
                    <div>Zoom: 100%</div>
                </div>
                
                <div style="display: flex; align-items: center; justify-content: center; height: 100%; color: #6b7280;">
                    <div style="text-align: center;">
                        <i class="fas fa-cube" style="font-size: 4rem; margin-bottom: 1rem;"></i>
                        <p>3D Model Loading...</p>
                        <p style="font-size: 0.9rem; opacity: 0.7;">Click and drag to rotate • Scroll to zoom</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Vendor Comparison -->
        <div class="vendor-comparison">
            <h3 style="text-align: center; margin-bottom: 2rem; color: var(--napkin-dark); font-size: 1.8rem;">
                <i class="fas fa-balance-scale"></i>
                Multi-Vendor System Comparison
            </h3>
            
            <div class="vendor-tabs">
                <div class="vendor-tab active" onclick="showVendor('ge')">
                    <i class="fas fa-hospital"></i> GE Healthcare
                </div>
                <div class="vendor-tab" onclick="showVendor('philips')">
                    <i class="fas fa-hospital"></i> Philips Healthcare
                </div>
                <div class="vendor-tab" onclick="showVendor('siemens')">
                    <i class="fas fa-hospital"></i> Siemens Healthineers
                </div>
                <div class="vendor-tab" onclick="showVendor('canon')">
                    <i class="fas fa-hospital"></i> Canon Medical
                </div>
            </div>
            
            <div class="vendor-content active" id="ge-content">
                <h4 style="color: var(--napkin-primary); margin-bottom: 1rem;">GE Healthcare - Discovery XR656</h4>
                <div class="specs-grid">
                    <div class="spec-card">
                        <div class="spec-title">Generator Type</div>
                        <div class="spec-value">High Frequency IGBT</div>
                    </div>
                    <div class="spec-card">
                        <div class="spec-title">kVp Range</div>
                        <div class="spec-value">40-150 kVp</div>
                    </div>
                    <div class="spec-card">
                        <div class="spec-title">mA Range</div>
                        <div class="spec-value">10-500 mA</div>
                    </div>
                    <div class="spec-card">
                        <div class="spec-title">Detector Size</div>
                        <div class="spec-value">43 x 43 cm</div>
                    </div>
                    <div class="spec-card">
                        <div class="spec-title">Image Matrix</div>
                        <div class="spec-value">3072 x 3072</div>
                    </div>
                    <div class="spec-card">
                        <div class="spec-title">Pixel Size</div>
                        <div class="spec-value">140 μm</div>
                    </div>
                </div>
            </div>
            
            <div class="vendor-content" id="philips-content">
                <h4 style="color: var(--napkin-primary); margin-bottom: 1rem;">Philips Healthcare - DigitalDiagnost C90</h4>
                <div class="specs-grid">
                    <div class="spec-card">
                        <div class="spec-title">Generator Type</div>
                        <div class="spec-value">Digital High Frequency</div>
                    </div>
                    <div class="spec-card">
                        <div class="spec-title">kVp Range</div>
                        <div class="spec-value">40-150 kVp</div>
                    </div>
                    <div class="spec-card">
                        <div class="spec-title">mA Range</div>
                        <div class="spec-value">10-630 mA</div>
                    </div>
                    <div class="spec-card">
                        <div class="spec-title">Suspension</div>
                        <div class="spec-value">SkyFlow 5-axis</div>
                    </div>
                    <div class="spec-card">
                        <div class="spec-title">User Interface</div>
                        <div class="spec-value">Eleva Touch</div>
                    </div>
                    <div class="spec-card">
                        <div class="spec-title">Dose Management</div>
                        <div class="spec-value">DoseWise Portal</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Virtual Assembly Simulator -->
        <div class="assembly-simulator">
            <h3 style="text-align: center; margin-bottom: 2rem; color: var(--napkin-dark); font-size: 1.8rem;">
                <i class="fas fa-tools"></i>
                Virtual Assembly Simulator
            </h3>
            <p style="text-align: center; margin-bottom: 2rem; color: #6b7280;">
                Practice system assembly procedures in a safe virtual environment
            </p>
            
            <div class="assembly-steps">
                <div class="step-card" onclick="startAssemblyStep(1)">
                    <div class="step-number">1</div>
                    <div class="step-title">Foundation Setup</div>
                    <div class="step-description">Install base frame and power connections</div>
                </div>
                
                <div class="step-card" onclick="startAssemblyStep(2)">
                    <div class="step-number">2</div>
                    <div class="step-title">Generator Installation</div>
                    <div class="step-description">Mount and connect high-frequency generator</div>
                </div>
                
                <div class="step-card" onclick="startAssemblyStep(3)">
                    <div class="step-number">3</div>
                    <div class="step-title">X-ray Tube Assembly</div>
                    <div class="step-description">Install tube housing and cooling system</div>
                </div>
                
                <div class="step-card" onclick="startAssemblyStep(4)">
                    <div class="step-number">4</div>
                    <div class="step-title">Detector Mounting</div>
                    <div class="step-description">Position and calibrate flat panel detector</div>
                </div>
                
                <div class="step-card" onclick="startAssemblyStep(5)">
                    <div class="step-number">5</div>
                    <div class="step-title">Control System</div>
                    <div class="step-description">Connect control console and software</div>
                </div>
                
                <div class="step-card" onclick="startAssemblyStep(6)">
                    <div class="step-number">6</div>
                    <div class="step-title">Final Testing</div>
                    <div class="step-description">System calibration and quality assurance</div>
                </div>
            </div>
        </div>
    </div>

    <script src="js/module-2-components.js"></script>
</body>
</html>
