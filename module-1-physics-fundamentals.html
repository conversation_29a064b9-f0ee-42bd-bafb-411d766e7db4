<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Module 1: X-ray Physics Fundamentals - VirtualX Pro</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --napkin-primary: #2563eb;
            --napkin-secondary: #7c3aed;
            --napkin-accent: #06b6d4;
            --napkin-success: #10b981;
            --napkin-warning: #f59e0b;
            --napkin-danger: #ef4444;
            --napkin-dark: #1f2937;
            --napkin-light: #f8fafc;
            --napkin-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --napkin-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            --napkin-shadow-lg: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #f0f9ff 0%, #e0e7ff 100%);
            min-height: 100vh;
            line-height: 1.6;
        }

        .training-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }

        .module-header {
            text-align: center;
            margin-bottom: 3rem;
            background: var(--napkin-gradient);
            color: white;
            padding: 3rem 2rem;
            border-radius: 20px;
            box-shadow: var(--napkin-shadow-lg);
            position: relative;
            overflow: hidden;
        }

        .module-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1.5" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="80" r="1" fill="rgba(255,255,255,0.1)"/></svg>');
            animation: float 20s ease-in-out infinite;
        }

        .module-title {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 1rem;
            position: relative;
            z-index: 1;
        }

        .module-subtitle {
            font-size: 1.3rem;
            opacity: 0.9;
            margin-bottom: 2rem;
            position: relative;
            z-index: 1;
        }

        .module-stats {
            display: flex;
            justify-content: center;
            gap: 3rem;
            margin-top: 2rem;
            position: relative;
            z-index: 1;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            display: block;
        }

        .stat-label {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        /* Mind Map Style Layout */
        .mind-map-container {
            position: relative;
            background: white;
            border-radius: 20px;
            padding: 3rem;
            margin: 2rem 0;
            box-shadow: var(--napkin-shadow);
            overflow: hidden;
        }

        .central-concept {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 200px;
            height: 200px;
            background: var(--napkin-gradient);
            border-radius: 50%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 1.2rem;
            text-align: center;
            box-shadow: var(--napkin-shadow-lg);
            cursor: pointer;
            transition: all 0.3s ease;
            z-index: 10;
        }

        .central-concept:hover {
            transform: translate(-50%, -50%) scale(1.1);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
        }

        .concept-icon {
            font-size: 3rem;
            margin-bottom: 0.5rem;
        }

        .branch-node {
            position: absolute;
            width: 150px;
            height: 150px;
            background: white;
            border: 3px solid var(--napkin-primary);
            border-radius: 50%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: var(--napkin-shadow);
        }

        .branch-node:hover {
            transform: scale(1.1);
            background: var(--napkin-primary);
            color: white;
            box-shadow: var(--napkin-shadow-lg);
        }

        .branch-icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
            color: var(--napkin-primary);
        }

        .branch-node:hover .branch-icon {
            color: white;
        }

        /* Positioning for mind map nodes */
        .node-1 { top: 10%; left: 20%; }
        .node-2 { top: 10%; right: 20%; }
        .node-3 { bottom: 10%; left: 20%; }
        .node-4 { bottom: 10%; right: 20%; }

        /* Connecting lines */
        .connection-line {
            position: absolute;
            background: var(--napkin-primary);
            height: 2px;
            transform-origin: left center;
            opacity: 0.6;
            z-index: 1;
        }

        .line-1 {
            top: 35%;
            left: 50%;
            width: 150px;
            transform: rotate(-45deg);
        }

        .line-2 {
            top: 35%;
            right: 50%;
            width: 150px;
            transform: rotate(45deg);
            transform-origin: right center;
        }

        .line-3 {
            bottom: 35%;
            left: 50%;
            width: 150px;
            transform: rotate(45deg);
        }

        .line-4 {
            bottom: 35%;
            right: 50%;
            width: 150px;
            transform: rotate(-45deg);
            transform-origin: right center;
        }

        /* Interactive Demo Tools */
        .demo-tools-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin: 3rem 0;
        }

        .demo-tool {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: var(--napkin-shadow);
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .demo-tool::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(37, 99, 235, 0.1), transparent);
            transition: left 0.5s ease;
        }

        .demo-tool:hover::before {
            left: 100%;
        }

        .demo-tool:hover {
            transform: translateY(-10px);
            box-shadow: var(--napkin-shadow-lg);
        }

        .tool-icon {
            width: 60px;
            height: 60px;
            background: var(--napkin-gradient);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            margin-bottom: 1rem;
        }

        .tool-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: var(--napkin-dark);
            margin-bottom: 0.5rem;
        }

        .tool-description {
            color: #6b7280;
            margin-bottom: 1rem;
            font-size: 0.9rem;
        }

        .tool-features {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
        }

        .feature-tag {
            background: var(--napkin-light);
            color: var(--napkin-primary);
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        /* Animation Viewer */
        .animation-viewer {
            background: var(--napkin-dark);
            border-radius: 15px;
            padding: 2rem;
            margin: 2rem 0;
            color: white;
            position: relative;
            overflow: hidden;
        }

        .animation-controls {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .control-btn {
            background: var(--napkin-primary);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 25px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .control-btn:hover {
            background: var(--napkin-secondary);
            transform: translateY(-2px);
        }

        .animation-canvas {
            width: 100%;
            height: 400px;
            background: #111827;
            border-radius: 10px;
            position: relative;
            overflow: hidden;
        }

        /* Progress Tracking */
        .progress-section {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            margin: 2rem 0;
            box-shadow: var(--napkin-shadow);
        }

        .progress-header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .progress-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--napkin-dark);
            margin-bottom: 0.5rem;
        }

        .progress-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
        }

        .progress-item {
            text-align: center;
            padding: 1.5rem;
            background: var(--napkin-light);
            border-radius: 10px;
        }

        .progress-circle {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: conic-gradient(var(--napkin-success) 0deg, var(--napkin-light) 0deg);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            font-weight: bold;
            color: var(--napkin-dark);
        }

        .progress-label {
            font-weight: 500;
            color: var(--napkin-dark);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .module-title {
                font-size: 2rem;
            }
            
            .module-stats {
                flex-direction: column;
                gap: 1rem;
            }
            
            .mind-map-container {
                height: 600px;
            }
            
            .central-concept {
                width: 150px;
                height: 150px;
                font-size: 1rem;
            }
            
            .branch-node {
                width: 100px;
                height: 100px;
                font-size: 0.8rem;
            }
        }

        /* Animations */
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .animate-on-scroll {
            animation: fadeInUp 0.6s ease-out;
        }
    </style>
</head>
<body>
    <div class="training-container">
        <!-- Module Header -->
        <div class="module-header animate__animated animate__fadeInDown">
            <h1 class="module-title">
                <i class="fas fa-atom"></i>
                X-ray Physics Fundamentals
            </h1>
            <p class="module-subtitle">Interactive Physics Simulation & Theory</p>
            <p>Master the fundamental physics principles of X-ray production, interaction, and detection through immersive simulations</p>
            
            <div class="module-stats">
                <div class="stat-item">
                    <span class="stat-number">6</span>
                    <span class="stat-label">Hours</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">4</span>
                    <span class="stat-label">Lessons</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">IEC</span>
                    <span class="stat-label">Compliant</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">95%</span>
                    <span class="stat-label">Success Rate</span>
                </div>
            </div>
        </div>

        <!-- Mind Map Concept Overview -->
        <div class="mind-map-container">
            <h2 style="text-align: center; margin-bottom: 2rem; color: var(--napkin-dark); font-size: 1.8rem;">
                Physics Concepts Mind Map
            </h2>
            
            <!-- Central Concept -->
            <div class="central-concept" onclick="showCentralConcept()">
                <div class="concept-icon">
                    <i class="fas fa-atom"></i>
                </div>
                <div>X-ray Physics</div>
            </div>
            
            <!-- Branch Nodes -->
            <div class="branch-node node-1" onclick="showConcept('production')">
                <div class="branch-icon">
                    <i class="fas fa-bolt"></i>
                </div>
                <div>X-ray Production</div>
            </div>
            
            <div class="branch-node node-2" onclick="showConcept('interaction')">
                <div class="branch-icon">
                    <i class="fas fa-exchange-alt"></i>
                </div>
                <div>Matter Interaction</div>
            </div>
            
            <div class="branch-node node-3" onclick="showConcept('attenuation')">
                <div class="branch-icon">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div>Beam Attenuation</div>
            </div>
            
            <div class="branch-node node-4" onclick="showConcept('safety')">
                <div class="branch-icon">
                    <i class="fas fa-shield-alt"></i>
                </div>
                <div>Radiation Safety</div>
            </div>
            
            <!-- Connecting Lines -->
            <div class="connection-line line-1"></div>
            <div class="connection-line line-2"></div>
            <div class="connection-line line-3"></div>
            <div class="connection-line line-4"></div>
        </div>

        <!-- Interactive Demo Tools -->
        <div class="demo-tools-grid">
            <div class="demo-tool" onclick="openDemo('electron-target')">
                <div class="tool-icon">
                    <i class="fas fa-play-circle"></i>
                </div>
                <div class="tool-title">3D Electron-Target Interaction</div>
                <div class="tool-description">
                    Visualize electron beam interactions with tungsten targets in real-time 3D simulation
                </div>
                <div class="tool-features">
                    <span class="feature-tag">3D Visualization</span>
                    <span class="feature-tag">Real-time</span>
                    <span class="feature-tag">Interactive</span>
                </div>
            </div>
            
            <div class="demo-tool" onclick="openDemo('spectrum-analyzer')">
                <div class="tool-icon">
                    <i class="fas fa-chart-area"></i>
                </div>
                <div class="tool-title">X-ray Spectrum Analyzer</div>
                <div class="tool-description">
                    Interactive tool for analyzing X-ray spectra, energy distributions, and filtration effects
                </div>
                <div class="tool-features">
                    <span class="feature-tag">Spectrum Analysis</span>
                    <span class="feature-tag">Energy Calc</span>
                    <span class="feature-tag">Filtration</span>
                </div>
            </div>
            
            <div class="demo-tool" onclick="openDemo('hvl-calculator')">
                <div class="tool-icon">
                    <i class="fas fa-calculator"></i>
                </div>
                <div class="tool-title">HVL & Filtration Calculator</div>
                <div class="tool-description">
                    Calculate half-value layers and optimize beam filtration for different materials
                </div>
                <div class="tool-features">
                    <span class="feature-tag">HVL Calc</span>
                    <span class="feature-tag">Materials</span>
                    <span class="feature-tag">Optimization</span>
                </div>
            </div>
            
            <div class="demo-tool" onclick="openDemo('shielding-designer')">
                <div class="tool-icon">
                    <i class="fas fa-shield-alt"></i>
                </div>
                <div class="tool-title">Radiation Shielding Designer</div>
                <div class="tool-description">
                    Design protective barriers and calculate shielding requirements per NCRP guidelines
                </div>
                <div class="tool-features">
                    <span class="feature-tag">NCRP Standards</span>
                    <span class="feature-tag">3D Design</span>
                    <span class="feature-tag">ALARA</span>
                </div>
            </div>
        </div>

        <!-- Animation Viewer -->
        <div class="animation-viewer">
            <h3 style="text-align: center; margin-bottom: 1rem; font-size: 1.5rem;">
                <i class="fas fa-film"></i>
                Physics Animation Theater
            </h3>
            <p style="text-align: center; margin-bottom: 2rem; opacity: 0.8;">
                Watch animated demonstrations of X-ray physics principles
            </p>
            
            <div class="animation-controls">
                <button type="button" class="control-btn" onclick="playAnimation('bremsstrahlung')">
                    <i class="fas fa-play"></i> Bremsstrahlung
                </button>
                <button type="button" class="control-btn" onclick="playAnimation('characteristic')">
                    <i class="fas fa-play"></i> Characteristic X-rays
                </button>
                <button type="button" class="control-btn" onclick="playAnimation('photoelectric')">
                    <i class="fas fa-play"></i> Photoelectric Effect
                </button>
                <button type="button" class="control-btn" onclick="playAnimation('compton')">
                    <i class="fas fa-play"></i> Compton Scattering
                </button>
            </div>
            
            <div class="animation-canvas" id="animationCanvas">
                <div style="display: flex; align-items: center; justify-content: center; height: 100%; color: #6b7280;">
                    <div style="text-align: center;">
                        <i class="fas fa-play-circle" style="font-size: 4rem; margin-bottom: 1rem;"></i>
                        <p>Select an animation to begin</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Progress Tracking -->
        <div class="progress-section">
            <div class="progress-header">
                <h3 class="progress-title">Your Learning Progress</h3>
                <p style="color: #6b7280;">Track your mastery of X-ray physics concepts</p>
            </div>
            
            <div class="progress-grid">
                <div class="progress-item">
                    <div class="progress-circle" style="background: conic-gradient(var(--napkin-success) 270deg, var(--napkin-light) 270deg);">
                        75%
                    </div>
                    <div class="progress-label">X-ray Production</div>
                </div>
                
                <div class="progress-item">
                    <div class="progress-circle" style="background: conic-gradient(var(--napkin-success) 180deg, var(--napkin-light) 180deg);">
                        50%
                    </div>
                    <div class="progress-label">Matter Interaction</div>
                </div>
                
                <div class="progress-item">
                    <div class="progress-circle" style="background: conic-gradient(var(--napkin-success) 90deg, var(--napkin-light) 90deg);">
                        25%
                    </div>
                    <div class="progress-label">Beam Attenuation</div>
                </div>
                
                <div class="progress-item">
                    <div class="progress-circle" style="background: conic-gradient(var(--napkin-light) 360deg);">
                        0%
                    </div>
                    <div class="progress-label">Radiation Safety</div>
                </div>
            </div>
        </div>
    </div>

    <script src="js/module-1-physics.js"></script>
</body>
</html>
