/**
 * VirtualX Pro - Module 4: Generator Systems & Power Electronics
 * Interactive circuit simulation and oscilloscope analysis
 */

class GeneratorModule {
    constructor() {
        this.currentWaveform = 'input-voltage';
        this.simulationParams = {
            frequency: 50,
            dutyCycle: 50,
            loadCurrent: 100,
            gateVoltage: 15
        };
        this.circuits = {
            input: {
                title: 'AC Input & Filtering',
                description: 'Three-phase AC input with power factor correction and filtering',
                specifications: {
                    'Input Voltage': '380-480V AC',
                    'Frequency': '50/60 Hz',
                    'Power Factor': '>0.95',
                    'THD': '<5%',
                    'Input Filter': 'EMI/RFI suppression'
                },
                components: [
                    'Three-phase rectifier bridge',
                    'Input EMI filter',
                    'Power factor correction',
                    'DC link capacitors',
                    'Inrush current limiting'
                ]
            },
            igbt: {
                title: 'IGBT Switching Circuit',
                description: 'High-frequency switching using IGBT technology',
                specifications: {
                    'Switching Frequency': '50-100 kHz',
                    'Voltage Rating': '1200-1700V',
                    'Current Rating': '50-200A',
                    'Turn-on Time': '<500ns',
                    'Turn-off Time': '<1μs'
                },
                components: [
                    'IGBT power modules',
                    'Gate driver circuits',
                    'Snubber circuits',
                    'Current sensing',
                    'Protection circuits'
                ]
            },
            transformer: {
                title: 'High-Voltage Transformer',
                description: 'Step-up transformer for X-ray tube voltage',
                specifications: {
                    'Turns Ratio': '1:500 to 1:1000',
                    'Primary Voltage': '400V AC',
                    'Secondary Voltage': '40-150 kV',
                    'Power Rating': '32-100 kW',
                    'Efficiency': '>98%'
                },
                components: [
                    'Ferrite core design',
                    'Primary windings',
                    'Secondary windings',
                    'Insulation system',
                    'Thermal management'
                ]
            },
            rectifier: {
                title: 'Rectifier & Smoothing',
                description: 'High-voltage DC conversion and filtering',
                specifications: {
                    'Rectifier Type': 'Full-wave bridge',
                    'Voltage Ripple': '<1%',
                    'Recovery Time': '<100ns',
                    'Voltage Rating': '150 kV',
                    'Current Rating': '1000 mA'
                },
                components: [
                    'High-voltage diodes',
                    'Smoothing capacitors',
                    'Voltage dividers',
                    'Bleeder resistors',
                    'Overvoltage protection'
                ]
            },
            control: {
                title: 'Control System',
                description: 'Digital control and regulation system',
                specifications: {
                    'Processor': '32-bit ARM Cortex',
                    'Control Loop': 'Digital PID',
                    'Response Time': '<1ms',
                    'Accuracy': '±0.1%',
                    'Interface': 'CAN/Ethernet'
                },
                components: [
                    'Microcontroller unit',
                    'ADC converters',
                    'PWM generators',
                    'Communication interface',
                    'Safety interlocks'
                ]
            },
            feedback: {
                title: 'Feedback Loop',
                description: 'Voltage and current feedback for regulation',
                specifications: {
                    'Voltage Accuracy': '±0.1%',
                    'Current Accuracy': '±0.5%',
                    'Response Time': '<100μs',
                    'Bandwidth': '10 kHz',
                    'Isolation': '10 kV'
                },
                components: [
                    'Voltage sensors',
                    'Current transformers',
                    'Isolation amplifiers',
                    'Signal conditioning',
                    'Compensation networks'
                ]
            }
        };
        this.waveforms = {
            'input-voltage': {
                title: 'Input Voltage',
                frequency: 50,
                amplitude: 230,
                type: 'sine',
                color: '#00ff00'
            },
            'igbt-switching': {
                title: 'IGBT Switching',
                frequency: 50000,
                amplitude: 400,
                type: 'square',
                color: '#ff6600'
            },
            'transformer-primary': {
                title: 'Transformer Primary',
                frequency: 50000,
                amplitude: 400,
                type: 'square',
                color: '#0066ff'
            },
            'output-voltage': {
                title: 'Output Voltage',
                frequency: 50,
                amplitude: 100000,
                type: 'dc',
                color: '#ff0066'
            },
            'current-feedback': {
                title: 'Current Feedback',
                frequency: 50,
                amplitude: 500,
                type: 'sine',
                color: '#ffff00'
            }
        };
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.initializeOscilloscope();
        this.startMetricsUpdate();
        this.updateSimulation();
    }

    setupEventListeners() {
        // Circuit node interactions
        document.querySelectorAll('.circuit-node').forEach(node => {
            node.addEventListener('mouseenter', this.highlightCircuit.bind(this));
            node.addEventListener('mouseleave', this.resetHighlight.bind(this));
        });

        // Oscilloscope button interactions
        document.querySelectorAll('.scope-btn').forEach(btn => {
            btn.addEventListener('click', this.updateActiveScope.bind(this));
        });
    }

    highlightCircuit(event) {
        const node = event.target.closest('.circuit-node');
        const lines = document.querySelectorAll('.circuit-line');
        
        // Highlight all connection lines
        lines.forEach(line => {
            line.style.background = 'var(--napkin-accent)';
            line.style.height = '4px';
            line.style.opacity = '1';
            line.style.boxShadow = '0 0 15px var(--napkin-accent)';
        });
        
        // Add glow effect to node
        node.style.boxShadow = '0 0 30px var(--napkin-accent)';
    }

    resetHighlight() {
        const lines = document.querySelectorAll('.circuit-line');
        const nodes = document.querySelectorAll('.circuit-node');
        
        lines.forEach(line => {
            line.style.background = 'var(--napkin-primary)';
            line.style.height = '3px';
            line.style.opacity = '0.6';
            line.style.boxShadow = 'none';
        });
        
        nodes.forEach(node => {
            node.style.boxShadow = 'var(--napkin-shadow)';
        });
    }

    updateActiveScope(event) {
        const clickedBtn = event.target.closest('.scope-btn');
        if (!clickedBtn) return;
        
        // Update active state
        document.querySelectorAll('.scope-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        clickedBtn.classList.add('active');
    }

    initializeOscilloscope() {
        const canvas = document.getElementById('waveformCanvas');
        if (!canvas) return;
        
        this.ctx = canvas.getContext('2d');
        this.drawWaveform(this.currentWaveform);
    }

    drawWaveform(waveformType) {
        const canvas = document.getElementById('waveformCanvas');
        if (!canvas || !this.ctx) return;
        
        const waveform = this.waveforms[waveformType];
        if (!waveform) return;
        
        // Clear canvas
        this.ctx.clearRect(0, 0, canvas.width, canvas.height);
        
        // Set waveform color
        this.ctx.strokeStyle = waveform.color;
        this.ctx.lineWidth = 2;
        this.ctx.beginPath();
        
        const centerY = canvas.height / 2;
        const amplitude = canvas.height * 0.3;
        const frequency = waveform.frequency;
        const timeScale = waveformType === 'igbt-switching' ? 0.001 : 0.02; // Different time scales
        
        for (let x = 0; x < canvas.width; x++) {
            const t = (x / canvas.width) * timeScale;
            let y = centerY;
            
            switch (waveform.type) {
                case 'sine':
                    y = centerY - amplitude * Math.sin(2 * Math.PI * frequency * t);
                    break;
                case 'square':
                    y = centerY - amplitude * Math.sign(Math.sin(2 * Math.PI * frequency * t));
                    break;
                case 'dc':
                    y = centerY - amplitude * 0.8; // DC level with small ripple
                    y += amplitude * 0.05 * Math.sin(2 * Math.PI * 100 * t); // Small ripple
                    break;
            }
            
            if (x === 0) {
                this.ctx.moveTo(x, y);
            } else {
                this.ctx.lineTo(x, y);
            }
        }
        
        this.ctx.stroke();
        
        // Update scope info
        this.updateScopeInfo(waveform);
    }

    updateScopeInfo(waveform) {
        const scopeInfo = document.querySelector('.scope-info');
        if (!scopeInfo) return;
        
        const timeDiv = waveform.frequency > 1000 ? '100μs' : '5ms';
        const voltDiv = waveform.amplitude > 1000 ? '20kV' : '50V';
        
        scopeInfo.innerHTML = `
            <div>CH1: ${waveform.title}</div>
            <div>Frequency: ${waveform.frequency >= 1000 ? (waveform.frequency/1000) + ' kHz' : waveform.frequency + ' Hz'}</div>
            <div>Amplitude: ${waveform.amplitude >= 1000 ? (waveform.amplitude/1000) + ' kV' : waveform.amplitude + 'V'}</div>
            <div>Time/Div: ${timeDiv}</div>
            <div>Volt/Div: ${voltDiv}</div>
        `;
    }

    updateSimulation() {
        // Update control values
        const frequency = document.getElementById('frequency')?.value || 50;
        const dutyCycle = document.getElementById('dutyCycle')?.value || 50;
        const loadCurrent = document.getElementById('loadCurrent')?.value || 100;
        const gateVoltage = document.getElementById('gateVoltage')?.value || 15;
        
        // Update display values
        if (document.getElementById('frequencyValue')) {
            document.getElementById('frequencyValue').textContent = `${frequency} kHz`;
        }
        if (document.getElementById('dutyCycleValue')) {
            document.getElementById('dutyCycleValue').textContent = `${dutyCycle}%`;
        }
        if (document.getElementById('loadCurrentValue')) {
            document.getElementById('loadCurrentValue').textContent = `${loadCurrent} mA`;
        }
        if (document.getElementById('gateVoltageValue')) {
            document.getElementById('gateVoltageValue').textContent = `${gateVoltage}V`;
        }
        
        // Update simulation parameters
        this.simulationParams = {
            frequency: parseFloat(frequency),
            dutyCycle: parseFloat(dutyCycle),
            loadCurrent: parseFloat(loadCurrent),
            gateVoltage: parseFloat(gateVoltage)
        };
        
        // Update metrics based on simulation
        this.updateMetrics();
    }

    updateMetrics() {
        const params = this.simulationParams;
        
        // Calculate derived metrics
        const powerOutput = (params.loadCurrent * params.gateVoltage * params.dutyCycle) / 100;
        const efficiency = 95 + (params.frequency - 50) * 0.1 - (params.loadCurrent - 100) * 0.01;
        const rippleFactor = 2 - (params.frequency / 100);
        const temperature = 60 + (params.loadCurrent / 10) + (params.frequency / 10);
        const thd = 3 - (params.frequency / 50);
        
        // Update display
        if (document.getElementById('powerOutput')) {
            document.getElementById('powerOutput').textContent = powerOutput.toFixed(1);
        }
        if (document.getElementById('efficiency')) {
            document.getElementById('efficiency').textContent = Math.min(efficiency, 98).toFixed(1);
        }
        if (document.getElementById('rippleFactor')) {
            document.getElementById('rippleFactor').textContent = Math.max(rippleFactor, 0.1).toFixed(1);
        }
        if (document.getElementById('temperature')) {
            document.getElementById('temperature').textContent = Math.min(temperature, 85).toFixed(0);
        }
        if (document.getElementById('frequency')) {
            document.getElementById('frequency').textContent = params.frequency.toFixed(1);
        }
        if (document.getElementById('thd')) {
            document.getElementById('thd').textContent = Math.max(thd, 0.5).toFixed(1);
        }
    }

    startMetricsUpdate() {
        // Update metrics every 2 seconds with small variations
        setInterval(() => {
            const metrics = ['powerOutput', 'efficiency', 'rippleFactor', 'temperature', 'thd'];
            metrics.forEach(metric => {
                const element = document.getElementById(metric);
                if (element) {
                    const currentValue = parseFloat(element.textContent);
                    const variation = (Math.random() - 0.5) * 0.2; // ±0.1 variation
                    const newValue = currentValue + variation;
                    element.textContent = newValue.toFixed(1);
                }
            });
        }, 2000);
    }
}

// Global functions for HTML onclick handlers
function showGeneratorOverview() {
    showModal({
        title: 'High-Frequency Generator Overview',
        content: `
            <div style="padding: 2rem;">
                <div style="text-align: center; margin-bottom: 2rem;">
                    <div style="font-size: 4rem; color: var(--napkin-primary); margin-bottom: 1rem;">
                        <i class="fas fa-microchip"></i>
                    </div>
                    <h3 style="margin-bottom: 1rem;">Advanced Power Electronics System</h3>
                    <p style="color: #6b7280; margin-bottom: 2rem;">
                        High-frequency generators use IGBT switching technology to provide precise, 
                        stable X-ray tube voltage with minimal ripple and maximum efficiency.
                    </p>
                </div>
                
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1.5rem;">
                    <div style="background: #f8fafc; padding: 1.5rem; border-radius: 10px; border-left: 4px solid var(--napkin-primary);">
                        <h4 style="color: var(--napkin-primary); margin-bottom: 1rem;">
                            <i class="fas fa-bolt"></i> Power Conversion
                        </h4>
                        <p style="font-size: 0.9rem; color: #374151;">
                            Converts 3-phase AC input to high-voltage DC output with >95% efficiency
                        </p>
                    </div>
                    
                    <div style="background: #f8fafc; padding: 1.5rem; border-radius: 10px; border-left: 4px solid var(--napkin-accent);">
                        <h4 style="color: var(--napkin-accent); margin-bottom: 1rem;">
                            <i class="fas fa-microchip"></i> IGBT Technology
                        </h4>
                        <p style="font-size: 0.9rem; color: #374151;">
                            50-100kHz switching frequency for precise voltage control and low ripple
                        </p>
                    </div>
                    
                    <div style="background: #f8fafc; padding: 1.5rem; border-radius: 10px; border-left: 4px solid var(--napkin-success);">
                        <h4 style="color: var(--napkin-success); margin-bottom: 1rem;">
                            <i class="fas fa-sync"></i> Feedback Control
                        </h4>
                        <p style="font-size: 0.9rem; color: #374151;">
                            Digital PID control with <1ms response time for stable operation
                        </p>
                    </div>
                    
                    <div style="background: #f8fafc; padding: 1.5rem; border-radius: 10px; border-left: 4px solid var(--napkin-warning);">
                        <h4 style="color: var(--napkin-warning); margin-bottom: 1rem;">
                            <i class="fas fa-shield-alt"></i> Protection
                        </h4>
                        <p style="font-size: 0.9rem; color: #374151;">
                            Comprehensive protection against overcurrent, overvoltage, and faults
                        </p>
                    </div>
                </div>
                
                <div style="text-align: center; margin-top: 2rem;">
                    <button onclick="startCircuitAnalysis()" style="background: var(--napkin-primary); color: white; border: none; padding: 1rem 2rem; border-radius: 25px; cursor: pointer; font-weight: 500; margin-right: 1rem;">
                        <i class="fas fa-play"></i> Circuit Analysis
                    </button>
                    <button onclick="openOscilloscope()" style="background: var(--napkin-accent); color: white; border: none; padding: 1rem 2rem; border-radius: 25px; cursor: pointer; font-weight: 500;">
                        <i class="fas fa-wave-square"></i> Oscilloscope
                    </button>
                </div>
            </div>
        `
    });
}

function showCircuit(circuitId) {
    const circuit = window.generatorModule.circuits[circuitId];
    if (!circuit) return;
    
    showModal({
        title: circuit.title,
        content: `
            <div style="padding: 2rem;">
                <p style="font-size: 1.1rem; margin-bottom: 2rem; color: #374151;">
                    ${circuit.description}
                </p>
                
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem; margin-bottom: 2rem;">
                    <div>
                        <h4 style="color: var(--napkin-primary); margin-bottom: 1rem;">Technical Specifications</h4>
                        <div style="background: #f8fafc; padding: 1rem; border-radius: 8px;">
                            ${Object.entries(circuit.specifications).map(([key, value]) => `
                                <div style="display: flex; justify-content: space-between; padding: 0.5rem 0; border-bottom: 1px solid #e5e7eb;">
                                    <span style="font-weight: 500;">${key}:</span>
                                    <span style="color: var(--napkin-primary);">${value}</span>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                    
                    <div>
                        <h4 style="color: var(--napkin-primary); margin-bottom: 1rem;">Key Components</h4>
                        <div style="background: #f8fafc; padding: 1rem; border-radius: 8px;">
                            ${circuit.components.map(comp => `
                                <div style="display: flex; align-items: center; gap: 0.75rem; padding: 0.5rem 0;">
                                    <div style="width: 8px; height: 8px; background: var(--napkin-primary); border-radius: 50%;"></div>
                                    <span>${comp}</span>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                </div>
                
                <div style="text-align: center;">
                    <button onclick="analyzeCircuit('${circuitId}')" style="background: var(--napkin-primary); color: white; border: none; padding: 0.75rem 2rem; border-radius: 25px; cursor: pointer; font-weight: 500; margin-right: 1rem;">
                        <i class="fas fa-search"></i> Analyze Circuit
                    </button>
                    <button onclick="simulateCircuit('${circuitId}')" style="background: var(--napkin-accent); color: white; border: none; padding: 0.75rem 2rem; border-radius: 25px; cursor: pointer; font-weight: 500;">
                        <i class="fas fa-play"></i> Run Simulation
                    </button>
                </div>
            </div>
        `
    });
}

function showWaveform(waveformType) {
    window.generatorModule.currentWaveform = waveformType;
    window.generatorModule.drawWaveform(waveformType);
}

function updateSimulation() {
    if (window.generatorModule) {
        window.generatorModule.updateSimulation();
    }
}

function startCircuitAnalysis() {
    alert('Circuit Analysis Tool will launch in the full platform with SPICE simulation capabilities.');
    closeModal();
}

function openOscilloscope() {
    closeModal();
    document.querySelector('.oscilloscope-section').scrollIntoView({ behavior: 'smooth' });
}

function analyzeCircuit(circuitId) {
    alert(`Detailed circuit analysis for ${circuitId} will launch in the full platform.`);
    closeModal();
}

function simulateCircuit(circuitId) {
    alert(`Circuit simulation for ${circuitId} will start in the full platform.`);
    closeModal();
}

function showModal({ title, content }) {
    const modal = document.createElement('div');
    modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.8);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10000;
        animation: fadeIn 0.3s ease;
    `;
    
    modal.innerHTML = `
        <div style="background: white; border-radius: 15px; max-width: 800px; width: 90%; max-height: 80%; overflow-y: auto; position: relative;">
            <div style="background: var(--napkin-gradient); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;">
                <h3 style="margin: 0; font-size: 1.3rem;">${title}</h3>
                <button onclick="closeModal()" style="position: absolute; top: 1rem; right: 1rem; background: none; border: none; color: white; font-size: 1.5rem; cursor: pointer;">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div>${content}</div>
        </div>
    `;
    
    document.body.appendChild(modal);
    window.currentModal = modal;
    
    // Close on outside click
    modal.addEventListener('click', (e) => {
        if (e.target === modal) closeModal();
    });
}

function closeModal() {
    if (window.currentModal) {
        window.currentModal.remove();
        window.currentModal = null;
    }
}

// Add CSS for animations
const style = document.createElement('style');
style.textContent = `
    @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
    }
`;
document.head.appendChild(style);

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.generatorModule = new GeneratorModule();
});
