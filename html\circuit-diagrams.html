<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>X-ray System Electrical Circuit Diagrams | Training Module</title>
    <link rel="stylesheet" href="../css/main.css">
    <link rel="stylesheet" href="../css/circuit-diagrams.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <header>
        <div class="container">
            <h1>X-ray System Electrical Circuit Diagrams</h1>
            <p>Understanding the electrical circuits that power X-ray systems</p>
        </div>
    </header>

    <nav>
        <div class="container">
            <ul>
                <li><a href="../index.html">Home</a></li>
                <li><a href="#overview" class="active">Overview</a></li>
                <li><a href="#high-voltage">High Voltage</a></li>
                <li><a href="#filament">Filament Circuit</a></li>
                <li><a href="#control">Control Circuit</a></li>
                <li><a href="#detector">Detector Circuit</a></li>
            </ul>
        </div>
    </nav>

    <main>
        <section id="overview" class="overview">
            <div class="container">
                <h2>Electrical Circuit Diagrams Overview</h2>
                <p>Electrical circuit diagrams are essential tools for biomedical engineers to understand the detailed electrical connections and components within X-ray systems. These diagrams show the specific electronic components, their connections, and the signal paths that enable the system to function.</p>
                
                <div class="info-box">
                    <div class="info-icon"><i class="fas fa-info-circle"></i></div>
                    <div class="info-content">
                        <h3>Why Circuit Diagrams Matter</h3>
                        <p>For biomedical engineers, understanding circuit diagrams is crucial for:</p>
                        <ul>
                            <li>Troubleshooting electrical faults</li>
                            <li>Performing component-level repairs</li>
                            <li>Understanding signal flow and processing</li>
                            <li>Identifying potential failure points</li>
                            <li>Ensuring proper electrical safety</li>
                        </ul>
                    </div>
                </div>
                
                <div class="circuit-symbols">
                    <h3>Common Circuit Symbols in X-ray Systems</h3>
                    <div class="symbols-grid">
                        <div class="symbol-item">
                            <div class="symbol">
                                <img src="../images/resistor-symbol.png" alt="Resistor Symbol" onerror="this.src='https://via.placeholder.com/50x30?text=R'">
                            </div>
                            <div class="symbol-name">Resistor</div>
                        </div>
                        <div class="symbol-item">
                            <div class="symbol">
                                <img src="../images/capacitor-symbol.png" alt="Capacitor Symbol" onerror="this.src='https://via.placeholder.com/50x30?text=C'">
                            </div>
                            <div class="symbol-name">Capacitor</div>
                        </div>
                        <div class="symbol-item">
                            <div class="symbol">
                                <img src="../images/inductor-symbol.png" alt="Inductor Symbol" onerror="this.src='https://via.placeholder.com/50x30?text=L'">
                            </div>
                            <div class="symbol-name">Inductor</div>
                        </div>
                        <div class="symbol-item">
                            <div class="symbol">
                                <img src="../images/diode-symbol.png" alt="Diode Symbol" onerror="this.src='https://via.placeholder.com/50x30?text=D'">
                            </div>
                            <div class="symbol-name">Diode</div>
                        </div>
                        <div class="symbol-item">
                            <div class="symbol">
                                <img src="../images/transformer-symbol.png" alt="Transformer Symbol" onerror="this.src='https://via.placeholder.com/50x30?text=XFMR'">
                            </div>
                            <div class="symbol-name">Transformer</div>
                        </div>
                        <div class="symbol-item">
                            <div class="symbol">
                                <img src="../images/transistor-symbol.png" alt="Transistor Symbol" onerror="this.src='https://via.placeholder.com/50x30?text=T'">
                            </div>
                            <div class="symbol-name">Transistor</div>
                        </div>
                        <div class="symbol-item">
                            <div class="symbol">
                                <img src="../images/switch-symbol.png" alt="Switch Symbol" onerror="this.src='https://via.placeholder.com/50x30?text=SW'">
                            </div>
                            <div class="symbol-name">Switch</div>
                        </div>
                        <div class="symbol-item">
                            <div class="symbol">
                                <img src="../images/relay-symbol.png" alt="Relay Symbol" onerror="this.src='https://via.placeholder.com/50x30?text=RLY'">
                            </div>
                            <div class="symbol-name">Relay</div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section id="high-voltage" class="circuit-section">
            <div class="container">
                <h2>High Voltage Generator Circuit</h2>
                <p>The high voltage generator circuit is responsible for producing the high voltage (typically 40-150 kV) needed to accelerate electrons in the X-ray tube.</p>
                
                <div class="circuit-container">
                    <div class="circuit-diagram" id="high-voltage-diagram">
                        <img src="../images/high-voltage-circuit.png" alt="High Voltage Generator Circuit" onerror="this.src='https://via.placeholder.com/800x500?text=High+Voltage+Generator+Circuit'">
                    </div>
                    
                    <div class="circuit-details">
                        <div class="circuit-tabs">
                            <button class="tab-btn active" data-tab="components">Components</button>
                            <button class="tab-btn" data-tab="operation">Operation</button>
                            <button class="tab-btn" data-tab="troubleshooting">Troubleshooting</button>
                        </div>
                        
                        <div class="tab-content">
                            <div class="tab-pane active" id="components-tab">
                                <h3>Key Components</h3>
                                <ul>
                                    <li><strong>Input Transformer:</strong> Steps down line voltage for the inverter</li>
                                    <li><strong>Inverter Circuit:</strong> Converts DC to high-frequency AC</li>
                                    <li><strong>High Voltage Transformer:</strong> Steps up voltage to required kV level</li>
                                    <li><strong>Rectifier Assembly:</strong> Converts AC to DC for X-ray tube</li>
                                    <li><strong>Voltage Multiplier:</strong> Increases voltage in some designs</li>
                                    <li><strong>Filter Capacitors:</strong> Smooth the high voltage output</li>
                                    <li><strong>Voltage Divider:</strong> Provides feedback for voltage regulation</li>
                                    <li><strong>Control Circuitry:</strong> Regulates output based on kV selection</li>
                                </ul>
                            </div>
                            
                            <div class="tab-pane" id="operation-tab">
                                <h3>Circuit Operation</h3>
                                <ol>
                                    <li>Line voltage (220-240V AC) enters the circuit</li>
                                    <li>Input transformer and rectifier convert to DC</li>
                                    <li>Inverter converts DC to high-frequency AC (typically 5-50 kHz)</li>
                                    <li>High-frequency transformer steps up voltage</li>
                                    <li>Rectifier assembly converts high voltage AC to DC</li>
                                    <li>Filter capacitors smooth the output voltage</li>
                                    <li>Voltage divider provides feedback for regulation</li>
                                    <li>Control circuitry adjusts output based on selected kV</li>
                                </ol>
                                <p>Modern generators use high-frequency inverter technology for more efficient and precise voltage control compared to older designs that used line-frequency transformers.</p>
                            </div>
                            
                            <div class="tab-pane" id="troubleshooting-tab">
                                <h3>Troubleshooting Guide</h3>
                                <div class="troubleshooting-item">
                                    <h4>Symptom: No High Voltage Output</h4>
                                    <ul>
                                        <li>Check input power and fuses</li>
                                        <li>Verify inverter operation</li>
                                        <li>Test high voltage transformer</li>
                                        <li>Check rectifier diodes for failure</li>
                                        <li>Inspect control circuit for errors</li>
                                    </ul>
                                </div>
                                <div class="troubleshooting-item">
                                    <h4>Symptom: Unstable kV</h4>
                                    <ul>
                                        <li>Check voltage feedback circuit</li>
                                        <li>Inspect filter capacitors</li>
                                        <li>Verify control circuit operation</li>
                                        <li>Check for input power fluctuations</li>
                                    </ul>
                                </div>
                                <div class="troubleshooting-item">
                                    <h4>Symptom: Overheating</h4>
                                    <ul>
                                        <li>Check cooling system</li>
                                        <li>Verify proper component spacing</li>
                                        <li>Inspect for shorted components</li>
                                        <li>Check for excessive load</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section id="filament" class="circuit-section">
            <div class="container">
                <h2>X-ray Tube Filament Circuit</h2>
                <p>The filament circuit provides controlled heating current to the X-ray tube cathode filament, which emits electrons through thermionic emission.</p>
                
                <div class="circuit-container">
                    <div class="circuit-diagram" id="filament-diagram">
                        <img src="../images/filament-circuit.png" alt="Filament Circuit" onerror="this.src='https://via.placeholder.com/800x500?text=Filament+Circuit'">
                    </div>
                    
                    <div class="circuit-details">
                        <div class="circuit-tabs">
                            <button class="tab-btn active" data-tab="components">Components</button>
                            <button class="tab-btn" data-tab="operation">Operation</button>
                            <button class="tab-btn" data-tab="troubleshooting">Troubleshooting</button>
                        </div>
                        
                        <div class="tab-content">
                            <div class="tab-pane active" id="filament-components-tab">
                                <h3>Key Components</h3>
                                <ul>
                                    <li><strong>Filament Transformer:</strong> Provides isolation and voltage adjustment</li>
                                    <li><strong>Current Regulator:</strong> Controls filament current for consistent emission</li>
                                    <li><strong>Boost Circuit:</strong> Provides initial higher current for filament warm-up</li>
                                    <li><strong>Current Feedback:</strong> Monitors actual filament current</li>
                                    <li><strong>Protection Circuit:</strong> Prevents filament burnout</li>
                                    <li><strong>Control Interface:</strong> Receives commands from main control system</li>
                                </ul>
                            </div>
                            
                            <div class="tab-pane" id="filament-operation-tab">
                                <h3>Circuit Operation</h3>
                                <ol>
                                    <li>Control system sends mA selection to filament circuit</li>
                                    <li>Boost circuit provides initial higher current for warm-up</li>
                                    <li>Current regulator adjusts to maintain proper filament temperature</li>
                                    <li>Feedback circuit monitors actual current flow</li>
                                    <li>Protection circuit prevents overcurrent conditions</li>
                                    <li>During exposure, filament current is precisely maintained to achieve selected mA</li>
                                </ol>
                                <p>The filament current directly affects the tube current (mA) by controlling the number of electrons emitted from the cathode. Precise control is essential for consistent image quality and tube longevity.</p>
                            </div>
                            
                            <div class="tab-pane" id="filament-troubleshooting-tab">
                                <h3>Troubleshooting Guide</h3>
                                <div class="troubleshooting-item">
                                    <h4>Symptom: No Tube Current (mA)</h4>
                                    <ul>
                                        <li>Check filament transformer output</li>
                                        <li>Verify filament continuity</li>
                                        <li>Test current regulator circuit</li>
                                        <li>Check control interface signals</li>
                                    </ul>
                                </div>
                                <div class="troubleshooting-item">
                                    <h4>Symptom: Unstable Tube Current</h4>
                                    <ul>
                                        <li>Check feedback circuit</li>
                                        <li>Inspect regulator components</li>
                                        <li>Verify filament condition</li>
                                        <li>Test for interference in control signals</li>
                                    </ul>
                                </div>
                                <div class="troubleshooting-item">
                                    <h4>Symptom: Filament Failure</h4>
                                    <ul>
                                        <li>Check for overcurrent conditions</li>
                                        <li>Verify protection circuit operation</li>
                                        <li>Inspect for voltage spikes</li>
                                        <li>Check for tube aging (normal end-of-life)</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section id="control" class="circuit-section">
            <div class="container">
                <h2>Control Circuit</h2>
                <p>The control circuit coordinates all aspects of X-ray system operation, including exposure timing, parameter selection, and safety interlocks.</p>
                
                <div class="circuit-container">
                    <div class="circuit-diagram" id="control-diagram">
                        <img src="../images/control-circuit.png" alt="Control Circuit" onerror="this.src='https://via.placeholder.com/800x500?text=Control+Circuit'">
                    </div>
                    
                    <div class="circuit-details">
                        <h3>Key Components</h3>
                        <ul>
                            <li><strong>Microprocessor/CPU:</strong> Central processing unit that coordinates all functions</li>
                            <li><strong>Memory (RAM/ROM):</strong> Stores program code and operational data</li>
                            <li><strong>Input/Output Interfaces:</strong> Connect to various system components</li>
                            <li><strong>Analog-to-Digital Converters:</strong> Convert sensor signals to digital data</li>
                            <li><strong>Digital-to-Analog Converters:</strong> Convert digital commands to analog control signals</li>
                            <li><strong>Safety Interlock Circuits:</strong> Monitor system safety conditions</li>
                            <li><strong>Timing Circuits:</strong> Control exposure duration</li>
                            <li><strong>Communication Interfaces:</strong> Connect to user interface and other subsystems</li>
                        </ul>
                        
                        <h3>Circuit Operation</h3>
                        <p>The control circuit is the brain of the X-ray system, coordinating all operations:</p>
                        <ol>
                            <li>Receives user input for technique factors (kV, mA, time)</li>
                            <li>Verifies all safety interlocks are satisfied</li>
                            <li>Sends appropriate signals to high voltage and filament circuits</li>
                            <li>Controls exposure timing with precision</li>
                            <li>Monitors system parameters during exposure</li>
                            <li>Terminates exposure if abnormal conditions are detected</li>
                            <li>Communicates with detector and image processing systems</li>
                        </ol>
                        
                        <div class="common-issues">
                            <h4>Common Issues</h4>
                            <ul>
                                <li>Software/firmware errors</li>
                                <li>Communication failures between subsystems</li>
                                <li>Sensor calibration drift</li>
                                <li>Timing circuit failures</li>
                                <li>Safety interlock malfunctions</li>
                                <li>Power supply issues affecting logic circuits</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        
        <section class="navigation-buttons">
            <div class="container">
                <div class="nav-buttons">
                    <a href="block-diagrams.html" class="btn"><i class="fas fa-arrow-left"></i> Previous: Block Diagrams</a>
                    <a href="quality-control.html" class="btn">Next: Quality Control <i class="fas fa-arrow-right"></i></a>
                </div>
            </div>
        </section>
    </main>

    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>X-ray System Training</h3>
                    <p>A comprehensive training platform for biomedical engineers specializing in X-ray systems.</p>
                </div>
                <div class="footer-section">
                    <h3>Quick Links</h3>
                    <ul>
                        <li><a href="../index.html">Home</a></li>
                        <li><a href="#overview">Overview</a></li>
                        <li><a href="#high-voltage">High Voltage</a></li>
                        <li><a href="#filament">Filament Circuit</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h3>Contact</h3>
                    <p>Email: <EMAIL></p>
                    <p>Phone: +****************</p>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2025 X-ray System Interactive Training Module. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script src="../js/main.js"></script>
    <script src="../js/circuit-diagrams.js"></script>
</body>
</html>
