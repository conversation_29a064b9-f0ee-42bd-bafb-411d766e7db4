/**
 * VirtualX Pro - Module 3: Safety & Radiation Protection
 * Interactive safety training and dose calculations
 */

class SafetyModule {
    constructor() {
        this.radiationLevel = 100; // mrem/hr
        this.principles = {
            time: {
                title: 'Time Principle',
                description: 'Minimize exposure time to reduce total dose',
                details: [
                    'Dose = Dose Rate × Time',
                    'Reduce procedure time',
                    'Use automatic exposure control',
                    'Optimize technique factors',
                    'Minimize retakes'
                ],
                formula: 'Total Dose = Dose Rate (mrem/hr) × Time (hours)'
            },
            distance: {
                title: 'Distance Principle', 
                description: 'Increase distance to reduce exposure (inverse square law)',
                details: [
                    'Dose ∝ 1/Distance²',
                    'Double distance = 1/4 dose',
                    'Use remote controls',
                    'Maximize operator distance',
                    'Use long exposure cords'
                ],
                formula: 'Dose₂ = Dose₁ × (Distance₁/Distance₂)²'
            },
            shielding: {
                title: 'Shielding Principle',
                description: 'Use appropriate shielding materials to attenuate radiation',
                details: [
                    'Lead aprons and thyroid shields',
                    'Structural shielding design',
                    'Mobile lead barriers',
                    'Beam limitation devices',
                    'Patient shielding protocols'
                ],
                formula: 'I = I₀ × e^(-μx) where μ = attenuation coefficient'
            }
        };
        this.emergencyProcedures = {
            'radiation-leak': {
                title: 'Radiation Leak Response',
                steps: [
                    'Immediately evacuate the area',
                    'Secure the area and post warning signs',
                    'Notify radiation safety officer',
                    'Contact regulatory authorities',
                    'Document the incident',
                    'Conduct radiation surveys',
                    'Investigate root cause',
                    'Implement corrective actions'
                ],
                priority: 'CRITICAL',
                timeframe: 'Immediate'
            },
            'equipment-failure': {
                title: 'Equipment Failure Protocol',
                steps: [
                    'Activate emergency stop',
                    'Ensure patient safety',
                    'Clear the examination room',
                    'Tag equipment as out of service',
                    'Notify service personnel',
                    'Document malfunction details',
                    'Complete incident report',
                    'Arrange backup equipment'
                ],
                priority: 'HIGH',
                timeframe: 'Immediate'
            },
            'exposure-incident': {
                title: 'Exposure Incident Response',
                steps: [
                    'Provide immediate medical attention',
                    'Estimate radiation dose received',
                    'Notify radiation safety officer',
                    'Document exposure circumstances',
                    'Conduct dose assessment',
                    'Report to regulatory authorities',
                    'Arrange medical follow-up',
                    'Review and improve procedures'
                ],
                priority: 'CRITICAL',
                timeframe: 'Immediate'
            },
            'fire-emergency': {
                title: 'Fire Emergency Protocol',
                steps: [
                    'Activate fire alarm system',
                    'Evacuate all personnel',
                    'Call emergency services',
                    'Shut down X-ray equipment',
                    'Secure radioactive materials',
                    'Meet at assembly point',
                    'Account for all personnel',
                    'Coordinate with fire department'
                ],
                priority: 'CRITICAL',
                timeframe: 'Immediate'
            }
        };
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.initializeZoneVisualizer();
        this.calculateDose();
    }

    setupEventListeners() {
        // ALARA node interactions
        document.querySelectorAll('.principle-node').forEach(node => {
            node.addEventListener('mouseenter', this.highlightPrinciple.bind(this));
            node.addEventListener('mouseleave', this.resetHighlight.bind(this));
        });

        // Calculator input changes
        ['activity', 'distance', 'shielding', 'thickness'].forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.addEventListener('input', this.calculateDose.bind(this));
            }
        });
    }

    highlightPrinciple(event) {
        const node = event.target.closest('.principle-node');
        const lines = document.querySelectorAll('.alara-line');
        
        // Highlight all connection lines
        lines.forEach(line => {
            line.style.background = 'var(--napkin-accent)';
            line.style.height = '4px';
            line.style.opacity = '1';
            line.style.boxShadow = '0 0 15px var(--napkin-accent)';
        });
        
        // Add glow effect to node
        node.style.boxShadow = '0 0 30px var(--napkin-accent)';
    }

    resetHighlight() {
        const lines = document.querySelectorAll('.alara-line');
        const nodes = document.querySelectorAll('.principle-node');
        
        lines.forEach(line => {
            line.style.background = 'var(--napkin-primary)';
            line.style.height = '3px';
            line.style.opacity = '0.6';
            line.style.boxShadow = 'none';
        });
        
        nodes.forEach(node => {
            node.style.boxShadow = 'var(--napkin-shadow)';
        });
    }

    initializeZoneVisualizer() {
        // Add interactive elements to radiation zones
        const zones = document.querySelectorAll('.radiation-zone');
        zones.forEach(zone => {
            zone.addEventListener('click', this.showZoneInfo.bind(this));
        });
    }

    showZoneInfo(event) {
        const zone = event.target;
        let zoneType = '';
        let description = '';
        
        if (zone.classList.contains('zone-controlled')) {
            zoneType = 'Controlled Area';
            description = 'High radiation area (>100 mrem/hr) requiring special access controls and monitoring';
        } else if (zone.classList.contains('zone-supervised')) {
            zoneType = 'Supervised Area';
            description = 'Moderate radiation area (2-100 mrem/hr) requiring supervision and monitoring';
        } else if (zone.classList.contains('zone-unrestricted')) {
            zoneType = 'Unrestricted Area';
            description = 'Low radiation area (<2 mrem/hr) safe for general public access';
        }
        
        if (zoneType) {
            this.showZoneModal(zoneType, description);
        }
    }

    showZoneModal(zoneType, description) {
        showModal({
            title: zoneType,
            content: `
                <div style="padding: 2rem; text-align: center;">
                    <div style="font-size: 3rem; color: var(--napkin-primary); margin-bottom: 1rem;">
                        <i class="fas fa-radiation"></i>
                    </div>
                    <p style="font-size: 1.1rem; margin-bottom: 2rem; color: #374151;">
                        ${description}
                    </p>
                    <div style="background: #f8fafc; padding: 1.5rem; border-radius: 10px; text-align: left;">
                        <h4 style="color: var(--napkin-primary); margin-bottom: 1rem;">Zone Requirements:</h4>
                        <ul style="list-style: none; padding: 0;">
                            <li style="margin-bottom: 0.5rem;"><i class="fas fa-check" style="color: var(--napkin-success); margin-right: 0.5rem;"></i> Radiation monitoring</li>
                            <li style="margin-bottom: 0.5rem;"><i class="fas fa-check" style="color: var(--napkin-success); margin-right: 0.5rem;"></i> Access controls</li>
                            <li style="margin-bottom: 0.5rem;"><i class="fas fa-check" style="color: var(--napkin-success); margin-right: 0.5rem;"></i> Warning signage</li>
                            <li style="margin-bottom: 0.5rem;"><i class="fas fa-check" style="color: var(--napkin-success); margin-right: 0.5rem;"></i> Personnel dosimetry</li>
                        </ul>
                    </div>
                </div>
            `
        });
    }

    calculateDose() {
        const activity = parseFloat(document.getElementById('activity')?.value || 100);
        const distance = parseFloat(document.getElementById('distance')?.value || 2);
        const shielding = document.getElementById('shielding')?.value || 'none';
        const thickness = parseFloat(document.getElementById('thickness')?.value || 0);
        
        // Calculate unshielded dose rate using inverse square law
        const unshieldedDose = (activity * 6) / (distance * distance); // Simplified calculation
        
        // Calculate attenuation based on shielding material
        let attenuationFactor = 1;
        if (shielding !== 'none' && thickness > 0) {
            const attenuationCoefficients = {
                'lead': 0.5,      // mm⁻¹
                'concrete': 0.05, // mm⁻¹
                'steel': 0.1      // mm⁻¹
            };
            
            const mu = attenuationCoefficients[shielding] || 0;
            attenuationFactor = Math.exp(-mu * thickness);
        }
        
        const shieldedDose = unshieldedDose * attenuationFactor;
        
        // Update display
        document.getElementById('unshielded-dose').textContent = `${unshieldedDose.toFixed(1)} mrem/hr`;
        document.getElementById('shielded-dose').textContent = `${shieldedDose.toFixed(1)} mrem/hr`;
        document.getElementById('attenuation-factor').textContent = attenuationFactor.toFixed(3);
        
        // Determine safety classification
        let safetyClass = '';
        let recommendations = '';
        
        if (shieldedDose < 2) {
            safetyClass = 'Unrestricted Area';
            recommendations = 'Dose rate is acceptable for unrestricted public access.';
        } else if (shieldedDose < 100) {
            safetyClass = 'Supervised Area';
            recommendations = 'Area requires supervision and radiation monitoring. Consider additional shielding for unrestricted access.';
        } else {
            safetyClass = 'Controlled Area';
            recommendations = 'High radiation area requiring strict access controls, monitoring, and additional shielding.';
        }
        
        document.getElementById('safety-class').textContent = safetyClass;
        document.getElementById('recommendations').textContent = recommendations;
    }
}

// Global functions for HTML onclick handlers
function showALARAOverview() {
    showModal({
        title: 'ALARA Principles Overview',
        content: `
            <div style="padding: 2rem;">
                <div style="text-align: center; margin-bottom: 2rem;">
                    <div style="font-size: 4rem; color: var(--napkin-primary); margin-bottom: 1rem;">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h3 style="margin-bottom: 1rem;">As Low As Reasonably Achievable</h3>
                    <p style="color: #6b7280; margin-bottom: 2rem;">
                        ALARA is the fundamental principle of radiation protection, requiring that radiation exposures 
                        be kept as low as reasonably achievable, taking into account economic and social factors.
                    </p>
                </div>
                
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1.5rem;">
                    <div style="background: #f8fafc; padding: 1.5rem; border-radius: 10px; border-left: 4px solid var(--napkin-primary);">
                        <h4 style="color: var(--napkin-primary); margin-bottom: 1rem;">
                            <i class="fas fa-clock"></i> Time
                        </h4>
                        <p style="font-size: 0.9rem; color: #374151;">
                            Minimize exposure time through efficient procedures and automatic exposure controls
                        </p>
                    </div>
                    
                    <div style="background: #f8fafc; padding: 1.5rem; border-radius: 10px; border-left: 4px solid var(--napkin-accent);">
                        <h4 style="color: var(--napkin-accent); margin-bottom: 1rem;">
                            <i class="fas fa-ruler"></i> Distance
                        </h4>
                        <p style="font-size: 0.9rem; color: #374151;">
                            Maximize distance from radiation source using inverse square law principles
                        </p>
                    </div>
                    
                    <div style="background: #f8fafc; padding: 1.5rem; border-radius: 10px; border-left: 4px solid var(--napkin-success);">
                        <h4 style="color: var(--napkin-success); margin-bottom: 1rem;">
                            <i class="fas fa-shield-alt"></i> Shielding
                        </h4>
                        <p style="font-size: 0.9rem; color: #374151;">
                            Use appropriate shielding materials to attenuate radiation exposure
                        </p>
                    </div>
                </div>
                
                <div style="text-align: center; margin-top: 2rem;">
                    <button onclick="startALARAQuiz()" style="background: var(--napkin-primary); color: white; border: none; padding: 1rem 2rem; border-radius: 25px; cursor: pointer; font-weight: 500; margin-right: 1rem;">
                        <i class="fas fa-question-circle"></i> ALARA Quiz
                    </button>
                    <button onclick="openDoseCalculator()" style="background: var(--napkin-accent); color: white; border: none; padding: 1rem 2rem; border-radius: 25px; cursor: pointer; font-weight: 500;">
                        <i class="fas fa-calculator"></i> Dose Calculator
                    </button>
                </div>
            </div>
        `
    });
}

function showPrinciple(principleId) {
    const principle = window.safetyModule.principles[principleId];
    if (!principle) return;
    
    showModal({
        title: principle.title,
        content: `
            <div style="padding: 2rem;">
                <p style="font-size: 1.1rem; margin-bottom: 2rem; color: #374151;">
                    ${principle.description}
                </p>
                
                <div style="background: #f8fafc; padding: 1.5rem; border-radius: 10px; margin-bottom: 2rem;">
                    <h4 style="color: var(--napkin-primary); margin-bottom: 1rem;">Mathematical Formula:</h4>
                    <div style="background: white; padding: 1rem; border-radius: 8px; font-family: monospace; font-size: 1.1rem; color: var(--napkin-primary); text-align: center;">
                        ${principle.formula}
                    </div>
                </div>
                
                <div style="margin-bottom: 2rem;">
                    <h4 style="color: var(--napkin-primary); margin-bottom: 1rem;">Key Implementation Points:</h4>
                    <div style="display: grid; gap: 0.75rem;">
                        ${principle.details.map(detail => `
                            <div style="display: flex; align-items: center; gap: 0.75rem; padding: 0.75rem; background: #f8fafc; border-radius: 8px;">
                                <div style="width: 8px; height: 8px; background: var(--napkin-primary); border-radius: 50%;"></div>
                                <span>${detail}</span>
                            </div>
                        `).join('')}
                    </div>
                </div>
                
                <div style="text-align: center;">
                    <button onclick="practiceCalculation('${principleId}')" style="background: var(--napkin-primary); color: white; border: none; padding: 0.75rem 2rem; border-radius: 25px; cursor: pointer; font-weight: 500; margin-right: 1rem;">
                        <i class="fas fa-calculator"></i> Practice Calculation
                    </button>
                    <button onclick="viewExamples('${principleId}')" style="background: var(--napkin-accent); color: white; border: none; padding: 0.75rem 2rem; border-radius: 25px; cursor: pointer; font-weight: 500;">
                        <i class="fas fa-lightbulb"></i> View Examples
                    </button>
                </div>
            </div>
        `
    });
}

function showEmergencyProcedure(procedureId) {
    const procedure = window.safetyModule.emergencyProcedures[procedureId];
    if (!procedure) return;
    
    const priorityColors = {
        'CRITICAL': 'var(--napkin-primary)',
        'HIGH': 'var(--napkin-warning)',
        'MEDIUM': 'var(--napkin-accent)'
    };
    
    showModal({
        title: procedure.title,
        content: `
            <div style="padding: 2rem;">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 2rem;">
                    <div style="background: ${priorityColors[procedure.priority]}; color: white; padding: 0.5rem 1rem; border-radius: 20px; font-weight: 600;">
                        ${procedure.priority} PRIORITY
                    </div>
                    <div style="color: #6b7280;">
                        <i class="fas fa-clock"></i> ${procedure.timeframe}
                    </div>
                </div>
                
                <h4 style="color: var(--napkin-primary); margin-bottom: 1rem;">Emergency Response Steps:</h4>
                <div style="background: #f8fafc; padding: 1.5rem; border-radius: 10px;">
                    ${procedure.steps.map((step, index) => `
                        <div style="display: flex; align-items: flex-start; gap: 1rem; margin-bottom: 1rem; padding: 1rem; background: white; border-radius: 8px;">
                            <div style="width: 30px; height: 30px; background: var(--napkin-primary); color: white; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: bold; flex-shrink: 0;">
                                ${index + 1}
                            </div>
                            <span style="flex: 1;">${step}</span>
                        </div>
                    `).join('')}
                </div>
                
                <div style="text-align: center; margin-top: 2rem;">
                    <button onclick="startEmergencyDrill('${procedureId}')" style="background: var(--napkin-primary); color: white; border: none; padding: 1rem 2rem; border-radius: 25px; cursor: pointer; font-weight: 500; margin-right: 1rem;">
                        <i class="fas fa-play"></i> Practice Drill
                    </button>
                    <button onclick="downloadProcedure('${procedureId}')" style="background: var(--napkin-accent); color: white; border: none; padding: 1rem 2rem; border-radius: 25px; cursor: pointer; font-weight: 500;">
                        <i class="fas fa-download"></i> Download Guide
                    </button>
                </div>
            </div>
        `
    });
}

function adjustRadiation(action) {
    const currentLevel = window.safetyModule.radiationLevel;
    
    if (action === 'increase') {
        window.safetyModule.radiationLevel = Math.min(currentLevel * 1.5, 1000);
    } else {
        window.safetyModule.radiationLevel = Math.max(currentLevel * 0.7, 10);
    }
    
    // Update zone visualization (simplified)
    const zones = document.querySelectorAll('.radiation-zone');
    const scaleFactor = window.safetyModule.radiationLevel / 100;
    
    zones.forEach(zone => {
        const currentWidth = parseInt(zone.style.width || zone.offsetWidth);
        const newWidth = Math.max(currentWidth * scaleFactor, 50);
        zone.style.width = `${newWidth}px`;
        zone.style.height = `${newWidth}px`;
    });
    
    // Show notification
    const notification = document.createElement('div');
    notification.style.cssText = `
        position: fixed;
        top: 2rem;
        right: 2rem;
        background: var(--napkin-primary);
        color: white;
        padding: 1rem 2rem;
        border-radius: 8px;
        z-index: 10001;
        animation: slideIn 0.3s ease;
    `;
    notification.textContent = `Radiation level ${action}d to ${window.safetyModule.radiationLevel.toFixed(0)} mrem/hr`;
    document.body.appendChild(notification);
    
    setTimeout(() => notification.remove(), 3000);
}

function calculateDose() {
    window.safetyModule.calculateDose();
}

function startALARAQuiz() {
    alert('ALARA Principles Quiz will launch in the full platform with interactive questions and scenarios.');
    closeModal();
}

function openDoseCalculator() {
    closeModal();
    document.querySelector('.dose-calculator').scrollIntoView({ behavior: 'smooth' });
}

function practiceCalculation(principleId) {
    alert(`Interactive calculation practice for ${principleId} principle will launch in the full platform.`);
    closeModal();
}

function viewExamples(principleId) {
    alert(`Real-world examples for ${principleId} principle will be displayed in the full platform.`);
    closeModal();
}

function startEmergencyDrill(procedureId) {
    alert(`Emergency drill simulation for ${procedureId} will launch in the full platform.`);
    closeModal();
}

function downloadProcedure(procedureId) {
    alert(`Emergency procedure guide for ${procedureId} will be downloaded in the full platform.`);
    closeModal();
}

function showModal({ title, content }) {
    const modal = document.createElement('div');
    modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.8);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10000;
        animation: fadeIn 0.3s ease;
    `;
    
    modal.innerHTML = `
        <div style="background: white; border-radius: 15px; max-width: 700px; width: 90%; max-height: 80%; overflow-y: auto; position: relative;">
            <div style="background: var(--napkin-gradient); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;">
                <h3 style="margin: 0; font-size: 1.3rem;">${title}</h3>
                <button onclick="closeModal()" style="position: absolute; top: 1rem; right: 1rem; background: none; border: none; color: white; font-size: 1.5rem; cursor: pointer;">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div>${content}</div>
        </div>
    `;
    
    document.body.appendChild(modal);
    window.currentModal = modal;
    
    // Close on outside click
    modal.addEventListener('click', (e) => {
        if (e.target === modal) closeModal();
    });
}

function closeModal() {
    if (window.currentModal) {
        window.currentModal.remove();
        window.currentModal = null;
    }
}

// Add CSS for animations
const style = document.createElement('style');
style.textContent = `
    @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
    }
    
    @keyframes slideIn {
        from { transform: translateX(100%); }
        to { transform: translateX(0); }
    }
`;
document.head.appendChild(style);

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.safetyModule = new SafetyModule();
});
