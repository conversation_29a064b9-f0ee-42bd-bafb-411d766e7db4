/**
 * JavaScript for X-ray System Components Module
 */

document.addEventListener('DOMContentLoaded', function() {
    // Navigation active state management for component page
    const navLinks = document.querySelectorAll('nav ul li a');
    const sections = document.querySelectorAll('section[id]');
    
    // Smooth scrolling for navigation links
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            // Only prevent default if it's an anchor link
            if (this.getAttribute('href').startsWith('#')) {
                e.preventDefault();
                
                const targetId = this.getAttribute('href');
                const targetSection = document.querySelector(targetId);
                
                if (targetSection) {
                    window.scrollTo({
                        top: targetSection.offsetTop - 60, // Adjust for nav height
                        behavior: 'smooth'
                    });
                    
                    // Update active link
                    navLinks.forEach(link => link.classList.remove('active'));
                    this.classList.add('active');
                }
            }
        });
    });
    
    // Update active navigation link on scroll
    window.addEventListener('scroll', function() {
        let current = '';
        
        sections.forEach(section => {
            const sectionTop = section.offsetTop;
            const sectionHeight = section.clientHeight;
            
            if (pageYOffset >= sectionTop - 100) {
                current = section.getAttribute('id');
            }
        });
        
        navLinks.forEach(link => {
            link.classList.remove('active');
            const href = link.getAttribute('href');
            if (href && href === `#${current}`) {
                link.classList.add('active');
            }
        });
    });
    
    // Initialize interactive simulations
    initializeInteractiveSimulations();
});

/**
 * Initialize interactive simulations for X-ray components
 */
function initializeInteractiveSimulations() {
    // X-ray Tube Simulation
    const tubeSimulationBtn = document.getElementById('tubeSimulationBtn');
    if (tubeSimulationBtn) {
        tubeSimulationBtn.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Create modal for tube simulation
            const modal = createSimulationModal('X-ray Tube Interactive Simulation');
            
            // Add simulation content
            const simulationContent = document.createElement('div');
            simulationContent.className = 'simulation-content';
            simulationContent.innerHTML = `
                <div class="tube-simulation">
                    <div class="tube-container">
                        <div class="tube-envelope">
                            <div class="cathode">
                                <div class="filament"></div>
                            </div>
                            <div class="electron-beam"></div>
                            <div class="anode">
                                <div class="target"></div>
                            </div>
                            <div class="xray-beam"></div>
                        </div>
                    </div>
                    <div class="simulation-controls">
                        <div class="control-group">
                            <label for="kv-control">kV (Tube Voltage):</label>
                            <input type="range" id="kv-control" min="40" max="150" value="80">
                            <span id="kv-value">80 kV</span>
                        </div>
                        <div class="control-group">
                            <label for="ma-control">mA (Tube Current):</label>
                            <input type="range" id="ma-control" min="10" max="500" value="100">
                            <span id="ma-value">100 mA</span>
                        </div>
                        <button id="activate-tube" class="btn">Activate X-ray Tube</button>
                    </div>
                </div>
                <div class="simulation-info">
                    <h4>How It Works</h4>
                    <p>1. The filament in the cathode is heated by an electric current, causing it to emit electrons (thermionic emission).</p>
                    <p>2. The high voltage between cathode and anode accelerates these electrons across the tube.</p>
                    <p>3. When electrons strike the target material in the anode, they rapidly decelerate, converting their kinetic energy into X-ray photons.</p>
                    <p>4. The higher the kV, the more penetrating the X-rays. The higher the mA, the more X-ray photons produced.</p>
                </div>
            `;
            modal.querySelector('.modal-body').appendChild(simulationContent);
            
            // Add event listeners for simulation controls
            setTimeout(() => {
                const kvControl = document.getElementById('kv-control');
                const maControl = document.getElementById('ma-control');
                const kvValue = document.getElementById('kv-value');
                const maValue = document.getElementById('ma-value');
                const activateButton = document.getElementById('activate-tube');
                const electronBeam = document.querySelector('.electron-beam');
                const xrayBeam = document.querySelector('.xray-beam');
                
                if (kvControl && maControl && activateButton) {
                    kvControl.addEventListener('input', function() {
                        kvValue.textContent = `${this.value} kV`;
                    });
                    
                    maControl.addEventListener('input', function() {
                        maValue.textContent = `${this.value} mA`;
                    });
                    
                    activateButton.addEventListener('click', function() {
                        // Animate electron beam
                        electronBeam.style.opacity = '1';
                        electronBeam.style.animation = 'electronFlow 1s infinite linear';
                        
                        // Animate X-ray beam
                        setTimeout(() => {
                            xrayBeam.style.opacity = '1';
                            xrayBeam.style.animation = 'xrayEmission 1.5s infinite ease-out';
                        }, 500);
                        
                        // Disable button during animation
                        this.disabled = true;
                        
                        // Reset after 5 seconds
                        setTimeout(() => {
                            electronBeam.style.opacity = '0';
                            electronBeam.style.animation = 'none';
                            xrayBeam.style.opacity = '0';
                            xrayBeam.style.animation = 'none';
                            this.disabled = false;
                        }, 5000);
                    });
                }
            }, 100);
        });
    }
    
    // Generator Simulation Button
    const generatorSimulationBtn = document.getElementById('generatorSimulationBtn');
    if (generatorSimulationBtn) {
        generatorSimulationBtn.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Create modal for generator simulation
            const modal = createSimulationModal('High Voltage Generator Simulation');
            
            // Add simulation content
            const simulationContent = document.createElement('div');
            simulationContent.className = 'simulation-content';
            simulationContent.innerHTML = `
                <div class="generator-simulation">
                    <div class="circuit-diagram">
                        <img src="../images/generator-circuit.png" alt="Generator Circuit Diagram" onerror="this.src='https://via.placeholder.com/600x300?text=Generator+Circuit+Diagram'">
                    </div>
                    <div class="simulation-controls">
                        <div class="control-group">
                            <label for="input-voltage">Input Voltage:</label>
                            <input type="range" id="input-voltage" min="200" max="240" value="220">
                            <span id="input-voltage-value">220 V</span>
                        </div>
                        <div class="control-group">
                            <label for="output-kv">Output kV:</label>
                            <input type="range" id="output-kv" min="40" max="150" value="80">
                            <span id="output-kv-value">80 kV</span>
                        </div>
                        <button id="activate-generator" class="btn">Activate Generator</button>
                    </div>
                </div>
                <div class="simulation-info">
                    <h4>How It Works</h4>
                    <p>1. Line voltage (typically 220-240V) enters the generator.</p>
                    <p>2. In modern generators, an inverter converts this to high-frequency AC.</p>
                    <p>3. A step-up transformer increases the voltage to the required kV level.</p>
                    <p>4. Rectifiers convert the high-voltage AC to DC for the X-ray tube.</p>
                    <p>5. Control circuits regulate the output based on the selected technique factors.</p>
                </div>
            `;
            modal.querySelector('.modal-body').appendChild(simulationContent);
            
            // Add event listeners for simulation controls
            setTimeout(() => {
                const inputVoltage = document.getElementById('input-voltage');
                const outputKv = document.getElementById('output-kv');
                const inputVoltageValue = document.getElementById('input-voltage-value');
                const outputKvValue = document.getElementById('output-kv-value');
                const activateButton = document.getElementById('activate-generator');
                
                if (inputVoltage && outputKv && activateButton) {
                    inputVoltage.addEventListener('input', function() {
                        inputVoltageValue.textContent = `${this.value} V`;
                    });
                    
                    outputKv.addEventListener('input', function() {
                        outputKvValue.textContent = `${this.value} kV`;
                    });
                    
                    activateButton.addEventListener('click', function() {
                        // Simulate generator activation
                        this.textContent = 'Generator Active';
                        this.classList.add('active');
                        
                        // Reset after 3 seconds
                        setTimeout(() => {
                            this.textContent = 'Activate Generator';
                            this.classList.remove('active');
                        }, 3000);
                    });
                }
            }, 100);
        });
    }
}

/**
 * Create a modal for interactive simulations
 * @param {string} title - The title of the simulation
 * @returns {HTMLElement} - The created modal element
 */
function createSimulationModal(title) {
    // Create modal container
    const modalContainer = document.createElement('div');
    modalContainer.className = 'simulation-modal-container';
    modalContainer.style.position = 'fixed';
    modalContainer.style.top = '0';
    modalContainer.style.left = '0';
    modalContainer.style.width = '100%';
    modalContainer.style.height = '100%';
    modalContainer.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
    modalContainer.style.display = 'flex';
    modalContainer.style.justifyContent = 'center';
    modalContainer.style.alignItems = 'center';
    modalContainer.style.zIndex = '1000';
    
    // Create modal
    const modal = document.createElement('div');
    modal.className = 'simulation-modal';
    modal.style.backgroundColor = 'white';
    modal.style.borderRadius = '8px';
    modal.style.width = '90%';
    modal.style.maxWidth = '800px';
    modal.style.maxHeight = '90vh';
    modal.style.overflow = 'auto';
    modal.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.2)';
    
    // Create modal header
    const modalHeader = document.createElement('div');
    modalHeader.className = 'modal-header';
    modalHeader.style.padding = '1rem';
    modalHeader.style.borderBottom = '1px solid #e9ecef';
    modalHeader.style.display = 'flex';
    modalHeader.style.justifyContent = 'space-between';
    modalHeader.style.alignItems = 'center';
    
    const modalTitle = document.createElement('h3');
    modalTitle.textContent = title;
    modalTitle.style.margin = '0';
    
    const closeButton = document.createElement('button');
    closeButton.innerHTML = '&times;';
    closeButton.style.background = 'none';
    closeButton.style.border = 'none';
    closeButton.style.fontSize = '1.5rem';
    closeButton.style.cursor = 'pointer';
    closeButton.style.padding = '0 0.5rem';
    
    modalHeader.appendChild(modalTitle);
    modalHeader.appendChild(closeButton);
    
    // Create modal body
    const modalBody = document.createElement('div');
    modalBody.className = 'modal-body';
    modalBody.style.padding = '1rem';
    
    // Assemble modal
    modal.appendChild(modalHeader);
    modal.appendChild(modalBody);
    modalContainer.appendChild(modal);
    
    // Add close functionality
    closeButton.addEventListener('click', function() {
        document.body.removeChild(modalContainer);
    });
    
    // Add click outside to close
    modalContainer.addEventListener('click', function(e) {
        if (e.target === modalContainer) {
            document.body.removeChild(modalContainer);
        }
    });
    
    // Add to document
    document.body.appendChild(modalContainer);
    
    return modal;
}
