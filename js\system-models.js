/**
 * VirtualX Pro - System Models Interactive Features
 * Advanced 3D visualization and learning tools for X-ray system models
 */

class SystemModelsManager {
    constructor() {
        this.currentModel = null;
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.controls = null;
        this.modelData = {};
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadModelData();
        this.setupVendorFiltering();
        this.initializeProgressTracking();
    }

    setupEventListeners() {
        // Vendor tab switching
        document.querySelectorAll('.vendor-tab').forEach(tab => {
            tab.addEventListener('click', (e) => {
                this.switchVendor(e.target.dataset.vendor);
            });
        });

        // 3D viewer controls
        document.querySelectorAll('.control-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.handleControlAction(e.target);
            });
        });

        // Model card interactions
        document.querySelectorAll('.model-3d-preview').forEach(preview => {
            preview.addEventListener('click', (e) => {
                e.stopPropagation();
                const modelCard = e.target.closest('.model-card');
                const modelId = this.getModelIdFromCard(modelCard);
                this.open3DViewer(modelId);
            });
        });
    }

    loadModelData() {
        // Comprehensive model database
        this.modelData = {
            'ge-discovery-xr656': {
                name: 'Discovery XR656',
                manufacturer: 'GE Healthcare',
                category: 'Digital Radiography',
                specifications: {
                    'Generator Type': 'High Frequency',
                    'kVp Range': '40-150 kVp',
                    'mA Range': '10-500 mA',
                    'Detector Type': 'Flat Panel',
                    'Detector Size': '43 x 43 cm',
                    'Image Matrix': '3072 x 3072',
                    'Pixel Size': '140 μm',
                    'A/D Conversion': '16-bit',
                    'Exposure Time': '1-6300 ms',
                    'Grid Ratio': '12:1 or 15:1'
                },
                components: {
                    tube: {
                        name: 'X-ray Tube',
                        description: 'High-performance rotating anode X-ray tube with tungsten target',
                        specifications: {
                            'Anode Angle': '12°',
                            'Focal Spot': '0.6/1.2 mm',
                            'Heat Capacity': '300 kHU',
                            'Cooling Rate': '60 kW'
                        }
                    },
                    generator: {
                        name: 'High Frequency Generator',
                        description: 'Advanced high-frequency generator with precise exposure control',
                        specifications: {
                            'Frequency': '100 kHz',
                            'Power Rating': '80 kW',
                            'Voltage Ripple': '<1%',
                            'Exposure Accuracy': '±2%'
                        }
                    },
                    detector: {
                        name: 'Flat Panel Detector',
                        description: 'Amorphous silicon flat panel detector with excellent image quality',
                        specifications: {
                            'Technology': 'a-Si/CsI',
                            'Fill Factor': '>80%',
                            'DQE': '>60% at 1 lp/mm',
                            'Dynamic Range': '>10,000:1'
                        }
                    },
                    collimator: {
                        name: 'Automatic Collimator',
                        description: 'Motorized collimator with automatic field sizing',
                        specifications: {
                            'Filtration': '2.5 mm Al equiv.',
                            'Light Field': 'LED illumination',
                            'Accuracy': '±2% SID',
                            'Motors': 'Servo-controlled'
                        }
                    }
                },
                learningObjectives: [
                    'Understand the complete system architecture',
                    'Identify key components and their functions',
                    'Learn maintenance procedures and schedules',
                    'Practice troubleshooting common issues'
                ]
            },
            'philips-digitaldiagnost-c90': {
                name: 'DigitalDiagnost C90',
                manufacturer: 'Philips Healthcare',
                category: 'Premium Digital Radiography',
                specifications: {
                    'Generator Type': 'High Frequency',
                    'kVp Range': '40-150 kVp',
                    'mA Range': '10-630 mA',
                    'Detector Type': 'Flat Panel',
                    'Detector Size': '43 x 43 cm',
                    'Suspension': 'SkyFlow',
                    'User Interface': 'Eleva',
                    'Dose Management': 'DoseWise',
                    'Workflow': 'Smart Positioning'
                },
                components: {
                    tube: {
                        name: 'X-ray Tube Assembly',
                        description: 'Advanced rotating anode tube with extended life design',
                        specifications: {
                            'Anode Angle': '10°',
                            'Focal Spot': '0.6/1.0 mm',
                            'Heat Capacity': '400 kHU',
                            'Cooling Rate': '70 kW'
                        }
                    },
                    generator: {
                        name: 'Digital Generator',
                        description: 'State-of-the-art digital generator with precise control',
                        specifications: {
                            'Technology': 'IGBT',
                            'Power Rating': '80 kW',
                            'Frequency': '100 kHz',
                            'Accuracy': '±1%'
                        }
                    },
                    detector: {
                        name: 'Digital Detector',
                        description: 'High-performance flat panel detector with excellent DQE',
                        specifications: {
                            'Technology': 'a-Si/CsI',
                            'Pixel Pitch': '143 μm',
                            'DQE': '>65% at 1 lp/mm',
                            'Conversion': '16-bit'
                        }
                    },
                    suspension: {
                        name: 'SkyFlow Suspension',
                        description: 'Innovative ceiling-mounted suspension system',
                        specifications: {
                            'Movement': '5-axis',
                            'Load Capacity': '200 kg',
                            'Positioning': 'Motorized',
                            'Brakes': 'Electromagnetic'
                        }
                    }
                }
            },
            'siemens-ysio-max': {
                name: 'Ysio Max',
                manufacturer: 'Siemens Healthineers',
                category: 'Digital Radiography',
                specifications: {
                    'Generator Type': 'High Frequency',
                    'kVp Range': '40-150 kVp',
                    'mA Range': '10-500 mA',
                    'Technology': 'CARE+CLEAR',
                    'Detector Size': '43 x 43 cm',
                    'Workflow': 'Smart Workflow',
                    'Image Processing': 'MUSICA',
                    'Dose Optimization': 'CAREfilter'
                },
                components: {
                    tube: {
                        name: 'OPTI 150/30/50 HC-100',
                        description: 'High-capacity rotating anode X-ray tube',
                        specifications: {
                            'Anode Angle': '12°',
                            'Focal Spot': '0.6/1.2 mm',
                            'Heat Capacity': '350 kHU',
                            'Housing': 'Metal-ceramic'
                        }
                    },
                    generator: {
                        name: 'POLYDOROS SX 80',
                        description: 'Advanced high-frequency generator',
                        specifications: {
                            'Power': '80 kW',
                            'Frequency': '100 kHz',
                            'Technology': 'IGBT',
                            'Control': 'Digital'
                        }
                    },
                    detector: {
                        name: 'FD-R 43x43',
                        description: 'High-resolution flat panel detector',
                        specifications: {
                            'Technology': 'a-Si/CsI',
                            'Matrix': '3072 x 3072',
                            'Pixel Size': '140 μm',
                            'DQE': '>60%'
                        }
                    }
                }
            }
        };
    }

    switchVendor(vendor) {
        // Update active tab
        document.querySelectorAll('.vendor-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelector(`[data-vendor="${vendor}"]`).classList.add('active');

        // Filter model cards
        const modelCards = document.querySelectorAll('.model-card');
        modelCards.forEach(card => {
            if (vendor === 'all' || card.dataset.vendor === vendor) {
                card.style.display = 'block';
                card.classList.add('animate__animated', 'animate__fadeInUp');
            } else {
                card.style.display = 'none';
            }
        });

        // Update progress tracking
        this.updateProgressDisplay(vendor);
    }

    open3DViewer(modelId) {
        this.currentModel = modelId;
        const modelInfo = this.modelData[modelId];
        
        if (!modelInfo) {
            console.error('Model not found:', modelId);
            return;
        }

        // Update viewer title
        document.getElementById('viewer-title').textContent = `${modelInfo.name} - 3D Explorer`;
        
        // Show viewer
        document.getElementById('model-viewer').style.display = 'flex';
        
        // Initialize 3D scene
        this.init3DScene();
        
        // Load model
        this.load3DModel(modelId);
        
        // Update component info
        this.updateComponentInfo('Select a component to view details');
        
        // Track learning progress
        this.trackModelViewed(modelId);
    }

    close3DViewer() {
        document.getElementById('model-viewer').style.display = 'none';
        
        // Cleanup 3D scene
        if (this.renderer) {
            this.renderer.dispose();
            this.renderer = null;
        }
        
        this.currentModel = null;
    }

    init3DScene() {
        const canvas = document.getElementById('model-canvas');
        const container = document.getElementById('viewer-3d');
        
        // Scene setup
        this.scene = new THREE.Scene();
        this.scene.background = new THREE.Color(0xf8f9fa);
        
        // Camera setup
        this.camera = new THREE.PerspectiveCamera(
            75,
            container.clientWidth / container.clientHeight,
            0.1,
            1000
        );
        this.camera.position.set(5, 5, 5);
        
        // Renderer setup
        this.renderer = new THREE.WebGLRenderer({ 
            canvas: canvas,
            antialias: true 
        });
        this.renderer.setSize(container.clientWidth, container.clientHeight);
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        
        // Lighting
        const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
        this.scene.add(ambientLight);
        
        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
        directionalLight.position.set(10, 10, 5);
        directionalLight.castShadow = true;
        this.scene.add(directionalLight);
        
        // Controls (simplified for demo)
        this.setupBasicControls();
        
        // Start render loop
        this.animate();
    }

    load3DModel(modelId) {
        // Create a simplified 3D representation
        const modelInfo = this.modelData[modelId];
        
        // Clear existing models
        this.scene.children = this.scene.children.filter(child => 
            child.type === 'AmbientLight' || child.type === 'DirectionalLight'
        );
        
        // Create basic geometric representation
        this.createBasicModel(modelInfo);
    }

    createBasicModel(modelInfo) {
        const group = new THREE.Group();
        
        // X-ray tube (cylinder)
        const tubeGeometry = new THREE.CylinderGeometry(0.5, 0.5, 2, 16);
        const tubeMaterial = new THREE.MeshLambertMaterial({ color: 0x666666 });
        const tube = new THREE.Mesh(tubeGeometry, tubeMaterial);
        tube.position.set(-2, 1, 0);
        tube.userData = { component: 'tube', name: 'X-ray Tube' };
        group.add(tube);
        
        // Generator (box)
        const generatorGeometry = new THREE.BoxGeometry(1.5, 1, 1);
        const generatorMaterial = new THREE.MeshLambertMaterial({ color: 0x0066cc });
        const generator = new THREE.Mesh(generatorGeometry, generatorMaterial);
        generator.position.set(-2, -1, 0);
        generator.userData = { component: 'generator', name: 'Generator' };
        group.add(generator);
        
        // Detector (flat panel)
        const detectorGeometry = new THREE.PlaneGeometry(2, 2);
        const detectorMaterial = new THREE.MeshLambertMaterial({ 
            color: 0x333333,
            side: THREE.DoubleSide 
        });
        const detector = new THREE.Mesh(detectorGeometry, detectorMaterial);
        detector.position.set(2, 0, 0);
        detector.userData = { component: 'detector', name: 'Detector' };
        group.add(detector);
        
        // Collimator (smaller box)
        const collimatorGeometry = new THREE.BoxGeometry(0.8, 0.3, 0.8);
        const collimatorMaterial = new THREE.MeshLambertMaterial({ color: 0x888888 });
        const collimator = new THREE.Mesh(collimatorGeometry, collimatorMaterial);
        collimator.position.set(-1, 1, 0);
        collimator.userData = { component: 'collimator', name: 'Collimator' };
        group.add(collimator);
        
        // Add click interaction
        this.setupModelInteraction(group);
        
        this.scene.add(group);
    }

    setupModelInteraction(group) {
        const raycaster = new THREE.Raycaster();
        const mouse = new THREE.Vector2();
        const canvas = document.getElementById('model-canvas');
        
        canvas.addEventListener('click', (event) => {
            const rect = canvas.getBoundingClientRect();
            mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
            mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;
            
            raycaster.setFromCamera(mouse, this.camera);
            const intersects = raycaster.intersectObjects(group.children);
            
            if (intersects.length > 0) {
                const selectedObject = intersects[0].object;
                this.highlightComponent(selectedObject);
                this.showComponentDetails(selectedObject.userData);
            }
        });
    }

    highlightComponent(object) {
        // Reset all materials
        this.scene.traverse((child) => {
            if (child.isMesh && child.userData.component) {
                child.material.emissive.setHex(0x000000);
            }
        });
        
        // Highlight selected component
        object.material.emissive.setHex(0x444444);
    }

    showComponentDetails(userData) {
        const modelInfo = this.modelData[this.currentModel];
        const componentInfo = modelInfo.components[userData.component];
        
        if (componentInfo) {
            let html = `
                <h4 style="color: var(--enterprise-primary); margin-bottom: 1rem;">
                    ${componentInfo.name}
                </h4>
                <p style="margin-bottom: 1rem;">${componentInfo.description}</p>
                <div style="background: white; padding: 1rem; border-radius: 8px;">
                    <h5 style="margin-bottom: 0.5rem;">Specifications:</h5>
            `;
            
            Object.entries(componentInfo.specifications).forEach(([key, value]) => {
                html += `
                    <div style="display: flex; justify-content: space-between; padding: 0.25rem 0; border-bottom: 1px solid #eee;">
                        <span style="font-weight: 600;">${key}:</span>
                        <span>${value}</span>
                    </div>
                `;
            });
            
            html += '</div>';
            
            document.getElementById('component-info').innerHTML = html;
        }
    }

    setupBasicControls() {
        let isRotating = false;
        let previousMousePosition = { x: 0, y: 0 };
        const canvas = document.getElementById('model-canvas');
        
        canvas.addEventListener('mousedown', (e) => {
            isRotating = true;
            previousMousePosition = { x: e.clientX, y: e.clientY };
        });
        
        canvas.addEventListener('mouseup', () => {
            isRotating = false;
        });
        
        canvas.addEventListener('mousemove', (e) => {
            if (isRotating) {
                const deltaMove = {
                    x: e.clientX - previousMousePosition.x,
                    y: e.clientY - previousMousePosition.y
                };
                
                const deltaRotationQuaternion = new THREE.Quaternion()
                    .setFromEuler(new THREE.Euler(
                        deltaMove.y * 0.01,
                        deltaMove.x * 0.01,
                        0,
                        'XYZ'
                    ));
                
                this.camera.quaternion.multiplyQuaternions(deltaRotationQuaternion, this.camera.quaternion);
                
                previousMousePosition = { x: e.clientX, y: e.clientY };
            }
        });
        
        // Zoom with mouse wheel
        canvas.addEventListener('wheel', (e) => {
            e.preventDefault();
            const scale = e.deltaY > 0 ? 1.1 : 0.9;
            this.camera.position.multiplyScalar(scale);
        });
    }

    animate() {
        if (this.renderer && this.scene && this.camera) {
            requestAnimationFrame(() => this.animate());
            this.renderer.render(this.scene, this.camera);
        }
    }

    handleControlAction(button) {
        const action = button.dataset.action;
        const component = button.dataset.component;
        const mode = button.dataset.mode;
        
        // Update button states
        if (action || component || mode) {
            const parentSection = button.closest('.control-section');
            parentSection.querySelectorAll('.control-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            button.classList.add('active');
        }
        
        // Handle different actions
        if (action) {
            this.handleViewAction(action);
        } else if (component) {
            this.highlightSpecificComponent(component);
        } else if (mode) {
            this.switchLearningMode(mode);
        }
    }

    handleViewAction(action) {
        switch (action) {
            case 'rotate':
                this.enableRotation();
                break;
            case 'zoom':
                this.enableZoom();
                break;
            case 'explode':
                this.toggleExplodedView();
                break;
            case 'xray':
                this.toggleXrayView();
                break;
        }
    }

    highlightSpecificComponent(componentType) {
        this.scene.traverse((child) => {
            if (child.isMesh && child.userData.component) {
                if (child.userData.component === componentType) {
                    child.material.emissive.setHex(0x444444);
                    this.showComponentDetails(child.userData);
                } else {
                    child.material.emissive.setHex(0x000000);
                }
            }
        });
    }

    switchLearningMode(mode) {
        switch (mode) {
            case 'explore':
                this.updateComponentInfo('Explore mode: Click on components to learn about them');
                break;
            case 'assembly':
                this.updateComponentInfo('Assembly mode: Learn how components fit together');
                break;
            case 'quiz':
                this.startQuizMode();
                break;
            case 'compare':
                this.updateComponentInfo('Compare mode: Compare this system with others');
                break;
        }
    }

    startQuizMode() {
        const questions = [
            'What is the primary function of the X-ray tube?',
            'What type of detector is used in this system?',
            'What is the purpose of the collimator?'
        ];
        
        const randomQuestion = questions[Math.floor(Math.random() * questions.length)];
        this.updateComponentInfo(`Quiz Question: ${randomQuestion}`);
    }

    updateComponentInfo(content) {
        document.getElementById('component-info').innerHTML = `<p>${content}</p>`;
    }

    trackModelViewed(modelId) {
        // Update progress tracking
        const progress = JSON.parse(localStorage.getItem('virtualx-progress') || '{}');
        if (!progress.modelsViewed) {
            progress.modelsViewed = [];
        }
        
        if (!progress.modelsViewed.includes(modelId)) {
            progress.modelsViewed.push(modelId);
            localStorage.setItem('virtualx-progress', JSON.stringify(progress));
            this.updateProgressDisplay();
        }
    }

    updateProgressDisplay(vendor = 'all') {
        const progress = JSON.parse(localStorage.getItem('virtualx-progress') || '{}');
        const viewedModels = progress.modelsViewed || [];
        
        // Update progress bars based on viewed models
        const progressItems = document.querySelectorAll('.progress-item');
        progressItems.forEach(item => {
            const progressBar = item.querySelector('.progress-fill');
            const currentWidth = parseInt(progressBar.style.width) || 0;
            const newWidth = Math.min(currentWidth + 5, 100); // Simulate progress
            progressBar.style.width = `${newWidth}%`;
        });
    }

    getModelIdFromCard(card) {
        // Extract model ID from card data or content
        const title = card.querySelector('.model-title').textContent;
        const category = card.querySelector('.model-category').textContent;
        
        // Simple mapping based on title
        const titleMap = {
            'Discovery XR656': 'ge-discovery-xr656',
            'DigitalDiagnost C90': 'philips-digitaldiagnost-c90',
            'Ysio Max': 'siemens-ysio-max'
        };
        
        return titleMap[title] || 'unknown';
    }

    enableRotation() {
        this.updateComponentInfo('Rotation enabled: Click and drag to rotate the model');
    }

    enableZoom() {
        this.updateComponentInfo('Zoom enabled: Use mouse wheel to zoom in/out');
    }

    toggleExplodedView() {
        this.updateComponentInfo('Exploded view: Components separated to show internal structure');
        // Implement exploded view logic
    }

    toggleXrayView() {
        this.updateComponentInfo('X-ray view: See through external components to view internal structure');
        // Implement x-ray view logic
    }

    initializeProgressTracking() {
        // Initialize progress if not exists
        const progress = JSON.parse(localStorage.getItem('virtualx-progress') || '{}');
        if (!progress.modelsViewed) {
            progress.modelsViewed = [];
            localStorage.setItem('virtualx-progress', JSON.stringify(progress));
        }
    }
}

// Global functions for HTML onclick handlers
function open3DViewer(modelId) {
    if (window.systemModelsManager) {
        window.systemModelsManager.open3DViewer(modelId);
    }
}

function close3DViewer() {
    if (window.systemModelsManager) {
        window.systemModelsManager.close3DViewer();
    }
}

function showSpecifications(modelId) {
    // Open specifications in a new modal or page
    alert(`Opening detailed specifications for ${modelId}`);
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.systemModelsManager = new SystemModelsManager();
});

// Add CSS for navigation brand
const navStyle = document.createElement('style');
navStyle.textContent = `
    .nav-brand {
        display: flex;
        align-items: center;
        gap: 1rem;
        text-decoration: none;
    }

    .nav-logo {
        height: 40px;
        width: auto;
    }

    .brand-text {
        font-size: 1.5rem;
        font-weight: 700;
        color: var(--enterprise-primary);
    }

    .nav-brand:hover .brand-text {
        color: var(--enterprise-secondary);
    }
`;
document.head.appendChild(navStyle);
