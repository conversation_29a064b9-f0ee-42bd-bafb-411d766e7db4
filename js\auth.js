/**
 * Authentication and Security Module for X-ray System Training
 * Implements user authentication, password encryption, and session management
 */

// CryptoJS library is required for encryption operations
// We'll load it dynamically if not already available
if (typeof CryptoJS === 'undefined') {
    loadCryptoJSLibrary();
}

// Configuration
const AUTH_CONFIG = {
    // Salt for password hashing (in a real app, this would be stored securely)
    salt: 'X-ray-Training-Salt-2025',
    // Encryption key for content (in a real app, this would be stored securely)
    encryptionKey: 'X-ray-Content-Encryption-Key-2025',
    // Session duration in minutes
    sessionDuration: 60,
    // Local storage keys
    storageKeys: {
        user: 'xray_training_user',
        session: 'xray_training_session',
        encryptedContent: 'xray_training_encrypted_content'
    }
};

/**
 * User class to handle user-related operations
 */
class User {
    constructor(username, email, role = 'student') {
        this.username = username;
        this.email = email;
        this.role = role; // 'student', 'instructor', 'admin'
        this.lastLogin = new Date().toISOString();
        this.progress = {};
    }

    // Save user data to local storage (encrypted)
    save() {
        const userData = JSON.stringify(this);
        const encryptedData = encryptData(userData, AUTH_CONFIG.encryptionKey);
        localStorage.setItem(AUTH_CONFIG.storageKeys.user, encryptedData);
    }

    // Update user progress for a specific module
    updateProgress(moduleId, progress) {
        this.progress[moduleId] = progress;
        this.save();
    }

    // Get user progress for a specific module
    getProgress(moduleId) {
        return this.progress[moduleId] || 0;
    }
}

/**
 * Authentication class to handle login, registration, and session management
 */
class Auth {
    constructor() {
        this.currentUser = null;
        this.isAuthenticated = false;
        this.sessionToken = null;
        
        // Check if user is already logged in
        this.checkSession();
    }

    /**
     * Register a new user
     * @param {string} username - Username
     * @param {string} email - Email address
     * @param {string} password - Plain text password
     * @returns {boolean} - Success status
     */
    register(username, email, password) {
        try {
            // Check if username already exists
            const existingUsers = this.getAllUsers();
            if (existingUsers.find(user => user.username === username)) {
                console.error('Username already exists');
                return false;
            }

            // Hash password
            const hashedPassword = this.hashPassword(password);
            
            // Create new user
            const user = new User(username, email);
            
            // Save user to "database" (localStorage in this case)
            const users = existingUsers;
            users.push({
                username,
                email,
                password: hashedPassword,
                role: 'student',
                createdAt: new Date().toISOString()
            });
            
            localStorage.setItem('xray_training_users', JSON.stringify(users));
            
            return true;
        } catch (error) {
            console.error('Registration error:', error);
            return false;
        }
    }

    /**
     * Login user
     * @param {string} username - Username
     * @param {string} password - Plain text password
     * @returns {boolean} - Success status
     */
    login(username, password) {
        try {
            // Get user from "database"
            const users = this.getAllUsers();
            const userRecord = users.find(user => user.username === username);
            
            if (!userRecord) {
                console.error('User not found');
                return false;
            }
            
            // Verify password
            const hashedPassword = this.hashPassword(password);
            if (userRecord.password !== hashedPassword) {
                console.error('Invalid password');
                return false;
            }
            
            // Create user object
            this.currentUser = new User(userRecord.username, userRecord.email, userRecord.role);
            
            // Update last login
            userRecord.lastLogin = new Date().toISOString();
            localStorage.setItem('xray_training_users', JSON.stringify(users));
            
            // Create session
            this.createSession();
            
            // Save user data
            this.currentUser.save();
            
            this.isAuthenticated = true;
            
            // Trigger event
            this.triggerAuthEvent('login');
            
            return true;
        } catch (error) {
            console.error('Login error:', error);
            return false;
        }
    }

    /**
     * Logout user
     */
    logout() {
        this.currentUser = null;
        this.isAuthenticated = false;
        this.sessionToken = null;
        
        // Clear session
        localStorage.removeItem(AUTH_CONFIG.storageKeys.session);
        
        // Trigger event
        this.triggerAuthEvent('logout');
    }

    /**
     * Check if session is valid
     * @returns {boolean} - Is session valid
     */
    checkSession() {
        try {
            // Get session from storage
            const sessionData = localStorage.getItem(AUTH_CONFIG.storageKeys.session);
            if (!sessionData) {
                return false;
            }
            
            // Decrypt session data
            const decryptedSession = decryptData(sessionData, AUTH_CONFIG.encryptionKey);
            const session = JSON.parse(decryptedSession);
            
            // Check if session is expired
            const now = new Date();
            const sessionExpiry = new Date(session.expiry);
            
            if (now > sessionExpiry) {
                // Session expired
                localStorage.removeItem(AUTH_CONFIG.storageKeys.session);
                return false;
            }
            
            // Session is valid, restore user
            const userData = localStorage.getItem(AUTH_CONFIG.storageKeys.user);
            if (userData) {
                const decryptedUser = decryptData(userData, AUTH_CONFIG.encryptionKey);
                const userObj = JSON.parse(decryptedUser);
                
                this.currentUser = new User(userObj.username, userObj.email, userObj.role);
                this.currentUser.progress = userObj.progress || {};
                this.isAuthenticated = true;
                this.sessionToken = session.token;
                
                // Extend session
                this.createSession();
                
                // Trigger event
                this.triggerAuthEvent('session-restored');
                
                return true;
            }
            
            return false;
        } catch (error) {
            console.error('Session check error:', error);
            return false;
        }
    }

    /**
     * Create a new session
     */
    createSession() {
        // Generate random token
        this.sessionToken = this.generateToken();
        
        // Set expiry time
        const expiry = new Date();
        expiry.setMinutes(expiry.getMinutes() + AUTH_CONFIG.sessionDuration);
        
        // Create session object
        const session = {
            token: this.sessionToken,
            username: this.currentUser.username,
            expiry: expiry.toISOString()
        };
        
        // Encrypt and save session
        const encryptedSession = encryptData(JSON.stringify(session), AUTH_CONFIG.encryptionKey);
        localStorage.setItem(AUTH_CONFIG.storageKeys.session, encryptedSession);
    }

    /**
     * Hash password with salt
     * @param {string} password - Plain text password
     * @returns {string} - Hashed password
     */
    hashPassword(password) {
        return CryptoJS.SHA256(password + AUTH_CONFIG.salt).toString();
    }

    /**
     * Generate random token
     * @returns {string} - Random token
     */
    generateToken() {
        return CryptoJS.lib.WordArray.random(16).toString();
    }

    /**
     * Get all users from storage
     * @returns {Array} - Array of user objects
     */
    getAllUsers() {
        const users = localStorage.getItem('xray_training_users');
        return users ? JSON.parse(users) : [];
    }

    /**
     * Trigger authentication event
     * @param {string} eventType - Event type
     */
    triggerAuthEvent(eventType) {
        const event = new CustomEvent('auth', {
            detail: {
                type: eventType,
                user: this.currentUser
            }
        });
        document.dispatchEvent(event);
    }

    /**
     * Check if user has access to a specific module
     * @param {string} moduleId - Module ID
     * @returns {boolean} - Has access
     */
    hasAccessToModule(moduleId) {
        if (!this.isAuthenticated) {
            return false;
        }
        
        // Admin and instructor have access to all modules
        if (this.currentUser.role === 'admin' || this.currentUser.role === 'instructor') {
            return true;
        }
        
        // For students, check module-specific access
        // This is a simplified example - in a real app, you'd have more complex access control
        const restrictedModules = ['advanced-troubleshooting', 'system-calibration'];
        
        if (restrictedModules.includes(moduleId)) {
            // These modules require special access
            return false;
        }
        
        return true;
    }
}

/**
 * Content encryption/decryption functions
 */

/**
 * Encrypt data
 * @param {string} data - Data to encrypt
 * @param {string} key - Encryption key
 * @returns {string} - Encrypted data
 */
function encryptData(data, key) {
    return CryptoJS.AES.encrypt(data, key).toString();
}

/**
 * Decrypt data
 * @param {string} encryptedData - Encrypted data
 * @param {string} key - Encryption key
 * @returns {string} - Decrypted data
 */
function decryptData(encryptedData, key) {
    const bytes = CryptoJS.AES.decrypt(encryptedData, key);
    return bytes.toString(CryptoJS.enc.Utf8);
}

/**
 * Load CryptoJS library dynamically
 */
function loadCryptoJSLibrary() {
    return new Promise((resolve, reject) => {
        const script = document.createElement('script');
        script.src = 'https://cdnjs.cloudflare.com/ajax/libs/crypto-js/4.1.1/crypto-js.min.js';
        script.integrity = 'sha512-E8QSvWZ0eCLGk4km3hxSsNmGWbLtSCSUcewDQPQWZF6pEU8GlT8a5fF32wOl1i8ftdMhssTrF/OhyGWwonTcXA==';
        script.crossOrigin = 'anonymous';
        script.referrerPolicy = 'no-referrer';
        
        script.onload = () => {
            console.log('CryptoJS loaded successfully');
            resolve();
        };
        
        script.onerror = () => {
            console.error('Failed to load CryptoJS');
            reject(new Error('Failed to load CryptoJS'));
        };
        
        document.head.appendChild(script);
    });
}

// Initialize authentication
const auth = new Auth();

// Export for use in other modules
window.XrayTrainingAuth = auth;
