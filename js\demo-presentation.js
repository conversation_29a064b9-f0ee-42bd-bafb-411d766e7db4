/**
 * VirtualX Pro - Competition Demo Presentation
 * Interactive demonstration script for GE/Philips competition
 */

class DemoPresentation {
    constructor() {
        this.currentSlide = 0;
        this.demoSteps = [
            'welcome',
            'multi-vendor-support',
            '3d-visualization',
            'ai-diagnostics',
            'collaboration-tools',
            'analytics-dashboard',
            'mobile-ar',
            'roi-metrics'
        ];
        this.init();
    }

    init() {
        this.createDemoInterface();
        this.setupEventListeners();
        this.startDemo();
    }

    createDemoInterface() {
        // Create demo overlay
        const demoOverlay = document.createElement('div');
        demoOverlay.id = 'demo-overlay';
        demoOverlay.className = 'demo-overlay';
        demoOverlay.innerHTML = `
            <div class="demo-container">
                <div class="demo-header">
                    <h2>VirtualX Pro - Live Demonstration</h2>
                    <div class="demo-progress">
                        <div class="progress-bar">
                            <div class="progress-fill" id="demo-progress"></div>
                        </div>
                        <span class="progress-text">Step <span id="current-step">1</span> of ${this.demoSteps.length}</span>
                    </div>
                </div>
                
                <div class="demo-content" id="demo-content">
                    <!-- Dynamic content will be inserted here -->
                </div>
                
                <div class="demo-controls">
                    <button type="button" id="demo-prev" class="btn btn-outline" disabled>
                        <i class="fas fa-chevron-left"></i> Previous
                    </button>
                    <button type="button" id="demo-next" class="btn btn-primary">
                        Next <i class="fas fa-chevron-right"></i>
                    </button>
                    <button type="button" id="demo-close" class="btn btn-outline">
                        <i class="fas fa-times"></i> Close Demo
                    </button>
                </div>
            </div>
        `;
        
        document.body.appendChild(demoOverlay);
    }

    setupEventListeners() {
        document.getElementById('demo-next').addEventListener('click', () => {
            this.nextSlide();
        });
        
        document.getElementById('demo-prev').addEventListener('click', () => {
            this.prevSlide();
        });
        
        document.getElementById('demo-close').addEventListener('click', () => {
            this.closeDemo();
        });
        
        // Keyboard navigation
        document.addEventListener('keydown', (e) => {
            if (document.getElementById('demo-overlay')) {
                switch(e.key) {
                    case 'ArrowRight':
                    case ' ':
                        e.preventDefault();
                        this.nextSlide();
                        break;
                    case 'ArrowLeft':
                        e.preventDefault();
                        this.prevSlide();
                        break;
                    case 'Escape':
                        e.preventDefault();
                        this.closeDemo();
                        break;
                }
            }
        });
    }

    startDemo() {
        this.showSlide(0);
    }

    nextSlide() {
        if (this.currentSlide < this.demoSteps.length - 1) {
            this.currentSlide++;
            this.showSlide(this.currentSlide);
        }
    }

    prevSlide() {
        if (this.currentSlide > 0) {
            this.currentSlide--;
            this.showSlide(this.currentSlide);
        }
    }

    showSlide(index) {
        const step = this.demoSteps[index];
        const content = this.getSlideContent(step);
        
        document.getElementById('demo-content').innerHTML = content;
        document.getElementById('current-step').textContent = index + 1;
        
        // Update progress bar
        const progress = ((index + 1) / this.demoSteps.length) * 100;
        document.getElementById('demo-progress').style.width = `${progress}%`;
        
        // Update button states
        document.getElementById('demo-prev').disabled = index === 0;
        const nextBtn = document.getElementById('demo-next');
        if (index === this.demoSteps.length - 1) {
            nextBtn.innerHTML = '<i class="fas fa-check"></i> Finish Demo';
            nextBtn.onclick = () => this.closeDemo();
        } else {
            nextBtn.innerHTML = 'Next <i class="fas fa-chevron-right"></i>';
            nextBtn.onclick = () => this.nextSlide();
        }
        
        // Execute slide-specific animations
        this.executeSlideAnimations(step);
    }

    getSlideContent(step) {
        const slides = {
            'welcome': `
                <div class="demo-slide welcome-slide">
                    <div class="slide-hero">
                        <h1>Welcome to VirtualX Pro</h1>
                        <p class="slide-subtitle">The Future of X-ray System Training</p>
                        <div class="welcome-features">
                            <div class="feature-highlight">
                                <i class="fas fa-globe"></i>
                                <span>Global Multi-Vendor Support</span>
                            </div>
                            <div class="feature-highlight">
                                <i class="fas fa-brain"></i>
                                <span>AI-Powered Diagnostics</span>
                            </div>
                            <div class="feature-highlight">
                                <i class="fas fa-users"></i>
                                <span>Real-time Collaboration</span>
                            </div>
                        </div>
                    </div>
                    <div class="slide-stats">
                        <div class="stat-item">
                            <div class="stat-number">50+</div>
                            <div class="stat-label">System Models</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">95%</div>
                            <div class="stat-label">Accuracy</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">120+</div>
                            <div class="stat-label">Countries</div>
                        </div>
                    </div>
                </div>
            `,
            
            'multi-vendor-support': `
                <div class="demo-slide vendor-slide">
                    <h2>Comprehensive Multi-Vendor Support</h2>
                    <p>Unlike single-vendor solutions, VirtualX Pro supports all major X-ray manufacturers</p>
                    
                    <div class="vendor-comparison">
                        <div class="vendor-column">
                            <h3>Traditional Training</h3>
                            <div class="limitation-item">
                                <i class="fas fa-times text-danger"></i>
                                <span>Single vendor focus</span>
                            </div>
                            <div class="limitation-item">
                                <i class="fas fa-times text-danger"></i>
                                <span>Limited system coverage</span>
                            </div>
                            <div class="limitation-item">
                                <i class="fas fa-times text-danger"></i>
                                <span>Separate training programs</span>
                            </div>
                        </div>
                        
                        <div class="vendor-column">
                            <h3>VirtualX Pro</h3>
                            <div class="advantage-item">
                                <i class="fas fa-check text-success"></i>
                                <span>All major vendors supported</span>
                            </div>
                            <div class="advantage-item">
                                <i class="fas fa-check text-success"></i>
                                <span>50+ system models</span>
                            </div>
                            <div class="advantage-item">
                                <i class="fas fa-check text-success"></i>
                                <span>Unified training platform</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="vendor-showcase">
                        <div class="vendor-item active" data-vendor="ge">
                            <img src="images/vendors/ge-logo.png" alt="GE Healthcare" onerror="this.style.display='none'">
                            <span>15 Models</span>
                        </div>
                        <div class="vendor-item" data-vendor="philips">
                            <img src="images/vendors/philips-logo.png" alt="Philips" onerror="this.style.display='none'">
                            <span>12 Models</span>
                        </div>
                        <div class="vendor-item" data-vendor="siemens">
                            <img src="images/vendors/siemens-logo.png" alt="Siemens" onerror="this.style.display='none'">
                            <span>18 Models</span>
                        </div>
                    </div>
                </div>
            `,
            
            '3d-visualization': `
                <div class="demo-slide visualization-slide">
                    <h2>Immersive 3D Visualization</h2>
                    <p>Interactive 3D models provide unprecedented learning experiences</p>
                    
                    <div class="visualization-demo">
                        <div class="demo-canvas-container">
                            <canvas id="demo-3d-canvas" width="500" height="300"></canvas>
                            <div class="canvas-controls">
                                <button type="button" class="control-btn" data-action="rotate">
                                    <i class="fas fa-sync-alt"></i> Rotate
                                </button>
                                <button type="button" class="control-btn" data-action="explode">
                                    <i class="fas fa-expand-arrows-alt"></i> Explode View
                                </button>
                                <button type="button" class="control-btn" data-action="xray">
                                    <i class="fas fa-eye"></i> X-ray View
                                </button>
                            </div>
                        </div>
                        
                        <div class="visualization-features">
                            <h3>Key Features</h3>
                            <ul>
                                <li><i class="fas fa-cube"></i> Photorealistic 3D models</li>
                                <li><i class="fas fa-tools"></i> Interactive assembly/disassembly</li>
                                <li><i class="fas fa-search"></i> Component identification</li>
                                <li><i class="fas fa-play"></i> Animated workflows</li>
                                <li><i class="fas fa-mobile-alt"></i> AR integration</li>
                            </ul>
                        </div>
                    </div>
                </div>
            `,
            
            'ai-diagnostics': `
                <div class="demo-slide ai-slide">
                    <h2>AI-Powered Diagnostic Engine</h2>
                    <p>Machine learning algorithms provide intelligent fault detection and resolution</p>
                    
                    <div class="ai-demo-container">
                        <div class="diagnostic-interface">
                            <div class="symptom-input">
                                <h3>Symptom Analysis</h3>
                                <div class="symptom-item">
                                    <i class="fas fa-exclamation-triangle text-warning"></i>
                                    <span>Image quality degradation</span>
                                </div>
                                <div class="symptom-item">
                                    <i class="fas fa-bolt text-danger"></i>
                                    <span>Intermittent exposure failures</span>
                                </div>
                                <div class="symptom-item">
                                    <i class="fas fa-thermometer-half text-info"></i>
                                    <span>Tube overheating warnings</span>
                                </div>
                            </div>
                            
                            <div class="ai-analysis">
                                <h3>AI Analysis Results</h3>
                                <div class="diagnosis-result">
                                    <div class="confidence-meter">
                                        <div class="confidence-bar">
                                            <div class="confidence-fill" style="width: 94%"></div>
                                        </div>
                                        <span>94% Confidence</span>
                                    </div>
                                    <div class="diagnosis-text">
                                        <strong>Probable Cause:</strong> X-ray tube filament degradation
                                    </div>
                                    <div class="recommended-actions">
                                        <h4>Recommended Actions:</h4>
                                        <ol>
                                            <li>Check filament current readings</li>
                                            <li>Perform tube conditioning cycle</li>
                                            <li>Verify generator calibration</li>
                                            <li>Schedule tube replacement if needed</li>
                                        </ol>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="ai-metrics">
                            <div class="metric-item">
                                <div class="metric-number">10,000+</div>
                                <div class="metric-label">Training Cases</div>
                            </div>
                            <div class="metric-item">
                                <div class="metric-number">95%</div>
                                <div class="metric-label">Accuracy Rate</div>
                            </div>
                            <div class="metric-item">
                                <div class="metric-number">60%</div>
                                <div class="metric-label">Time Reduction</div>
                            </div>
                        </div>
                    </div>
                </div>
            `,
            
            'collaboration-tools': `
                <div class="demo-slide collaboration-slide">
                    <h2>Real-time Global Collaboration</h2>
                    <p>Connect experts worldwide for immediate assistance and knowledge sharing</p>
                    
                    <div class="collaboration-demo">
                        <div class="video-conference-mockup">
                            <div class="video-grid">
                                <div class="video-participant main">
                                    <div class="video-placeholder">
                                        <i class="fas fa-user"></i>
                                        <span>Field Technician (Tokyo)</span>
                                    </div>
                                </div>
                                <div class="video-participant">
                                    <div class="video-placeholder">
                                        <i class="fas fa-user-tie"></i>
                                        <span>Senior Engineer (Boston)</span>
                                    </div>
                                </div>
                                <div class="video-participant">
                                    <div class="video-placeholder">
                                        <i class="fas fa-user-graduate"></i>
                                        <span>Training Specialist (Munich)</span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="collaboration-tools-bar">
                                <button type="button" class="tool-btn active">
                                    <i class="fas fa-video"></i> Video
                                </button>
                                <button type="button" class="tool-btn">
                                    <i class="fas fa-microphone"></i> Audio
                                </button>
                                <button type="button" class="tool-btn">
                                    <i class="fas fa-desktop"></i> Screen Share
                                </button>
                                <button type="button" class="tool-btn">
                                    <i class="fas fa-pen"></i> Annotate
                                </button>
                                <button type="button" class="tool-btn">
                                    <i class="fas fa-file-alt"></i> Documents
                                </button>
                            </div>
                        </div>
                        
                        <div class="collaboration-benefits">
                            <h3>Benefits</h3>
                            <div class="benefit-item">
                                <i class="fas fa-clock text-success"></i>
                                <span>Instant expert access</span>
                            </div>
                            <div class="benefit-item">
                                <i class="fas fa-globe text-primary"></i>
                                <span>24/7 global support</span>
                            </div>
                            <div class="benefit-item">
                                <i class="fas fa-graduation-cap text-info"></i>
                                <span>Continuous learning</span>
                            </div>
                            <div class="benefit-item">
                                <i class="fas fa-chart-line text-warning"></i>
                                <span>Knowledge sharing</span>
                            </div>
                        </div>
                    </div>
                </div>
            `,
            
            'analytics-dashboard': `
                <div class="demo-slide analytics-slide">
                    <h2>Advanced Analytics Dashboard</h2>
                    <p>Comprehensive insights for optimized training outcomes and performance tracking</p>
                    
                    <div class="analytics-mockup">
                        <div class="dashboard-header">
                            <h3>Global Training Performance</h3>
                            <div class="time-selector">
                                <button type="button" class="time-btn active">30 Days</button>
                                <button type="button" class="time-btn">Quarter</button>
                                <button type="button" class="time-btn">Year</button>
                            </div>
                        </div>
                        
                        <div class="analytics-grid">
                            <div class="analytics-card">
                                <h4>Training Completion</h4>
                                <div class="chart-placeholder">
                                    <div class="chart-bar" style="height: 85%"></div>
                                    <div class="chart-bar" style="height: 92%"></div>
                                    <div class="chart-bar" style="height: 78%"></div>
                                    <div class="chart-bar" style="height: 94%"></div>
                                </div>
                                <div class="chart-value">94.2% Average</div>
                            </div>
                            
                            <div class="analytics-card">
                                <h4>Skill Improvement</h4>
                                <div class="progress-ring">
                                    <div class="ring-value">+47%</div>
                                </div>
                                <div class="chart-value">vs. Traditional Training</div>
                            </div>
                            
                            <div class="analytics-card">
                                <h4>Global Reach</h4>
                                <div class="world-map-placeholder">
                                    <i class="fas fa-globe-americas"></i>
                                    <div class="map-stats">
                                        <span>120 Countries</span>
                                        <span>1,247 Active Users</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `,
            
            'mobile-ar': `
                <div class="demo-slide ar-slide">
                    <h2>Mobile Augmented Reality</h2>
                    <p>Bring virtual training to real equipment with AR technology</p>
                    
                    <div class="ar-demo">
                        <div class="ar-device-mockup">
                            <div class="device-screen">
                                <div class="ar-overlay">
                                    <div class="ar-component" style="top: 20%; left: 30%;">
                                        <div class="ar-label">X-ray Tube</div>
                                        <div class="ar-info">Status: Normal</div>
                                    </div>
                                    <div class="ar-component" style="top: 60%; left: 50%;">
                                        <div class="ar-label">Generator</div>
                                        <div class="ar-info">kVp: 120</div>
                                    </div>
                                    <div class="ar-component" style="top: 40%; left: 70%;">
                                        <div class="ar-label">Detector</div>
                                        <div class="ar-info">Ready</div>
                                    </div>
                                </div>
                                <div class="ar-equipment-outline"></div>
                            </div>
                        </div>
                        
                        <div class="ar-features">
                            <h3>AR Capabilities</h3>
                            <div class="ar-feature">
                                <i class="fas fa-eye"></i>
                                <span>Component identification</span>
                            </div>
                            <div class="ar-feature">
                                <i class="fas fa-info-circle"></i>
                                <span>Real-time status display</span>
                            </div>
                            <div class="ar-feature">
                                <i class="fas fa-tools"></i>
                                <span>Maintenance guidance</span>
                            </div>
                            <div class="ar-feature">
                                <i class="fas fa-video"></i>
                                <span>Remote expert overlay</span>
                            </div>
                        </div>
                    </div>
                </div>
            `,
            
            'roi-metrics': `
                <div class="demo-slide roi-slide">
                    <h2>Measurable Return on Investment</h2>
                    <p>VirtualX Pro delivers quantifiable business value and competitive advantages</p>
                    
                    <div class="roi-dashboard">
                        <div class="roi-summary">
                            <div class="roi-card primary">
                                <div class="roi-number">300%</div>
                                <div class="roi-label">ROI in Year 1</div>
                            </div>
                            <div class="roi-card success">
                                <div class="roi-number">50%</div>
                                <div class="roi-label">Training Time Reduction</div>
                            </div>
                            <div class="roi-card info">
                                <div class="roi-number">40%</div>
                                <div class="roi-label">Cost Savings</div>
                            </div>
                        </div>
                        
                        <div class="roi-breakdown">
                            <h3>Cost-Benefit Analysis</h3>
                            <div class="cost-comparison">
                                <div class="cost-column">
                                    <h4>Traditional Training</h4>
                                    <div class="cost-item">
                                        <span>Travel & Accommodation</span>
                                        <span class="cost-value">$2.5M</span>
                                    </div>
                                    <div class="cost-item">
                                        <span>Equipment Downtime</span>
                                        <span class="cost-value">$1.8M</span>
                                    </div>
                                    <div class="cost-item">
                                        <span>Instructor Costs</span>
                                        <span class="cost-value">$1.2M</span>
                                    </div>
                                    <div class="cost-total">
                                        <span>Total Annual Cost</span>
                                        <span class="cost-value">$5.5M</span>
                                    </div>
                                </div>
                                
                                <div class="cost-column">
                                    <h4>VirtualX Pro</h4>
                                    <div class="cost-item">
                                        <span>Platform License</span>
                                        <span class="cost-value">$300K</span>
                                    </div>
                                    <div class="cost-item">
                                        <span>Implementation</span>
                                        <span class="cost-value">$200K</span>
                                    </div>
                                    <div class="cost-item">
                                        <span>Support & Maintenance</span>
                                        <span class="cost-value">$100K</span>
                                    </div>
                                    <div class="cost-total savings">
                                        <span>Total Annual Cost</span>
                                        <span class="cost-value">$600K</span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="savings-highlight">
                                <i class="fas fa-piggy-bank"></i>
                                <span>Annual Savings: $4.9M</span>
                            </div>
                        </div>
                    </div>
                </div>
            `
        };
        
        return slides[step] || '<div class="demo-slide"><h2>Content not found</h2></div>';
    }

    executeSlideAnimations(step) {
        // Add slide-specific animations and interactions
        switch(step) {
            case '3d-visualization':
                this.setup3DDemo();
                break;
            case 'ai-diagnostics':
                this.animateAIAnalysis();
                break;
            case 'analytics-dashboard':
                this.animateCharts();
                break;
        }
    }

    setup3DDemo() {
        const canvas = document.getElementById('demo-3d-canvas');
        if (!canvas) return;
        
        const ctx = canvas.getContext('2d');
        
        // Simple 3D cube animation
        let rotation = 0;
        const animate = () => {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // Draw rotating cube
            ctx.save();
            ctx.translate(canvas.width / 2, canvas.height / 2);
            ctx.rotate(rotation);
            
            ctx.strokeStyle = '#0066cc';
            ctx.lineWidth = 2;
            ctx.strokeRect(-50, -50, 100, 100);
            
            ctx.strokeStyle = '#00aaff';
            ctx.strokeRect(-40, -40, 100, 100);
            
            ctx.restore();
            
            rotation += 0.02;
            requestAnimationFrame(animate);
        };
        
        animate();
    }

    animateAIAnalysis() {
        const confidenceFill = document.querySelector('.confidence-fill');
        if (confidenceFill) {
            confidenceFill.style.width = '0%';
            setTimeout(() => {
                confidenceFill.style.transition = 'width 2s ease';
                confidenceFill.style.width = '94%';
            }, 500);
        }
    }

    animateCharts() {
        const chartBars = document.querySelectorAll('.chart-bar');
        chartBars.forEach((bar, index) => {
            bar.style.height = '0%';
            setTimeout(() => {
                bar.style.transition = 'height 1s ease';
                bar.style.height = bar.dataset.height || ['85%', '92%', '78%', '94%'][index];
            }, index * 200);
        });
    }

    closeDemo() {
        const overlay = document.getElementById('demo-overlay');
        if (overlay) {
            overlay.remove();
        }
    }
}

// Auto-start demo when page loads (for competition presentation)
document.addEventListener('DOMContentLoaded', () => {
    // Add demo trigger button
    const demoButton = document.createElement('button');
    demoButton.id = 'start-demo';
    demoButton.className = 'btn btn-primary btn-large demo-trigger';
    demoButton.innerHTML = '<i class="fas fa-play"></i> Start Live Demo';
    demoButton.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 1000;
        animation: pulse 2s infinite;
    `;
    
    demoButton.addEventListener('click', () => {
        new DemoPresentation();
        demoButton.style.display = 'none';
    });
    
    document.body.appendChild(demoButton);
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = DemoPresentation;
}
