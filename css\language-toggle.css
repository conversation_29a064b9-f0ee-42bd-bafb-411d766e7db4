/* CSS for Language Toggle Functionality */

/* Language Toggle Button */
.language-toggle-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
}

.language-toggle-btn {
    background-color: #3498db;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 8px 16px;
    font-size: 14px;
    cursor: pointer;
    transition: background-color 0.3s ease;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.language-toggle-btn:hover {
    background-color: #2980b9;
}

/* RTL Support for Arabic */
body.lang-ar {
    font-family: 'Segoe UI', Tahoma, 'Arial Unicode MS', sans-serif;
}

/* RTL adjustments for specific elements */
.lang-ar .info-icon,
.lang-ar .tool-icon {
    margin-right: 0;
    margin-left: 1.5rem;
}

.lang-ar .nav-buttons .btn i.fa-arrow-left {
    transform: rotate(180deg);
}

.lang-ar .nav-buttons .btn i.fa-arrow-right {
    transform: rotate(180deg);
}

/* Adjust padding for RTL */
.lang-ar .info-content,
.lang-ar .test-details,
.lang-ar .component-description {
    padding-right: 1rem;
    padding-left: 0;
}

/* Adjust margins for lists in RTL */
.lang-ar ul,
.lang-ar ol {
    margin-right: 1.5rem;
    margin-left: 0;
}

/* Adjust text alignment for RTL */
.lang-ar {
    text-align: right;
}

.lang-ar .text-center,
.lang-ar .footer-bottom,
.lang-ar .diagram-caption,
.lang-ar .tool-card,
.lang-ar .symbol-item {
    text-align: center;
}

/* Adjust flex direction for RTL layouts */
.lang-ar .info-box,
.lang-ar .test-content,
.lang-ar .component-content {
    flex-direction: row-reverse;
}

/* Adjust language toggle position for RTL */
.lang-ar .language-toggle-container {
    right: auto;
    left: 20px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .language-toggle-container {
        top: 10px;
        right: 10px;
    }
    
    .lang-ar .language-toggle-container {
        right: auto;
        left: 10px;
    }
    
    .language-toggle-btn {
        padding: 6px 12px;
        font-size: 12px;
    }
}
