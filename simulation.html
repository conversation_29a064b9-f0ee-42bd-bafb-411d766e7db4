<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D Simulation Laboratory - VirtualX Pro</title>
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/enterprise.css">
    <link rel="stylesheet" href="css/competition-features.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
    <style>
        .simulation-container {
            max-width: 1600px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .simulation-header {
            text-align: center;
            margin-bottom: 3rem;
            background: var(--enterprise-gradient);
            color: white;
            padding: 3rem 2rem;
            border-radius: var(--border-radius);
        }
        
        .simulation-workspace {
            display: grid;
            grid-template-columns: 1fr 350px;
            gap: 2rem;
            height: 80vh;
            margin-bottom: 2rem;
        }
        
        .simulation-main {
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-enterprise);
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }
        
        .simulation-toolbar {
            background: #f8f9fa;
            padding: 1rem;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .toolbar-section {
            display: flex;
            gap: 0.5rem;
            align-items: center;
        }
        
        .toolbar-btn {
            padding: 0.5rem 1rem;
            background: white;
            border: 1px solid #ddd;
            border-radius: var(--border-radius);
            cursor: pointer;
            transition: var(--transition);
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.9rem;
        }
        
        .toolbar-btn:hover,
        .toolbar-btn.active {
            background: var(--enterprise-primary);
            color: white;
            border-color: var(--enterprise-primary);
        }
        
        .simulation-viewport {
            flex: 1;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            position: relative;
            overflow: hidden;
        }
        
        .viewport-canvas {
            width: 100%;
            height: 100%;
            display: block;
        }
        
        .viewport-overlay {
            position: absolute;
            top: 1rem;
            left: 1rem;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 1rem;
            border-radius: var(--border-radius);
            font-family: monospace;
            font-size: 0.9rem;
        }
        
        .simulation-controls {
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-enterprise);
            padding: 1.5rem;
            height: fit-content;
        }
        
        .control-panel {
            margin-bottom: 2rem;
        }
        
        .control-panel h3 {
            color: var(--enterprise-primary);
            margin-bottom: 1rem;
            font-size: 1.1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .control-group {
            margin-bottom: 1.5rem;
        }
        
        .control-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: #333;
            font-size: 0.9rem;
        }
        
        .control-input {
            width: 100%;
            padding: 0.5rem;
            border: 1px solid #ddd;
            border-radius: var(--border-radius);
            font-size: 0.9rem;
        }
        
        .control-slider {
            width: 100%;
            margin: 0.5rem 0;
        }
        
        .control-value {
            background: #f8f9fa;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-family: monospace;
            font-size: 0.8rem;
            color: var(--enterprise-primary);
            font-weight: bold;
        }
        
        .simulation-modes {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 0.5rem;
            margin-bottom: 1rem;
        }
        
        .mode-btn {
            padding: 0.75rem;
            background: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: var(--border-radius);
            cursor: pointer;
            transition: var(--transition);
            text-align: center;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .mode-btn:hover,
        .mode-btn.active {
            background: var(--enterprise-primary);
            color: white;
            border-color: var(--enterprise-primary);
        }
        
        .physics-parameters {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: var(--border-radius);
            margin-bottom: 1rem;
        }
        
        .parameter-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
            font-size: 0.8rem;
        }
        
        .parameter-label {
            color: #666;
        }
        
        .parameter-value {
            font-weight: bold;
            color: var(--enterprise-primary);
        }
        
        .simulation-scenarios {
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-enterprise);
            padding: 2rem;
            margin-bottom: 2rem;
        }
        
        .scenarios-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-top: 1.5rem;
        }
        
        .scenario-card {
            border: 2px solid #e9ecef;
            border-radius: var(--border-radius);
            padding: 1.5rem;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
        }
        
        .scenario-card:hover {
            border-color: var(--enterprise-primary);
            transform: translateY(-2px);
            box-shadow: var(--shadow-enterprise);
        }
        
        .scenario-card.active {
            border-color: var(--enterprise-primary);
            background: var(--enterprise-light);
        }
        
        .scenario-icon {
            font-size: 2.5rem;
            color: var(--enterprise-primary);
            margin-bottom: 1rem;
        }
        
        .scenario-title {
            font-size: 1.2rem;
            font-weight: 700;
            color: var(--enterprise-dark);
            margin-bottom: 0.5rem;
        }
        
        .scenario-description {
            color: #666;
            font-size: 0.9rem;
            margin-bottom: 1rem;
        }
        
        .scenario-features {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
        }
        
        .feature-tag {
            background: var(--enterprise-light);
            color: var(--enterprise-dark);
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
            font-size: 0.7rem;
            font-weight: 500;
        }
        
        .simulation-status {
            position: absolute;
            bottom: 1rem;
            right: 1rem;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: var(--border-radius);
            font-size: 0.8rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: var(--enterprise-success);
            animation: pulse 2s infinite;
        }
        
        .learning-objectives {
            background: #f8f9fa;
            padding: 2rem;
            border-radius: var(--border-radius);
            margin-bottom: 2rem;
        }
        
        .objectives-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-top: 1.5rem;
        }
        
        .objective-item {
            display: flex;
            align-items: flex-start;
            gap: 1rem;
        }
        
        .objective-icon {
            background: var(--enterprise-primary);
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
        }
        
        .performance-metrics {
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-enterprise);
            padding: 2rem;
        }
        
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1.5rem;
            margin-top: 1.5rem;
        }
        
        .metric-card {
            text-align: center;
            padding: 1.5rem;
            background: #f8f9fa;
            border-radius: var(--border-radius);
        }
        
        .metric-number {
            font-size: 2rem;
            font-weight: bold;
            color: var(--enterprise-primary);
            margin-bottom: 0.5rem;
        }
        
        .metric-label {
            color: #666;
            font-size: 0.9rem;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        
        @media (max-width: 1200px) {
            .simulation-workspace {
                grid-template-columns: 1fr;
                height: auto;
            }
            
            .simulation-main {
                height: 60vh;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="enterprise-nav">
        <div class="container">
            <div class="nav-content">
                <div class="nav-brand">
                    <a href="index.html">
                        <img src="images/virtualx-pro-logo.png" alt="VirtualX Pro" class="nav-logo" onerror="this.style.display='none'">
                        <span class="brand-text">VirtualX Pro</span>
                    </a>
                </div>
                <ul class="main-nav">
                    <li><a href="index.html"><i class="fas fa-home"></i> Dashboard</a></li>
                    <li><a href="system-models.html"><i class="fas fa-cogs"></i> System Models</a></li>
                    <li><a href="training-modules.html"><i class="fas fa-graduation-cap"></i> Training Modules</a></li>
                    <li><a href="simulation.html" class="active"><i class="fas fa-cube"></i> 3D Simulation</a></li>
                    <li><a href="ai-diagnostics.html"><i class="fas fa-stethoscope"></i> AI Diagnostics</a></li>
                    <li><a href="collaboration.html"><i class="fas fa-users"></i> Remote Support</a></li>
                    <li><a href="analytics.html"><i class="fas fa-chart-line"></i> Analytics</a></li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="simulation-container">
        <!-- Header Section -->
        <div class="simulation-header animate__animated animate__fadeInDown">
            <h1>3D Simulation Laboratory</h1>
            <p>Immersive virtual environment for hands-on X-ray system training and experimentation</p>
            <div style="margin-top: 2rem;">
                <div class="stat-item" style="display: inline-block; margin: 0 2rem;">
                    <div class="stat-number" style="font-size: 2rem; font-weight: bold;">Real-time</div>
                    <div class="stat-label">Physics Simulation</div>
                </div>
                <div class="stat-item" style="display: inline-block; margin: 0 2rem;">
                    <div class="stat-number" style="font-size: 2rem; font-weight: bold;">Interactive</div>
                    <div class="stat-label">3D Environment</div>
                </div>
                <div class="stat-item" style="display: inline-block; margin: 0 2rem;">
                    <div class="stat-number" style="font-size: 2rem; font-weight: bold;">Safe</div>
                    <div class="stat-label">Virtual Training</div>
                </div>
            </div>
        </div>

        <!-- Learning Objectives -->
        <div class="learning-objectives">
            <h2 style="text-align: center; color: var(--enterprise-primary); margin-bottom: 1rem;">Simulation Learning Objectives</h2>
            <p style="text-align: center; color: #666; margin-bottom: 2rem;">Master X-ray system operation through realistic virtual simulations</p>
            
            <div class="objectives-grid">
                <div class="objective-item">
                    <div class="objective-icon">
                        <i class="fas fa-play"></i>
                    </div>
                    <div>
                        <h4>System Operation</h4>
                        <p>Practice complete X-ray system operation procedures in a safe virtual environment</p>
                    </div>
                </div>
                
                <div class="objective-item">
                    <div class="objective-icon">
                        <i class="fas fa-atom"></i>
                    </div>
                    <div>
                        <h4>Physics Understanding</h4>
                        <p>Visualize X-ray physics principles including beam generation, attenuation, and image formation</p>
                    </div>
                </div>
                
                <div class="objective-item">
                    <div class="objective-icon">
                        <i class="fas fa-wrench"></i>
                    </div>
                    <div>
                        <h4>Maintenance Procedures</h4>
                        <p>Learn and practice routine maintenance and calibration procedures safely</p>
                    </div>
                </div>
                
                <div class="objective-item">
                    <div class="objective-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div>
                        <h4>Emergency Response</h4>
                        <p>Train for emergency situations and system failures in a controlled environment</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Simulation Workspace -->
        <div class="simulation-workspace">
            <div class="simulation-main">
                <div class="simulation-toolbar">
                    <div class="toolbar-section">
                        <button type="button" class="toolbar-btn active" data-tool="select">
                            <i class="fas fa-mouse-pointer"></i>
                            Select
                        </button>
                        <button type="button" class="toolbar-btn" data-tool="rotate">
                            <i class="fas fa-sync-alt"></i>
                            Rotate
                        </button>
                        <button type="button" class="toolbar-btn" data-tool="zoom">
                            <i class="fas fa-search-plus"></i>
                            Zoom
                        </button>
                        <button type="button" class="toolbar-btn" data-tool="measure">
                            <i class="fas fa-ruler"></i>
                            Measure
                        </button>
                    </div>
                    
                    <div class="toolbar-section">
                        <button type="button" class="toolbar-btn" data-action="reset">
                            <i class="fas fa-undo"></i>
                            Reset View
                        </button>
                        <button type="button" class="toolbar-btn" data-action="fullscreen">
                            <i class="fas fa-expand"></i>
                            Fullscreen
                        </button>
                        <button type="button" class="toolbar-btn" data-action="screenshot">
                            <i class="fas fa-camera"></i>
                            Screenshot
                        </button>
                    </div>
                </div>
                
                <div class="simulation-viewport">
                    <canvas id="simulation-canvas" class="viewport-canvas"></canvas>
                    <div class="viewport-overlay">
                        <div>System: GE Discovery XR656</div>
                        <div>Mode: Interactive Exploration</div>
                        <div>Physics: Real-time</div>
                        <div>FPS: <span id="fps-counter">60</span></div>
                    </div>
                    <div class="simulation-status">
                        <div class="status-indicator"></div>
                        <span>Simulation Running</span>
                    </div>
                </div>
            </div>
            
            <div class="simulation-controls">
                <div class="control-panel">
                    <h3><i class="fas fa-sliders-h"></i> System Parameters</h3>
                    
                    <div class="control-group">
                        <label class="control-label">kVp (Tube Voltage)</label>
                        <input type="range" class="control-slider" min="40" max="150" value="120" id="kvp-slider">
                        <div class="control-value"><span id="kvp-value">120</span> kVp</div>
                    </div>
                    
                    <div class="control-group">
                        <label class="control-label">mAs (Tube Current × Time)</label>
                        <input type="range" class="control-slider" min="1" max="500" value="100" id="mas-slider">
                        <div class="control-value"><span id="mas-value">100</span> mAs</div>
                    </div>
                    
                    <div class="control-group">
                        <label class="control-label">Filtration (mm Al)</label>
                        <input type="range" class="control-slider" min="1" max="10" value="2.5" step="0.1" id="filter-slider">
                        <div class="control-value"><span id="filter-value">2.5</span> mm Al</div>
                    </div>
                    
                    <div class="control-group">
                        <label class="control-label">Collimation</label>
                        <input type="range" class="control-slider" min="5" max="43" value="35" id="collimation-slider">
                        <div class="control-value"><span id="collimation-value">35</span> cm</div>
                    </div>
                </div>
                
                <div class="control-panel">
                    <h3><i class="fas fa-cog"></i> Simulation Mode</h3>
                    <div class="simulation-modes">
                        <button type="button" class="mode-btn active" data-mode="explore">
                            <i class="fas fa-search"></i><br>Explore
                        </button>
                        <button type="button" class="mode-btn" data-mode="operate">
                            <i class="fas fa-play"></i><br>Operate
                        </button>
                        <button type="button" class="mode-btn" data-mode="maintain">
                            <i class="fas fa-tools"></i><br>Maintain
                        </button>
                        <button type="button" class="mode-btn" data-mode="troubleshoot">
                            <i class="fas fa-bug"></i><br>Debug
                        </button>
                    </div>
                </div>
                
                <div class="control-panel">
                    <h3><i class="fas fa-atom"></i> Physics Display</h3>
                    <div class="physics-parameters">
                        <div class="parameter-row">
                            <span class="parameter-label">Beam Energy:</span>
                            <span class="parameter-value" id="beam-energy">120 keV</span>
                        </div>
                        <div class="parameter-row">
                            <span class="parameter-label">Photon Flux:</span>
                            <span class="parameter-value" id="photon-flux">2.5×10⁸ /s</span>
                        </div>
                        <div class="parameter-row">
                            <span class="parameter-label">Dose Rate:</span>
                            <span class="parameter-value" id="dose-rate">15.2 mGy/s</span>
                        </div>
                        <div class="parameter-row">
                            <span class="parameter-label">HVL:</span>
                            <span class="parameter-value" id="hvl">4.2 mm Al</span>
                        </div>
                    </div>
                </div>
                
                <div class="control-panel">
                    <h3><i class="fas fa-eye"></i> Visualization</h3>
                    <div class="control-group">
                        <label class="control-label">
                            <input type="checkbox" checked> X-ray Beam
                        </label>
                    </div>
                    <div class="control-group">
                        <label class="control-label">
                            <input type="checkbox" checked> Component Labels
                        </label>
                    </div>
                    <div class="control-group">
                        <label class="control-label">
                            <input type="checkbox"> Radiation Field
                        </label>
                    </div>
                    <div class="control-group">
                        <label class="control-label">
                            <input type="checkbox"> Safety Zones
                        </label>
                    </div>
                </div>
            </div>
        </div>

        <!-- Simulation Scenarios -->
        <div class="simulation-scenarios">
            <h3 style="color: var(--enterprise-primary); margin-bottom: 1rem;">
                <i class="fas fa-play-circle"></i> Training Scenarios
            </h3>
            <p style="color: #666; margin-bottom: 1.5rem;">Choose from pre-configured training scenarios or create your own custom simulation</p>
            
            <div class="scenarios-grid">
                <div class="scenario-card active" data-scenario="basic-operation">
                    <div class="scenario-icon">
                        <i class="fas fa-play"></i>
                    </div>
                    <div class="scenario-title">Basic System Operation</div>
                    <div class="scenario-description">Learn fundamental X-ray system operation procedures and safety protocols</div>
                    <div class="scenario-features">
                        <span class="feature-tag">Beginner</span>
                        <span class="feature-tag">30 min</span>
                        <span class="feature-tag">Interactive</span>
                    </div>
                </div>
                
                <div class="scenario-card" data-scenario="image-optimization">
                    <div class="scenario-icon">
                        <i class="fas fa-image"></i>
                    </div>
                    <div class="scenario-title">Image Quality Optimization</div>
                    <div class="scenario-description">Practice adjusting exposure parameters to achieve optimal image quality</div>
                    <div class="scenario-features">
                        <span class="feature-tag">Intermediate</span>
                        <span class="feature-tag">45 min</span>
                        <span class="feature-tag">Physics</span>
                    </div>
                </div>
                
                <div class="scenario-card" data-scenario="maintenance">
                    <div class="scenario-icon">
                        <i class="fas fa-wrench"></i>
                    </div>
                    <div class="scenario-title">Preventive Maintenance</div>
                    <div class="scenario-description">Perform routine maintenance tasks and calibration procedures</div>
                    <div class="scenario-features">
                        <span class="feature-tag">Advanced</span>
                        <span class="feature-tag">60 min</span>
                        <span class="feature-tag">Hands-on</span>
                    </div>
                </div>
                
                <div class="scenario-card" data-scenario="emergency">
                    <div class="scenario-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="scenario-title">Emergency Procedures</div>
                    <div class="scenario-description">Train for emergency situations and system failure responses</div>
                    <div class="scenario-features">
                        <span class="feature-tag">Expert</span>
                        <span class="feature-tag">40 min</span>
                        <span class="feature-tag">Critical</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Performance Metrics -->
        <div class="performance-metrics">
            <h3 style="color: var(--enterprise-primary); margin-bottom: 1rem;">
                <i class="fas fa-chart-line"></i> Simulation Performance
            </h3>
            <p style="color: #666; margin-bottom: 1.5rem;">Real-time performance metrics and learning analytics</p>
            
            <div class="metrics-grid">
                <div class="metric-card">
                    <div class="metric-number">98%</div>
                    <div class="metric-label">Accuracy Score</div>
                </div>
                <div class="metric-card">
                    <div class="metric-number">2.3h</div>
                    <div class="metric-label">Session Time</div>
                </div>
                <div class="metric-card">
                    <div class="metric-number">15</div>
                    <div class="metric-label">Tasks Completed</div>
                </div>
                <div class="metric-card">
                    <div class="metric-number">92%</div>
                    <div class="metric-label">Safety Compliance</div>
                </div>
                <div class="metric-card">
                    <div class="metric-number">60</div>
                    <div class="metric-label">FPS Performance</div>
                </div>
                <div class="metric-card">
                    <div class="metric-number">A+</div>
                    <div class="metric-label">Overall Grade</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="js/simulation.js"></script>
</body>
</html>
