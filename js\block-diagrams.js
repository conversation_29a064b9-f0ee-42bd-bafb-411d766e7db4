/**
 * JavaScript for X-ray System Block Diagrams Module
 */

document.addEventListener('DOMContentLoaded', function() {
    // Navigation active state management
    const navLinks = document.querySelectorAll('nav ul li a');
    const sections = document.querySelectorAll('section[id]');
    
    // Smooth scrolling for navigation links
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            // Only prevent default if it's an anchor link
            if (this.getAttribute('href').startsWith('#')) {
                e.preventDefault();
                
                const targetId = this.getAttribute('href');
                const targetSection = document.querySelector(targetId);
                
                if (targetSection) {
                    window.scrollTo({
                        top: targetSection.offsetTop - 60, // Adjust for nav height
                        behavior: 'smooth'
                    });
                    
                    // Update active link
                    navLinks.forEach(link => link.classList.remove('active'));
                    this.classList.add('active');
                }
            }
        });
    });
    
    // Update active navigation link on scroll
    window.addEventListener('scroll', function() {
        let current = '';
        
        sections.forEach(section => {
            const sectionTop = section.offsetTop;
            const sectionHeight = section.clientHeight;
            
            if (pageYOffset >= sectionTop - 100) {
                current = section.getAttribute('id');
            }
        });
        
        navLinks.forEach(link => {
            link.classList.remove('active');
            const href = link.getAttribute('href');
            if (href && href === `#${current}`) {
                link.classList.add('active');
            }
        });
    });
    
    // Initialize interactive block diagrams
    initializeBlockDiagrams();
});

/**
 * Initialize interactive block diagrams
 */
function initializeBlockDiagrams() {
    // Component information for the general system diagram
    const componentInfo = {
        'power-supply': {
            title: 'Power Supply',
            description: 'The power supply subsystem provides electrical power to all components of the X-ray system.',
            functions: [
                'Converts facility power (typically 220-240V AC) to appropriate voltages',
                'Provides stable power to the X-ray generator',
                'Supplies regulated DC power to control electronics',
                'Includes protection circuits for safety',
                'May include battery backup for critical functions'
            ],
            issues: [
                'Power fluctuations affecting image quality',
                'Circuit breaker trips',
                'Capacitor failures',
                'Overheating'
            ]
        },
        'control-system': {
            title: 'Control System',
            description: 'The control system manages all aspects of X-ray system operation, from user interface to exposure control.',
            functions: [
                'Provides user interface for system operation',
                'Controls exposure parameters (kVp, mA, time)',
                'Manages system timing and sequencing',
                'Monitors system status and safety interlocks',
                'Communicates with all subsystems'
            ],
            issues: [
                'User interface failures',
                'Communication errors',
                'Software bugs or crashes',
                'Calibration drift',
                'Safety interlock malfunctions'
            ]
        },
        'generator': {
            title: 'X-ray Generator',
            description: 'The X-ray generator produces the high voltage needed to operate the X-ray tube.',
            functions: [
                'Converts input power to high voltage (40-150 kV)',
                'Controls tube current (mA)',
                'Regulates exposure timing',
                'Provides filament heating current',
                'Includes protection circuits'
            ],
            issues: [
                'High voltage failures',
                'Exposure timing errors',
                'Overheating',
                'Calibration drift',
                'Arcing or breakdown'
            ]
        },
        'tube': {
            title: 'X-ray Tube',
            description: 'The X-ray tube generates X-ray radiation by accelerating electrons into a target material.',
            functions: [
                'Emits electrons from heated filament (cathode)',
                'Accelerates electrons using high voltage',
                'Produces X-rays when electrons strike the anode',
                'Focuses electron beam to specific focal spot',
                'Dissipates heat from the anode'
            ],
            issues: [
                'Filament burnout',
                'Anode track wear',
                'Bearing failure in rotating anodes',
                'Vacuum loss',
                'Overheating'
            ]
        },
        'collimator': {
            title: 'Collimator',
            description: 'The collimator shapes and restricts the X-ray beam to the area of interest.',
            functions: [
                'Limits X-ray field size to reduce patient dose',
                'Provides adjustable aperture for different field sizes',
                'May include filters to modify beam quality',
                'Often includes a light field simulator',
                'May have automatic tracking with detector size'
            ],
            issues: [
                'Misalignment between light and X-ray fields',
                'Mechanical failures of adjustable blades',
                'Light bulb failures',
                'Filter mechanism failures',
                'Calibration drift'
            ]
        },
        'detector': {
            title: 'Detector System',
            description: 'The detector system captures X-rays after they pass through the patient to form an image.',
            functions: [
                'Converts X-ray energy to electronic signals',
                'Provides spatial resolution for image formation',
                'May include anti-scatter grid',
                'Includes readout electronics',
                'Provides raw data for image processing'
            ],
            issues: [
                'Dead pixels or detector elements',
                'Calibration errors',
                'Grid artifacts',
                'Electronic noise',
                'Sensitivity degradation over time'
            ]
        },
        'processing': {
            title: 'Image Processing',
            description: 'The image processing subsystem enhances and processes the raw image data.',
            functions: [
                'Applies corrections for detector non-uniformities',
                'Enhances contrast and detail visibility',
                'Reduces noise',
                'Applies post-processing algorithms',
                'Prepares images for display and storage'
            ],
            issues: [
                'Processing artifacts',
                'Software bugs',
                'Performance issues with large images',
                'Calibration errors',
                'Data corruption'
            ]
        },
        'display': {
            title: 'Display System',
            description: 'The display system presents the final image to the operator.',
            functions: [
                'Displays processed images',
                'Provides tools for image manipulation (zoom, pan, etc.)',
                'May include DICOM viewing capabilities',
                'Supports diagnostic interpretation',
                'May connect to PACS or other network systems'
            ],
            issues: [
                'Display calibration errors',
                'Monitor failures',
                'Color or grayscale inaccuracies',
                'Resolution issues',
                'Network connectivity problems'
            ]
        }
    };
    
    // Add click event listeners to blocks in the general system diagram
    const blocks = document.querySelectorAll('.block');
    const componentDetails = document.getElementById('component-details');
    const defaultInfo = document.querySelector('.default-info');
    
    if (blocks && componentDetails) {
        blocks.forEach(block => {
            block.addEventListener('click', function() {
                const blockId = this.getAttribute('data-block');
                const info = componentInfo[blockId];
                
                if (info) {
                    // Hide default info
                    if (defaultInfo) {
                        defaultInfo.style.display = 'none';
                    }
                    
                    // Remove any existing component info
                    const existingInfo = document.querySelector('.component-info');
                    if (existingInfo) {
                        existingInfo.remove();
                    }
                    
                    // Create and add new component info
                    const infoElement = document.createElement('div');
                    infoElement.className = 'component-info active';
                    infoElement.innerHTML = `
                        <h3>${info.title}</h3>
                        <p>${info.description}</p>
                        <h4>Key Functions</h4>
                        <ul>
                            ${info.functions.map(func => `<li>${func}</li>`).join('')}
                        </ul>
                        <div class="common-issues">
                            <h4>Common Issues</h4>
                            <ul>
                                ${info.issues.map(issue => `<li>${issue}</li>`).join('')}
                            </ul>
                        </div>
                    `;
                    
                    componentDetails.appendChild(infoElement);
                }
            });
        });
        
        // Add reset functionality when clicking outside blocks
        document.addEventListener('click', function(e) {
            if (!e.target.closest('.block') && !e.target.closest('.component-info')) {
                // Show default info
                if (defaultInfo) {
                    defaultInfo.style.display = 'block';
                }
                
                // Remove any existing component info
                const existingInfo = document.querySelector('.component-info');
                if (existingInfo) {
                    existingInfo.remove();
                }
            }
        });
    }
}
