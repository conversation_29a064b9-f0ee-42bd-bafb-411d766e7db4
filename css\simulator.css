/* CSS for X-ray Physics Simulator */

/* Simulator Section */
.simulator-section {
    padding: 3rem 0;
    background-color: #f8f9fa;
}

.simulator-container {
    display: flex;
    flex-wrap: wrap;
    gap: 2rem;
    margin: 2rem 0;
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1);
    padding: 1.5rem;
}

/* Controls Panel */
.simulator-controls {
    flex: 1 1 300px;
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.control-panel, .measurement-panel {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.control-panel h3, .measurement-panel h3 {
    margin-top: 0;
    margin-bottom: 1rem;
    color: #2c3e50;
    border-bottom: 2px solid #3498db;
    padding-bottom: 0.5rem;
}

.control-group {
    margin-bottom: 1.5rem;
}

.control-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.slider {
    width: 100%;
    height: 8px;
    background: #d1d8e0;
    border-radius: 4px;
    outline: none;
    -webkit-appearance: none;
}

.slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 18px;
    height: 18px;
    background: #3498db;
    border-radius: 50%;
    cursor: pointer;
}

.slider::-moz-range-thumb {
    width: 18px;
    height: 18px;
    background: #3498db;
    border-radius: 50%;
    cursor: pointer;
    border: none;
}

#kv-value, #ma-value, #time-value {
    display: inline-block;
    margin-left: 10px;
    font-weight: 600;
    color: #3498db;
}

.parameter-info {
    margin-top: 0.5rem;
    font-size: 0.9rem;
    color: #666;
    background-color: #e9f1f8;
    padding: 0.5rem;
    border-radius: 4px;
    border-left: 3px solid #3498db;
}

#material-select {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #d1d8e0;
    border-radius: 4px;
    background-color: white;
    font-size: 1rem;
}

#expose-btn {
    width: 100%;
    margin-top: 1rem;
    padding: 0.8rem;
    font-size: 1.1rem;
}

/* Measurement Panel */
.measurement-group {
    margin-bottom: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.measurement-group label {
    font-weight: 600;
}

.measurement-value {
    font-weight: 600;
    color: #3498db;
    background-color: #e9f1f8;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    min-width: 80px;
    text-align: center;
}

/* Simulation Display */
.simulation-display {
    flex: 2 1 500px;
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.xray-apparatus {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 1.5rem;
    height: 300px;
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.xray-tube {
    position: relative;
    width: 100%;
    display: flex;
    justify-content: center;
}

.tube-housing {
    width: 80px;
    height: 40px;
    background-color: #34495e;
    border-radius: 8px;
    position: relative;
}

.xray-beam {
    position: absolute;
    top: 40px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 40px solid transparent;
    border-right: 40px solid transparent;
    border-top: 150px solid rgba(52, 152, 219, 0.2);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.xray-beam.active {
    opacity: 1;
}

.phantom-container {
    width: 100%;
    display: flex;
    justify-content: center;
    z-index: 10;
}

.phantom {
    width: 150px;
    height: 100px;
    background-color: rgba(189, 195, 199, 0.5);
    border-radius: 10px;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
}

.phantom-structure {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background-color: rgba(50, 50, 50, 0.3);
    transition: background-color 0.3s ease;
}

.detector-panel {
    width: 100%;
    display: flex;
    justify-content: center;
}

.detector {
    width: 200px;
    height: 30px;
    background-color: #7f8c8d;
    border-radius: 4px;
}

.xray-image {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 1.5rem;
    height: 300px;
    display: flex;
    justify-content: center;
    align-items: center;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.image-placeholder {
    color: #7f8c8d;
    text-align: center;
    font-style: italic;
}

/* Simulator Info */
.simulator-info {
    margin-top: 2rem;
    background-color: white;
    border-radius: 10px;
    padding: 1.5rem;
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1);
}

.simulator-info h3 {
    color: #2c3e50;
    margin-top: 0;
}

.simulator-info ol {
    margin-left: 1.5rem;
    margin-bottom: 1rem;
}

.simulator-info li {
    margin-bottom: 0.5rem;
}

/* Physics Section */
.physics-section {
    padding: 4rem 0;
    background-color: white;
}

.physics-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.physics-card {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1);
}

.physics-card h3 {
    color: #2c3e50;
    margin-top: 0;
    margin-bottom: 1rem;
    border-bottom: 2px solid #3498db;
    padding-bottom: 0.5rem;
}

.physics-card ul {
    margin-left: 1.5rem;
}

.physics-card li {
    margin-bottom: 0.5rem;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .simulator-container {
        flex-direction: column;
    }
    
    .simulator-controls, .simulation-display {
        flex: none;
        width: 100%;
    }
    
    .xray-apparatus, .xray-image {
        height: 250px;
    }
}
