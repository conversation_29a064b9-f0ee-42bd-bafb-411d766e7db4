/* CSS for X-ray System Components Module */

/* Component Overview Section */
.overview {
    padding: 3rem 0;
    background-color: #f8f9fa;
}

.system-diagram {
    margin: 2rem 0;
    text-align: center;
}

.system-diagram img {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1);
}

.diagram-caption {
    margin-top: 1rem;
    font-style: italic;
    color: #666;
}

.component-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-top: 3rem;
}

.component-item {
    background-color: white;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    text-align: center;
}

.component-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
}

.component-item i {
    font-size: 2.5rem;
    color: #3498db;
    margin-bottom: 1rem;
}

.component-item h3 {
    margin-bottom: 0.5rem;
    color: #2c3e50;
}

/* Component Detail Sections */
.component-detail {
    padding: 4rem 0;
}

.component-detail:nth-child(odd) {
    background-color: white;
}

.component-detail:nth-child(even) {
    background-color: #f8f9fa;
}

.component-content {
    display: flex;
    flex-wrap: wrap;
    gap: 2rem;
    align-items: flex-start;
}

.component-image {
    flex: 1 1 300px;
}

.component-image img {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1);
}

.component-description {
    flex: 2 1 400px;
}

.component-description h3 {
    margin: 1.5rem 0 0.5rem;
    color: #2c3e50;
}

.component-description ul {
    margin-left: 1.5rem;
    margin-bottom: 1.5rem;
}

.component-description li {
    margin-bottom: 0.5rem;
}

.interactive-button {
    margin-top: 2rem;
}

.interactive-button .btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.interactive-button .btn i {
    font-size: 1.2rem;
}

/* Navigation Buttons */
.navigation-buttons {
    padding: 2rem 0;
    background-color: #f8f9fa;
    border-top: 1px solid #e9ecef;
}

.nav-buttons {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 1rem;
}

/* Interactive Elements */
.interactive-element {
    margin: 2rem 0;
    padding: 1.5rem;
    background-color: #e9f1f8;
    border-radius: 8px;
    border-left: 4px solid #3498db;
}

.interactive-element h4 {
    margin-bottom: 1rem;
    color: #2c3e50;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .component-content {
        flex-direction: column;
    }
    
    .component-image, .component-description {
        flex: none;
        width: 100%;
    }
    
    .nav-buttons {
        justify-content: center;
    }
    
    .nav-buttons .btn {
        width: 100%;
        text-align: center;
        margin-bottom: 0.5rem;
    }
}
