<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Module 3: Safety & Radiation Protection - VirtualX Pro</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --napkin-primary: #dc2626;
            --napkin-secondary: #7c2d12;
            --napkin-accent: #f59e0b;
            --napkin-success: #10b981;
            --napkin-warning: #f59e0b;
            --napkin-danger: #ef4444;
            --napkin-dark: #1f2937;
            --napkin-light: #fef2f2;
            --napkin-gradient: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
            --napkin-shadow: 0 10px 25px rgba(220, 38, 38, 0.1);
            --napkin-shadow-lg: 0 20px 40px rgba(220, 38, 38, 0.15);
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #fef2f2 0%, #fef3c7 100%);
            min-height: 100vh;
            line-height: 1.6;
        }

        .training-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }

        .module-header {
            text-align: center;
            margin-bottom: 3rem;
            background: var(--napkin-gradient);
            color: white;
            padding: 3rem 2rem;
            border-radius: 20px;
            box-shadow: var(--napkin-shadow-lg);
            position: relative;
            overflow: hidden;
        }

        .module-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="3" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="80" r="2.5" fill="rgba(255,255,255,0.1)"/></svg>');
            animation: float 18s ease-in-out infinite;
        }

        .module-title {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 1rem;
            position: relative;
            z-index: 1;
        }

        .module-subtitle {
            font-size: 1.3rem;
            opacity: 0.9;
            margin-bottom: 2rem;
            position: relative;
            z-index: 1;
        }

        /* ALARA Principles Mind Map */
        .alara-mindmap {
            position: relative;
            background: white;
            border-radius: 20px;
            padding: 4rem;
            margin: 2rem 0;
            box-shadow: var(--napkin-shadow);
            overflow: hidden;
            min-height: 600px;
        }

        .alara-core {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 200px;
            height: 200px;
            background: var(--napkin-gradient);
            border-radius: 50%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 1.2rem;
            text-align: center;
            box-shadow: var(--napkin-shadow-lg);
            cursor: pointer;
            transition: all 0.3s ease;
            z-index: 10;
        }

        .alara-core:hover {
            transform: translate(-50%, -50%) scale(1.1);
            box-shadow: 0 25px 50px rgba(220, 38, 38, 0.3);
        }

        .alara-icon {
            font-size: 3rem;
            margin-bottom: 0.5rem;
        }

        .principle-node {
            position: absolute;
            width: 140px;
            height: 140px;
            background: white;
            border: 3px solid var(--napkin-primary);
            border-radius: 50%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: var(--napkin-shadow);
        }

        .principle-node:hover {
            transform: scale(1.1);
            background: var(--napkin-primary);
            color: white;
            box-shadow: var(--napkin-shadow-lg);
        }

        .principle-icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
            color: var(--napkin-primary);
        }

        .principle-node:hover .principle-icon {
            color: white;
        }

        /* ALARA principle positioning */
        .time-node { top: 15%; left: 50%; transform: translateX(-50%); }
        .distance-node { bottom: 15%; left: 20%; }
        .shielding-node { bottom: 15%; right: 20%; }

        /* Connection lines */
        .alara-line {
            position: absolute;
            background: var(--napkin-primary);
            height: 3px;
            transform-origin: center;
            opacity: 0.6;
            z-index: 1;
            border-radius: 2px;
        }

        .line-time { top: 35%; left: 50%; width: 120px; transform: translateX(-50%) rotate(0deg); }
        .line-distance { bottom: 35%; left: 35%; width: 120px; transform: rotate(45deg); }
        .line-shielding { bottom: 35%; right: 35%; width: 120px; transform: rotate(-45deg); }

        /* Radiation Zones Visualizer */
        .radiation-zones {
            background: var(--napkin-dark);
            border-radius: 20px;
            padding: 2rem;
            margin: 3rem 0;
            color: white;
            position: relative;
            overflow: hidden;
        }

        .zones-header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .zone-simulator {
            width: 100%;
            height: 400px;
            background: #111827;
            border-radius: 15px;
            position: relative;
            overflow: hidden;
            border: 2px solid #374151;
        }

        .radiation-source {
            position: absolute;
            top: 50%;
            left: 30%;
            transform: translate(-50%, -50%);
            width: 40px;
            height: 40px;
            background: var(--napkin-primary);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
            animation: pulse 2s infinite;
        }

        .radiation-zone {
            position: absolute;
            border: 2px solid;
            border-radius: 50%;
            opacity: 0.3;
        }

        .zone-controlled {
            top: 50%;
            left: 30%;
            transform: translate(-50%, -50%);
            width: 200px;
            height: 200px;
            border-color: var(--napkin-warning);
            background: radial-gradient(circle, rgba(245, 158, 11, 0.1) 0%, transparent 70%);
        }

        .zone-supervised {
            top: 50%;
            left: 30%;
            transform: translate(-50%, -50%);
            width: 300px;
            height: 300px;
            border-color: var(--napkin-accent);
            background: radial-gradient(circle, rgba(245, 158, 11, 0.05) 0%, transparent 70%);
        }

        .zone-unrestricted {
            top: 50%;
            left: 30%;
            transform: translate(-50%, -50%);
            width: 380px;
            height: 380px;
            border-color: var(--napkin-success);
            background: radial-gradient(circle, rgba(16, 185, 129, 0.05) 0%, transparent 70%);
        }

        .zone-label {
            position: absolute;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .label-controlled { top: 20%; right: 20%; }
        .label-supervised { top: 10%; right: 10%; }
        .label-unrestricted { top: 5%; right: 5%; }

        /* Emergency Procedures */
        .emergency-procedures {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            margin: 3rem 0;
            box-shadow: var(--napkin-shadow);
        }

        .emergency-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-top: 2rem;
        }

        .emergency-card {
            background: var(--napkin-light);
            padding: 1.5rem;
            border-radius: 12px;
            border-left: 4px solid var(--napkin-primary);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .emergency-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--napkin-shadow);
        }

        .emergency-icon {
            font-size: 2.5rem;
            color: var(--napkin-primary);
            margin-bottom: 1rem;
        }

        .emergency-title {
            font-weight: 600;
            color: var(--napkin-dark);
            margin-bottom: 0.5rem;
            font-size: 1.1rem;
        }

        .emergency-description {
            color: #6b7280;
            font-size: 0.9rem;
        }

        /* Dose Calculator */
        .dose-calculator {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            margin: 3rem 0;
            box-shadow: var(--napkin-shadow);
        }

        .calculator-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            margin-top: 2rem;
        }

        .input-section {
            background: var(--napkin-light);
            padding: 1.5rem;
            border-radius: 12px;
        }

        .input-group {
            margin-bottom: 1.5rem;
        }

        .input-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: var(--napkin-dark);
        }

        .input-field {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }

        .input-field:focus {
            outline: none;
            border-color: var(--napkin-primary);
        }

        .calc-button {
            background: var(--napkin-primary);
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 25px;
            cursor: pointer;
            font-weight: 600;
            font-size: 1rem;
            transition: all 0.3s ease;
            width: 100%;
        }

        .calc-button:hover {
            background: var(--napkin-secondary);
            transform: translateY(-2px);
        }

        .results-section {
            background: #f8fafc;
            padding: 1.5rem;
            border-radius: 12px;
        }

        .result-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem 0;
            border-bottom: 1px solid #e5e7eb;
        }

        .result-label {
            font-weight: 500;
            color: var(--napkin-dark);
        }

        .result-value {
            font-weight: bold;
            color: var(--napkin-primary);
            font-size: 1.1rem;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .module-title {
                font-size: 2rem;
            }
            
            .alara-mindmap {
                padding: 2rem;
                min-height: 400px;
            }
            
            .alara-core {
                width: 150px;
                height: 150px;
                font-size: 1rem;
            }
            
            .principle-node {
                width: 100px;
                height: 100px;
                font-size: 0.8rem;
            }
            
            .calculator-grid {
                grid-template-columns: 1fr;
            }
        }

        /* Animations */
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-15px); }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.1); opacity: 0.8; }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
</head>
<body>
    <div class="training-container">
        <!-- Module Header -->
        <div class="module-header animate__animated animate__fadeInDown">
            <h1 class="module-title">
                <i class="fas fa-shield-alt"></i>
                Safety & Radiation Protection
            </h1>
            <p class="module-subtitle">Comprehensive Safety Protocol Training</p>
            <p>Master ALARA principles, regulatory compliance, and emergency procedures for safe X-ray operation</p>
            
            <div class="module-stats" style="display: flex; justify-content: center; gap: 3rem; margin-top: 2rem;">
                <div class="stat-item">
                    <span style="font-size: 2.5rem; font-weight: bold; display: block;">5</span>
                    <span style="font-size: 0.9rem; opacity: 0.8;">Hours</span>
                </div>
                <div class="stat-item">
                    <span style="font-size: 2.5rem; font-weight: bold; display: block;">ALARA</span>
                    <span style="font-size: 0.9rem; opacity: 0.8;">Principles</span>
                </div>
                <div class="stat-item">
                    <span style="font-size: 2.5rem; font-weight: bold; display: block;">IEC</span>
                    <span style="font-size: 0.9rem; opacity: 0.8;">Standards</span>
                </div>
                <div class="stat-item">
                    <span style="font-size: 2.5rem; font-weight: bold; display: block;">90%</span>
                    <span style="font-size: 0.9rem; opacity: 0.8;">Pass Rate</span>
                </div>
            </div>
        </div>

        <!-- ALARA Principles Mind Map -->
        <div class="alara-mindmap">
            <h2 style="text-align: center; margin-bottom: 3rem; color: var(--napkin-dark); font-size: 1.8rem;">
                ALARA Principles Mind Map
            </h2>
            
            <!-- Central ALARA Core -->
            <div class="alara-core" onclick="showALARAOverview()">
                <div class="alara-icon">
                    <i class="fas fa-shield-alt"></i>
                </div>
                <div>ALARA<br>Principles</div>
            </div>
            
            <!-- Principle Nodes -->
            <div class="principle-node time-node" onclick="showPrinciple('time')">
                <div class="principle-icon">
                    <i class="fas fa-clock"></i>
                </div>
                <div>Time</div>
            </div>
            
            <div class="principle-node distance-node" onclick="showPrinciple('distance')">
                <div class="principle-icon">
                    <i class="fas fa-ruler"></i>
                </div>
                <div>Distance</div>
            </div>
            
            <div class="principle-node shielding-node" onclick="showPrinciple('shielding')">
                <div class="principle-icon">
                    <i class="fas fa-shield-alt"></i>
                </div>
                <div>Shielding</div>
            </div>
            
            <!-- Connection Lines -->
            <div class="alara-line line-time"></div>
            <div class="alara-line line-distance"></div>
            <div class="alara-line line-shielding"></div>
        </div>

        <!-- Radiation Zones Visualizer -->
        <div class="radiation-zones">
            <div class="zones-header">
                <h3 style="font-size: 1.8rem; margin-bottom: 1rem;">
                    <i class="fas fa-radiation"></i>
                    Radiation Zones Visualizer
                </h3>
                <p style="opacity: 0.8;">Interactive visualization of radiation protection zones</p>
            </div>
            
            <div class="zone-simulator">
                <!-- Radiation Source -->
                <div class="radiation-source">
                    <i class="fas fa-radiation"></i>
                </div>
                
                <!-- Radiation Zones -->
                <div class="radiation-zone zone-unrestricted"></div>
                <div class="radiation-zone zone-supervised"></div>
                <div class="radiation-zone zone-controlled"></div>
                
                <!-- Zone Labels -->
                <div class="zone-label label-unrestricted">Unrestricted Area (&lt;2 mrem/hr)</div>
                <div class="zone-label label-supervised">Supervised Area (2-100 mrem/hr)</div>
                <div class="zone-label label-controlled">Controlled Area (&gt;100 mrem/hr)</div>
                
                <!-- Control Panel -->
                <div style="position: absolute; bottom: 1rem; left: 1rem; background: rgba(0,0,0,0.8); padding: 1rem; border-radius: 8px;">
                    <div style="margin-bottom: 0.5rem; font-size: 0.9rem;">
                        <strong>Controls:</strong>
                    </div>
                    <button type="button" onclick="adjustRadiation('increase')" style="background: var(--napkin-primary); color: white; border: none; padding: 0.5rem 1rem; border-radius: 5px; margin-right: 0.5rem; cursor: pointer;">
                        <i class="fas fa-plus"></i> Increase
                    </button>
                    <button type="button" onclick="adjustRadiation('decrease')" style="background: var(--napkin-success); color: white; border: none; padding: 0.5rem 1rem; border-radius: 5px; cursor: pointer;">
                        <i class="fas fa-minus"></i> Decrease
                    </button>
                </div>
            </div>
        </div>

        <!-- Emergency Procedures -->
        <div class="emergency-procedures">
            <h3 style="text-align: center; margin-bottom: 2rem; color: var(--napkin-dark); font-size: 1.8rem;">
                <i class="fas fa-exclamation-triangle"></i>
                Emergency Response Procedures
            </h3>
            
            <div class="emergency-grid">
                <div class="emergency-card" onclick="showEmergencyProcedure('radiation-leak')">
                    <div class="emergency-icon">
                        <i class="fas fa-radiation"></i>
                    </div>
                    <div class="emergency-title">Radiation Leak</div>
                    <div class="emergency-description">
                        Immediate response procedures for suspected radiation leakage incidents
                    </div>
                </div>
                
                <div class="emergency-card" onclick="showEmergencyProcedure('equipment-failure')">
                    <div class="emergency-icon">
                        <i class="fas fa-tools"></i>
                    </div>
                    <div class="emergency-title">Equipment Failure</div>
                    <div class="emergency-description">
                        Safety protocols for X-ray system malfunctions and emergency shutdowns
                    </div>
                </div>
                
                <div class="emergency-card" onclick="showEmergencyProcedure('exposure-incident')">
                    <div class="emergency-icon">
                        <i class="fas fa-user-injured"></i>
                    </div>
                    <div class="emergency-title">Exposure Incident</div>
                    <div class="emergency-description">
                        Response procedures for accidental radiation exposure of personnel or patients
                    </div>
                </div>
                
                <div class="emergency-card" onclick="showEmergencyProcedure('fire-emergency')">
                    <div class="emergency-icon">
                        <i class="fas fa-fire"></i>
                    </div>
                    <div class="emergency-title">Fire Emergency</div>
                    <div class="emergency-description">
                        Fire safety procedures specific to X-ray facilities and equipment
                    </div>
                </div>
            </div>
        </div>

        <!-- Dose Rate Calculator -->
        <div class="dose-calculator">
            <h3 style="text-align: center; margin-bottom: 2rem; color: var(--napkin-dark); font-size: 1.8rem;">
                <i class="fas fa-calculator"></i>
                Interactive Dose Rate Calculator
            </h3>
            
            <div class="calculator-grid">
                <div class="input-section">
                    <h4 style="color: var(--napkin-primary); margin-bottom: 1rem;">Input Parameters</h4>
                    
                    <div class="input-group">
                        <label class="input-label">Source Activity (mCi)</label>
                        <input type="number" class="input-field" id="activity" value="100" min="1" max="1000">
                    </div>
                    
                    <div class="input-group">
                        <label class="input-label">Distance (meters)</label>
                        <input type="number" class="input-field" id="distance" value="2" min="0.1" max="10" step="0.1">
                    </div>
                    
                    <div class="input-group">
                        <label class="input-label">Shielding Material</label>
                        <select class="input-field" id="shielding">
                            <option value="none">No Shielding</option>
                            <option value="lead">Lead (Pb)</option>
                            <option value="concrete">Concrete</option>
                            <option value="steel">Steel</option>
                        </select>
                    </div>
                    
                    <div class="input-group">
                        <label class="input-label">Shielding Thickness (mm)</label>
                        <input type="number" class="input-field" id="thickness" value="0" min="0" max="50">
                    </div>
                    
                    <button type="button" class="calc-button" onclick="calculateDose()">
                        <i class="fas fa-calculator"></i> Calculate Dose Rate
                    </button>
                </div>
                
                <div class="results-section">
                    <h4 style="color: var(--napkin-primary); margin-bottom: 1rem;">Calculation Results</h4>
                    
                    <div class="result-item">
                        <span class="result-label">Unshielded Dose Rate:</span>
                        <span class="result-value" id="unshielded-dose">25.0 mrem/hr</span>
                    </div>
                    
                    <div class="result-item">
                        <span class="result-label">Shielded Dose Rate:</span>
                        <span class="result-value" id="shielded-dose">25.0 mrem/hr</span>
                    </div>
                    
                    <div class="result-item">
                        <span class="result-label">Attenuation Factor:</span>
                        <span class="result-value" id="attenuation-factor">1.0</span>
                    </div>
                    
                    <div class="result-item">
                        <span class="result-label">Safety Classification:</span>
                        <span class="result-value" id="safety-class">Supervised Area</span>
                    </div>
                    
                    <div style="margin-top: 1.5rem; padding: 1rem; background: var(--napkin-light); border-radius: 8px;">
                        <h5 style="color: var(--napkin-primary); margin-bottom: 0.5rem;">Recommendations:</h5>
                        <p id="recommendations" style="font-size: 0.9rem; color: var(--napkin-dark);">
                            Current dose rate requires supervised area designation. Consider additional shielding for unrestricted access.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="js/module-3-safety.js"></script>
</body>
</html>
