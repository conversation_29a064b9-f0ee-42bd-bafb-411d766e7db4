<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VirtualX Pro - Executive Presentation</title>
    <link rel="stylesheet" href="css/enterprise.css">
    <link rel="stylesheet" href="css/competition-features.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .presentation-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
            background: white;
            min-height: 100vh;
        }
        
        .slide {
            display: none;
            padding: 3rem 0;
            text-align: center;
        }
        
        .slide.active {
            display: block;
        }
        
        .slide h1 {
            font-size: 3rem;
            color: var(--enterprise-primary);
            margin-bottom: 2rem;
        }
        
        .slide h2 {
            font-size: 2.5rem;
            color: var(--enterprise-dark);
            margin-bottom: 1.5rem;
        }
        
        .slide-controls {
            position: fixed;
            bottom: 20px;
            right: 20px;
            display: flex;
            gap: 1rem;
            z-index: 1000;
        }
        
        .control-btn {
            padding: 1rem;
            background: var(--enterprise-primary);
            color: white;
            border: none;
            border-radius: 50%;
            cursor: pointer;
            font-size: 1.2rem;
            width: 60px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .slide-counter {
            position: fixed;
            bottom: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 1rem;
            border-radius: 8px;
            z-index: 1000;
        }
        
        .roi-calculator {
            background: #f8f9fa;
            padding: 2rem;
            border-radius: 8px;
            margin: 2rem 0;
        }
        
        .cost-comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            margin: 2rem 0;
        }
        
        .cost-column {
            background: white;
            padding: 2rem;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }
        
        .cost-item {
            display: flex;
            justify-content: space-between;
            padding: 0.5rem 0;
            border-bottom: 1px solid #eee;
        }
        
        .cost-total {
            font-weight: bold;
            font-size: 1.2rem;
            color: var(--enterprise-primary);
            margin-top: 1rem;
            padding-top: 1rem;
            border-top: 2px solid var(--enterprise-primary);
        }
        
        .savings-highlight {
            background: var(--enterprise-success);
            color: white;
            padding: 1.5rem;
            border-radius: 8px;
            font-size: 1.5rem;
            font-weight: bold;
            margin-top: 2rem;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin: 2rem 0;
        }
        
        .feature-card {
            background: white;
            padding: 2rem;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            text-align: left;
        }
        
        .feature-icon {
            font-size: 3rem;
            color: var(--enterprise-primary);
            margin-bottom: 1rem;
        }
        
        .metric-large {
            font-size: 4rem;
            font-weight: bold;
            color: var(--enterprise-primary);
            margin: 1rem 0;
        }
        
        .competitive-matrix {
            margin: 2rem 0;
        }
        
        .matrix-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }
        
        .matrix-table th,
        .matrix-table td {
            padding: 1rem;
            text-align: center;
            border-bottom: 1px solid #eee;
        }
        
        .matrix-table th {
            background: var(--enterprise-primary);
            color: white;
            font-weight: bold;
        }
        
        .matrix-table .check {
            color: var(--enterprise-success);
            font-size: 1.5rem;
        }
        
        .matrix-table .cross {
            color: var(--enterprise-danger);
            font-size: 1.5rem;
        }
        
        .timeline {
            display: flex;
            justify-content: space-between;
            margin: 2rem 0;
            position: relative;
        }
        
        .timeline::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 2px;
            background: var(--enterprise-primary);
            z-index: 1;
        }
        
        .timeline-item {
            background: white;
            padding: 1.5rem;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            position: relative;
            z-index: 2;
            text-align: center;
            min-width: 200px;
        }
        
        .timeline-item h4 {
            color: var(--enterprise-primary);
            margin-bottom: 0.5rem;
        }
    </style>
</head>
<body>
    <div class="presentation-container">
        <!-- Slide 1: Title -->
        <div class="slide active">
            <h1>VirtualX Pro</h1>
            <h2>Next-Generation X-ray System Training Platform</h2>
            <div class="metric-large">300% ROI</div>
            <p style="font-size: 1.5rem; color: #666;">Transforming Global Technical Support Training</p>
            <div style="margin-top: 3rem;">
                <img src="images/virtualx-pro-logo.png" alt="VirtualX Pro" style="max-height: 200px;" onerror="this.style.display='none'">
            </div>
        </div>

        <!-- Slide 2: Problem Statement -->
        <div class="slide">
            <h2>The Challenge</h2>
            <div class="feature-grid">
                <div class="feature-card">
                    <div class="feature-icon"><i class="fas fa-plane"></i></div>
                    <h3>High Training Costs</h3>
                    <p>$2.5M annually in travel and accommodation for global technician training</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon"><i class="fas fa-clock"></i></div>
                    <h3>Equipment Downtime</h3>
                    <p>$1.8M lost revenue from equipment unavailable during training sessions</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon"><i class="fas fa-users"></i></div>
                    <h3>Inconsistent Quality</h3>
                    <p>Variable training quality across different regions and instructors</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon"><i class="fas fa-globe"></i></div>
                    <h3>Scalability Issues</h3>
                    <p>Difficulty scaling training for 5,000+ technicians across 120+ countries</p>
                </div>
            </div>
        </div>

        <!-- Slide 3: Solution Overview -->
        <div class="slide">
            <h2>VirtualX Pro Solution</h2>
            <div class="feature-grid">
                <div class="feature-card">
                    <div class="feature-icon"><i class="fas fa-cube"></i></div>
                    <h3>3D Interactive Learning</h3>
                    <p>Immersive 3D models of 50+ X-ray systems with virtual assembly training</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon"><i class="fas fa-brain"></i></div>
                    <h3>AI-Powered Diagnostics</h3>
                    <p>95% accurate fault detection trained on 10,000+ real service cases</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon"><i class="fas fa-video"></i></div>
                    <h3>Global Collaboration</h3>
                    <p>Real-time expert assistance and knowledge sharing worldwide</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon"><i class="fas fa-industry"></i></div>
                    <h3>Multi-Vendor Support</h3>
                    <p>Unified platform supporting GE, Philips, Siemens, Canon, and more</p>
                </div>
            </div>
        </div>

        <!-- Slide 4: Competitive Advantage -->
        <div class="slide">
            <h2>Competitive Advantage</h2>
            <div class="competitive-matrix">
                <table class="matrix-table">
                    <thead>
                        <tr>
                            <th>Feature</th>
                            <th>Traditional Training</th>
                            <th>Competitor A</th>
                            <th>VirtualX Pro</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Multi-Vendor Support</td>
                            <td><i class="fas fa-times cross"></i></td>
                            <td><i class="fas fa-times cross"></i></td>
                            <td><i class="fas fa-check check"></i></td>
                        </tr>
                        <tr>
                            <td>3D Visualization</td>
                            <td><i class="fas fa-times cross"></i></td>
                            <td>Basic</td>
                            <td><i class="fas fa-check check"></i> Advanced</td>
                        </tr>
                        <tr>
                            <td>AI Diagnostics</td>
                            <td><i class="fas fa-times cross"></i></td>
                            <td><i class="fas fa-times cross"></i></td>
                            <td><i class="fas fa-check check"></i> 95% Accuracy</td>
                        </tr>
                        <tr>
                            <td>Global Collaboration</td>
                            <td><i class="fas fa-times cross"></i></td>
                            <td><i class="fas fa-times cross"></i></td>
                            <td><i class="fas fa-check check"></i></td>
                        </tr>
                        <tr>
                            <td>Proven ROI</td>
                            <td>Negative</td>
                            <td>Unknown</td>
                            <td><i class="fas fa-check check"></i> 300%+</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Slide 5: ROI Analysis -->
        <div class="slide">
            <h2>Return on Investment</h2>
            <div class="roi-calculator">
                <div class="cost-comparison">
                    <div class="cost-column">
                        <h3>Traditional Training (Annual)</h3>
                        <div class="cost-item">
                            <span>Travel & Accommodation</span>
                            <span>$2.5M</span>
                        </div>
                        <div class="cost-item">
                            <span>Equipment Downtime</span>
                            <span>$1.8M</span>
                        </div>
                        <div class="cost-item">
                            <span>Instructor Costs</span>
                            <span>$1.2M</span>
                        </div>
                        <div class="cost-total">
                            <span>Total Annual Cost</span>
                            <span>$5.5M</span>
                        </div>
                    </div>
                    
                    <div class="cost-column">
                        <h3>VirtualX Pro (Annual)</h3>
                        <div class="cost-item">
                            <span>Platform License</span>
                            <span>$300K</span>
                        </div>
                        <div class="cost-item">
                            <span>Implementation (Year 1)</span>
                            <span>$200K</span>
                        </div>
                        <div class="cost-item">
                            <span>Support & Maintenance</span>
                            <span>$100K</span>
                        </div>
                        <div class="cost-total">
                            <span>Total Annual Cost</span>
                            <span>$600K</span>
                        </div>
                    </div>
                </div>
                
                <div class="savings-highlight">
                    <i class="fas fa-piggy-bank"></i>
                    Annual Savings: $4.9M | ROI: 817%
                </div>
            </div>
        </div>

        <!-- Slide 6: Implementation Timeline -->
        <div class="slide">
            <h2>Implementation Timeline</h2>
            <div class="timeline">
                <div class="timeline-item">
                    <h4>Month 1-3</h4>
                    <h5>Foundation</h5>
                    <p>Core platform deployment, user onboarding, 20 system models</p>
                </div>
                <div class="timeline-item">
                    <h4>Month 4-6</h4>
                    <h5>Advanced Features</h5>
                    <p>AI diagnostics, collaboration tools, full system library</p>
                </div>
                <div class="timeline-item">
                    <h4>Month 7-12</h4>
                    <h5>Global Rollout</h5>
                    <p>International deployment, mobile AR, advanced analytics</p>
                </div>
            </div>
            <div style="margin-top: 3rem;">
                <div class="metric-large">90 Days</div>
                <p style="font-size: 1.2rem;">To Full Value Realization</p>
            </div>
        </div>

        <!-- Slide 7: Success Metrics -->
        <div class="slide">
            <h2>Proven Results</h2>
            <div class="feature-grid">
                <div class="feature-card" style="text-align: center;">
                    <div class="metric-large">50%</div>
                    <h3>Faster Training</h3>
                    <p>Completion time reduction vs traditional methods</p>
                </div>
                <div class="feature-card" style="text-align: center;">
                    <div class="metric-large">95%</div>
                    <h3>Diagnostic Accuracy</h3>
                    <p>AI-powered fault detection success rate</p>
                </div>
                <div class="feature-card" style="text-align: center;">
                    <div class="metric-large">85%</div>
                    <h3>Learning Retention</h3>
                    <p>Improvement over traditional training methods</p>
                </div>
                <div class="feature-card" style="text-align: center;">
                    <div class="metric-large">24/7</div>
                    <h3>Global Support</h3>
                    <p>Expert assistance availability worldwide</p>
                </div>
            </div>
        </div>

        <!-- Slide 8: Next Steps -->
        <div class="slide">
            <h2>Partnership Opportunity</h2>
            <div style="text-align: left; max-width: 800px; margin: 0 auto;">
                <h3 style="color: var(--enterprise-primary); margin-bottom: 2rem;">Proposed Next Steps:</h3>
                <div style="font-size: 1.3rem; line-height: 2;">
                    <p><strong>1. Pilot Program (30 days)</strong><br>
                    Test with 50 technicians, measure immediate impact</p>
                    
                    <p><strong>2. Proof of Concept (90 days)</strong><br>
                    Full feature evaluation, ROI validation</p>
                    
                    <p><strong>3. Strategic Partnership</strong><br>
                    Global deployment, joint development, co-marketing</p>
                </div>
            </div>
            
            <div style="margin-top: 3rem; padding: 2rem; background: var(--enterprise-light); border-radius: 8px;">
                <h3 style="color: var(--enterprise-primary);">Guarantee</h3>
                <p style="font-size: 1.2rem;">Measurable improvements in training effectiveness within 30 days, or full refund</p>
            </div>
        </div>
    </div>

    <!-- Slide Controls -->
    <div class="slide-controls">
        <button type="button" class="control-btn" id="prev-slide" title="Previous Slide">
            <i class="fas fa-chevron-left"></i>
        </button>
        <button type="button" class="control-btn" id="next-slide" title="Next Slide">
            <i class="fas fa-chevron-right"></i>
        </button>
    </div>

    <!-- Slide Counter -->
    <div class="slide-counter">
        <span id="current-slide-num">1</span> / <span id="total-slides">8</span>
    </div>

    <script>
        class ExecutivePresentation {
            constructor() {
                this.currentSlide = 0;
                this.slides = document.querySelectorAll('.slide');
                this.totalSlides = this.slides.length;
                this.init();
            }

            init() {
                document.getElementById('total-slides').textContent = this.totalSlides;
                this.setupEventListeners();
                this.updateSlideCounter();
            }

            setupEventListeners() {
                document.getElementById('prev-slide').addEventListener('click', () => this.prevSlide());
                document.getElementById('next-slide').addEventListener('click', () => this.nextSlide());
                
                document.addEventListener('keydown', (e) => {
                    switch(e.key) {
                        case 'ArrowRight':
                        case ' ':
                            e.preventDefault();
                            this.nextSlide();
                            break;
                        case 'ArrowLeft':
                            e.preventDefault();
                            this.prevSlide();
                            break;
                    }
                });
            }

            nextSlide() {
                if (this.currentSlide < this.totalSlides - 1) {
                    this.slides[this.currentSlide].classList.remove('active');
                    this.currentSlide++;
                    this.slides[this.currentSlide].classList.add('active');
                    this.updateSlideCounter();
                }
            }

            prevSlide() {
                if (this.currentSlide > 0) {
                    this.slides[this.currentSlide].classList.remove('active');
                    this.currentSlide--;
                    this.slides[this.currentSlide].classList.add('active');
                    this.updateSlideCounter();
                }
            }

            updateSlideCounter() {
                document.getElementById('current-slide-num').textContent = this.currentSlide + 1;
            }
        }

        document.addEventListener('DOMContentLoaded', () => {
            new ExecutivePresentation();
        });
    </script>
</body>
</html>
