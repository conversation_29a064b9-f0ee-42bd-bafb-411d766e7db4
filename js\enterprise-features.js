/**
 * Enterprise Features JavaScript
 * Advanced functionality for competition-winning X-ray training platform
 */

class EnterpriseFeatures {
    constructor() {
        this.init();
        this.setupEventListeners();
        this.initializeAnimations();
        this.setupHeroCanvas();
    }

    init() {
        // Initialize enterprise features
        this.animateCounters();
        this.setupLearningPaths();
        this.initializeProgressTracking();
        this.setupVendorCarousel();
    }

    setupEventListeners() {
        // Learning path selector
        document.querySelectorAll('.path-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.switchLearningPath(e.target.dataset.path);
            });
        });

        // Module preview buttons
        document.querySelectorAll('[data-action="preview"]').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.showModulePreview(e.target.closest('.module-card'));
            });
        });

        // System model interactions
        document.querySelectorAll('.model-card').forEach(card => {
            card.addEventListener('click', (e) => {
                this.showSystemDetails(e.currentTarget);
            });
        });

        // Language selector
        const languageSelect = document.getElementById('language-select');
        if (languageSelect) {
            languageSelect.addEventListener('change', (e) => {
                this.changeLanguage(e.target.value);
            });
        }
    }

    initializeAnimations() {
        // Intersection Observer for scroll animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate__animated', 'animate__fadeInUp');
                }
            });
        }, observerOptions);

        // Observe all sections
        document.querySelectorAll('section').forEach(section => {
            observer.observe(section);
        });
    }

    animateCounters() {
        const counters = document.querySelectorAll('[data-count]');
        
        const animateCounter = (element) => {
            const target = parseInt(element.dataset.count);
            const duration = 2000; // 2 seconds
            const increment = target / (duration / 16); // 60fps
            let current = 0;

            const updateCounter = () => {
                current += increment;
                if (current < target) {
                    element.textContent = Math.floor(current);
                    requestAnimationFrame(updateCounter);
                } else {
                    element.textContent = target;
                }
            };

            updateCounter();
        };

        // Animate counters when they come into view
        const counterObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    animateCounter(entry.target);
                    counterObserver.unobserve(entry.target);
                }
            });
        });

        counters.forEach(counter => {
            counterObserver.observe(counter);
        });
    }

    setupLearningPaths() {
        const pathData = {
            foundation: [
                {
                    title: "Interactive 3D System Components",
                    duration: "4 hours",
                    difficulty: "foundation",
                    features: ["3D Interactive Models", "Virtual Assembly", "Multi-Vendor Support"]
                },
                {
                    title: "Interactive System Architecture", 
                    duration: "3 hours",
                    difficulty: "foundation",
                    features: ["Dynamic Flow Diagrams", "Signal Path Tracing", "Component Integration"]
                }
            ],
            advanced: [
                {
                    title: "Advanced Circuit Analysis",
                    duration: "6 hours", 
                    difficulty: "advanced",
                    features: ["SPICE Simulation", "Fault Injection", "Oscilloscope Training"]
                },
                {
                    title: "AI-Powered Diagnostics",
                    duration: "5 hours",
                    difficulty: "advanced", 
                    features: ["Machine Learning", "Pattern Recognition", "Predictive Analysis"]
                }
            ],
            expert: [
                {
                    title: "Multi-System Integration",
                    duration: "8 hours",
                    difficulty: "expert",
                    features: ["Cross-Platform Analysis", "System Optimization", "Performance Tuning"]
                }
            ],
            specialist: [
                {
                    title: "Field Service Excellence",
                    duration: "10 hours",
                    difficulty: "specialist",
                    features: ["Remote Diagnostics", "Customer Training", "Advanced Troubleshooting"]
                }
            ]
        };

        this.pathData = pathData;
    }

    switchLearningPath(pathType) {
        // Update active button
        document.querySelectorAll('.path-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-path="${pathType}"]`).classList.add('active');

        // Update module grid
        const moduleGrid = document.getElementById('foundation-modules');
        if (moduleGrid && this.pathData[pathType]) {
            this.renderModules(moduleGrid, this.pathData[pathType], pathType);
        }
    }

    renderModules(container, modules, pathType) {
        container.innerHTML = '';
        
        modules.forEach((module, index) => {
            const moduleCard = this.createModuleCard(module, pathType, index);
            container.appendChild(moduleCard);
        });
    }

    createModuleCard(module, pathType, index) {
        const card = document.createElement('div');
        card.className = `module-card enterprise-card animate__animated animate__fadeInUp`;
        card.style.animationDelay = `${index * 0.1}s`;
        
        card.innerHTML = `
            <div class="module-header">
                <div class="module-icon"><i class="fas fa-graduation-cap"></i></div>
                <div class="module-badges">
                    <span class="difficulty-badge ${module.difficulty}">${module.difficulty}</span>
                    <span class="duration-badge">${module.duration}</span>
                </div>
            </div>
            <h3>${module.title}</h3>
            <p>Advanced training module designed for ${pathType} level technicians.</p>
            <div class="module-features">
                ${module.features.map(feature => `
                    <div class="feature-item">
                        <i class="fas fa-check"></i>
                        <span>${feature}</span>
                    </div>
                `).join('')}
            </div>
            <div class="module-progress">
                <div class="progress-bar">
                    <div class="progress-fill progress-0"></div>
                </div>
                <span class="progress-text">Not Started</span>
            </div>
            <div class="module-actions">
                <button type="button" class="btn btn-primary">
                    <i class="fas fa-play"></i> Start Module
                </button>
                <button type="button" class="btn btn-outline btn-small" data-action="preview">
                    <i class="fas fa-eye"></i> Preview
                </button>
            </div>
        `;
        
        return card;
    }

    showModulePreview(moduleCard) {
        const title = moduleCard.querySelector('h3').textContent;
        
        // Create modal for preview
        const modal = document.createElement('div');
        modal.className = 'preview-modal';
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3>${title} - Preview</h3>
                    <button type="button" class="modal-close">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="preview-video">
                        <div class="video-placeholder">
                            <i class="fas fa-play-circle"></i>
                            <p>Interactive Preview Video</p>
                        </div>
                    </div>
                    <div class="preview-features">
                        <h4>What you'll learn:</h4>
                        <ul>
                            <li>Component identification and function</li>
                            <li>Interactive 3D exploration</li>
                            <li>Virtual assembly procedures</li>
                            <li>Multi-vendor comparisons</li>
                        </ul>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary">Start Full Module</button>
                    <button type="button" class="btn btn-outline modal-close">Close Preview</button>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        
        // Close modal functionality
        modal.querySelectorAll('.modal-close').forEach(btn => {
            btn.addEventListener('click', () => {
                modal.remove();
            });
        });
        
        // Close on backdrop click
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.remove();
            }
        });
    }

    setupHeroCanvas() {
        const canvas = document.getElementById('hero-canvas');
        if (!canvas) return;
        
        const ctx = canvas.getContext('2d');
        let animationId;
        
        // Simple 3D-like animation for hero section
        const particles = [];
        const particleCount = 50;
        
        // Initialize particles
        for (let i = 0; i < particleCount; i++) {
            particles.push({
                x: Math.random() * canvas.width,
                y: Math.random() * canvas.height,
                z: Math.random() * 1000,
                speed: Math.random() * 2 + 1
            });
        }
        
        const animate = () => {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // Draw background gradient
            const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
            gradient.addColorStop(0, '#f8f9fa');
            gradient.addColorStop(1, '#e9ecef');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // Update and draw particles
            particles.forEach(particle => {
                particle.z -= particle.speed;
                if (particle.z <= 0) {
                    particle.z = 1000;
                    particle.x = Math.random() * canvas.width;
                    particle.y = Math.random() * canvas.height;
                }
                
                const scale = (1000 - particle.z) / 1000;
                const x = particle.x * scale + canvas.width / 2 * (1 - scale);
                const y = particle.y * scale + canvas.height / 2 * (1 - scale);
                const size = scale * 3;
                
                ctx.fillStyle = `rgba(0, 102, 204, ${scale})`;
                ctx.beginPath();
                ctx.arc(x, y, size, 0, Math.PI * 2);
                ctx.fill();
            });
            
            // Draw central X-ray tube representation
            ctx.strokeStyle = '#0066cc';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.arc(canvas.width / 2, canvas.height / 2, 50, 0, Math.PI * 2);
            ctx.stroke();
            
            // Draw beam lines
            ctx.strokeStyle = '#00aaff';
            ctx.lineWidth = 1;
            for (let i = 0; i < 8; i++) {
                const angle = (i / 8) * Math.PI * 2;
                const x1 = canvas.width / 2 + Math.cos(angle) * 50;
                const y1 = canvas.height / 2 + Math.sin(angle) * 50;
                const x2 = canvas.width / 2 + Math.cos(angle) * 100;
                const y2 = canvas.height / 2 + Math.sin(angle) * 100;
                
                ctx.beginPath();
                ctx.moveTo(x1, y1);
                ctx.lineTo(x2, y2);
                ctx.stroke();
            }
            
            animationId = requestAnimationFrame(animate);
        };
        
        animate();
        
        // Cleanup on page unload
        window.addEventListener('beforeunload', () => {
            if (animationId) {
                cancelAnimationFrame(animationId);
            }
        });
    }

    initializeProgressTracking() {
        // Simulate progress for demo purposes
        const progressBars = document.querySelectorAll('.progress-fill');
        progressBars.forEach((bar, index) => {
            setTimeout(() => {
                const progress = Math.random() * 100;
                bar.style.width = `${progress}%`;
                
                const progressText = bar.closest('.module-progress').querySelector('.progress-text');
                if (progress === 0) {
                    progressText.textContent = 'Not Started';
                } else if (progress < 100) {
                    progressText.textContent = `${Math.floor(progress)}% Complete`;
                } else {
                    progressText.textContent = 'Completed';
                }
            }, index * 200);
        });
    }

    setupVendorCarousel() {
        const vendorLogos = document.querySelector('.vendor-logos');
        if (!vendorLogos) return;
        
        // Add hover effects and rotation
        vendorLogos.addEventListener('mouseenter', () => {
            vendorLogos.style.animationPlayState = 'paused';
        });
        
        vendorLogos.addEventListener('mouseleave', () => {
            vendorLogos.style.animationPlayState = 'running';
        });
    }

    changeLanguage(languageCode) {
        // Simulate language change
        console.log(`Changing language to: ${languageCode}`);
        
        // In a real implementation, this would:
        // 1. Load language pack
        // 2. Update all text elements
        // 3. Adjust layout for RTL languages
        // 4. Save preference to localStorage
        
        // Show loading indicator
        const loadingToast = document.createElement('div');
        loadingToast.className = 'language-loading-toast';
        loadingToast.innerHTML = `
            <i class="fas fa-spinner fa-spin"></i>
            Loading language pack...
        `;
        document.body.appendChild(loadingToast);
        
        setTimeout(() => {
            loadingToast.remove();
            // Show success message
            const successToast = document.createElement('div');
            successToast.className = 'language-success-toast';
            successToast.innerHTML = `
                <i class="fas fa-check"></i>
                Language updated successfully!
            `;
            document.body.appendChild(successToast);
            
            setTimeout(() => {
                successToast.remove();
            }, 3000);
        }, 1500);
    }

    showSystemDetails(systemCard) {
        const systemName = systemCard.querySelector('h4').textContent;
        
        // Create detailed system view modal
        const modal = document.createElement('div');
        modal.className = 'system-details-modal';
        modal.innerHTML = `
            <div class="modal-content large">
                <div class="modal-header">
                    <h3>${systemName} - Technical Specifications</h3>
                    <button type="button" class="modal-close">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="system-details-grid">
                        <div class="system-image">
                            <img src="${systemCard.querySelector('img').src}" alt="${systemName}">
                        </div>
                        <div class="system-specs">
                            <h4>Technical Specifications</h4>
                            <table class="specs-table">
                                <tr><td>Generator Type</td><td>High Frequency</td></tr>
                                <tr><td>kVp Range</td><td>40-150 kVp</td></tr>
                                <tr><td>mA Range</td><td>10-500 mA</td></tr>
                                <tr><td>Detector Type</td><td>Flat Panel</td></tr>
                                <tr><td>Image Matrix</td><td>3072 x 3072</td></tr>
                            </table>
                        </div>
                    </div>
                    <div class="training-modules">
                        <h4>Available Training Modules</h4>
                        <div class="module-list">
                            <div class="module-item">
                                <i class="fas fa-play-circle"></i>
                                <span>System Overview & Components</span>
                            </div>
                            <div class="module-item">
                                <i class="fas fa-play-circle"></i>
                                <span>Maintenance Procedures</span>
                            </div>
                            <div class="module-item">
                                <i class="fas fa-play-circle"></i>
                                <span>Troubleshooting Guide</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary">Start Training</button>
                    <button type="button" class="btn btn-outline">Download Specs</button>
                    <button type="button" class="btn btn-outline modal-close">Close</button>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        
        // Close modal functionality
        modal.querySelectorAll('.modal-close').forEach(btn => {
            btn.addEventListener('click', () => {
                modal.remove();
            });
        });
    }
}

// Initialize enterprise features when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new EnterpriseFeatures();
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = EnterpriseFeatures;
}
