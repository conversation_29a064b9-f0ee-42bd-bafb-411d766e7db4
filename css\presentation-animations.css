/**
 * VirtualX Pro - Advanced Presentation Animations
 * Enhanced visual effects and interactive animations
 */

/* Advanced Gradient Backgrounds */
.slide-1 {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    position: relative;
    overflow: hidden;
}

.slide-1::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 119, 198, 0.2) 0%, transparent 50%);
    animation: backgroundShift 20s ease-in-out infinite;
}

@keyframes backgroundShift {
    0%, 100% { transform: translateX(0) translateY(0); }
    25% { transform: translateX(-10px) translateY(-10px); }
    50% { transform: translateX(10px) translateY(10px); }
    75% { transform: translateX(-5px) translateY(5px); }
}

/* Interactive Card Hover Effects */
.interactive-card {
    position: relative;
    overflow: hidden;
}

.interactive-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.interactive-card:hover::before {
    left: 100%;
}

.interactive-card:hover {
    transform: translateY(-10px) scale(1.05);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.interactive-card:hover .card-icon {
    transform: scale(1.2) rotate(10deg);
    color: #fff;
    text-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
}

/* Module Card Animations */
.module-card {
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    position: relative;
}

.module-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.module-card:hover::after {
    opacity: 1;
    animation: shimmer 1s ease-in-out;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* Progress Step Animations */
.progress-step {
    position: relative;
    transition: all 0.3s ease;
}

.progress-step::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: all 0.3s ease;
}

.progress-step:hover::before {
    width: 80px;
    height: 80px;
}

.progress-step.active {
    animation: pulseGlow 2s ease-in-out infinite;
}

@keyframes pulseGlow {
    0%, 100% {
        box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.7);
    }
    50% {
        box-shadow: 0 0 0 20px rgba(255, 255, 255, 0);
    }
}

/* Statistics Counter Animations */
.stat-number {
    position: relative;
    display: inline-block;
}

.stat-number::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 3px;
    background: linear-gradient(90deg, #fff, transparent);
    transition: width 0.5s ease;
}

.stat-item:hover .stat-number::after {
    width: 100%;
}

/* Icon Animations */
.animated-icon {
    display: inline-block;
    transition: all 0.3s ease;
}

.animated-icon:hover {
    transform: scale(1.2);
    filter: drop-shadow(0 0 10px rgba(255, 255, 255, 0.5));
}

.rotating-icon {
    animation: rotate 3s linear infinite;
}

.floating-icon {
    animation: float 3s ease-in-out infinite;
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

/* Slide Transition Effects */
.slide {
    transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.slide.active {
    animation: slideInRight 0.8s ease-out;
}

.slide.prev {
    animation: slideOutLeft 0.8s ease-out;
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideOutLeft {
    from {
        opacity: 1;
        transform: translateX(0);
    }
    to {
        opacity: 0;
        transform: translateX(-100px);
    }
}

/* Navigation Button Effects */
.nav-btn {
    position: relative;
    overflow: hidden;
}

.nav-btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: all 0.3s ease;
}

.nav-btn:hover::before {
    width: 100px;
    height: 100px;
}

.nav-btn:active {
    transform: scale(0.95);
}

/* Slide Indicator Effects */
.indicator {
    position: relative;
    transition: all 0.3s ease;
}

.indicator::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.5);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: all 0.3s ease;
}

.indicator:hover::before {
    width: 24px;
    height: 24px;
}

.indicator.active {
    animation: indicatorPulse 2s ease-in-out infinite;
}

@keyframes indicatorPulse {
    0%, 100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.7);
    }
    50% {
        transform: scale(1.2);
        box-shadow: 0 0 0 10px rgba(255, 255, 255, 0);
    }
}

/* Feature Tag Animations */
.feature-tag {
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.feature-tag::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s ease;
}

.module-card:hover .feature-tag::before {
    left: 100%;
}

.feature-tag:hover {
    transform: scale(1.1);
    background: rgba(255, 255, 255, 0.4);
}

/* Text Animations */
.slide-title {
    animation: slideInDown 1s ease-out;
}

.slide-subtitle {
    animation: slideInUp 1s ease-out 0.3s both;
}

.slide-description {
    animation: fadeIn 1s ease-out 0.6s both;
}

@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* Particle System */
.particle {
    position: absolute;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    pointer-events: none;
    animation: particleFloat 10s linear infinite;
}

@keyframes particleFloat {
    0% {
        transform: translateY(100vh) rotate(0deg);
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    90% {
        opacity: 1;
    }
    100% {
        transform: translateY(-100vh) rotate(360deg);
        opacity: 0;
    }
}

/* Responsive Animations */
@media (max-width: 768px) {
    .interactive-card:hover {
        transform: translateY(-5px) scale(1.02);
    }
    
    .module-card:hover {
        transform: translateY(-3px);
    }
    
    .animated-icon:hover {
        transform: scale(1.1);
    }
}

/* Accessibility - Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    .animated-icon,
    .rotating-icon,
    .floating-icon,
    .particle {
        animation: none;
    }
    
    .interactive-card:hover,
    .module-card:hover {
        transform: none;
    }
    
    .slide {
        transition: opacity 0.3s ease;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .interactive-card,
    .module-card {
        border: 2px solid #fff;
    }
    
    .feature-tag {
        background: #000;
        color: #fff;
        border: 1px solid #fff;
    }
    
    .nav-btn,
    .indicator {
        border: 2px solid #fff;
    }
}
