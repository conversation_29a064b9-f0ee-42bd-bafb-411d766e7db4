<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D System Models - VirtualX Pro</title>
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/enterprise.css">
    <link rel="stylesheet" href="css/competition-features.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
    <style>
        .models-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .models-header {
            text-align: center;
            margin-bottom: 3rem;
            background: var(--enterprise-gradient);
            color: white;
            padding: 3rem 2rem;
            border-radius: var(--border-radius);
        }
        
        .models-header h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
        }
        
        .models-header p {
            font-size: 1.3rem;
            opacity: 0.9;
        }
        
        .vendor-tabs {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin-bottom: 2rem;
            flex-wrap: wrap;
        }
        
        .vendor-tab {
            padding: 1rem 2rem;
            background: white;
            border: 2px solid #e9ecef;
            border-radius: var(--border-radius);
            cursor: pointer;
            transition: var(--transition);
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-weight: 600;
        }
        
        .vendor-tab.active {
            background: var(--enterprise-primary);
            color: white;
            border-color: var(--enterprise-primary);
        }
        
        .vendor-tab:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-enterprise);
        }
        
        .models-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }
        
        .model-card {
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-enterprise);
            overflow: hidden;
            transition: var(--transition);
        }
        
        .model-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 30px rgba(0, 102, 204, 0.2);
        }
        
        .model-image {
            height: 250px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }
        
        .model-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .model-3d-preview {
            position: absolute;
            top: 10px;
            right: 10px;
            background: var(--enterprise-primary);
            color: white;
            padding: 0.5rem;
            border-radius: 50%;
            cursor: pointer;
        }
        
        .model-info {
            padding: 2rem;
        }
        
        .model-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--enterprise-dark);
            margin-bottom: 0.5rem;
        }
        
        .model-category {
            color: var(--enterprise-primary);
            font-weight: 600;
            margin-bottom: 1rem;
        }
        
        .model-specs {
            margin: 1.5rem 0;
        }
        
        .spec-item {
            display: flex;
            justify-content: space-between;
            padding: 0.5rem 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .spec-label {
            font-weight: 600;
            color: #666;
        }
        
        .spec-value {
            color: var(--enterprise-dark);
        }
        
        .model-features {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            margin: 1rem 0;
        }
        
        .feature-tag {
            background: var(--enterprise-light);
            color: var(--enterprise-dark);
            padding: 0.25rem 0.75rem;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        
        .model-actions {
            display: flex;
            gap: 1rem;
            margin-top: 1.5rem;
        }
        
        .btn-3d {
            background: var(--enterprise-primary);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: var(--border-radius);
            cursor: pointer;
            font-weight: 600;
            transition: var(--transition);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .btn-3d:hover {
            background: var(--enterprise-secondary);
            transform: translateY(-2px);
        }
        
        .btn-specs {
            background: transparent;
            color: var(--enterprise-primary);
            border: 2px solid var(--enterprise-primary);
            padding: 0.75rem 1.5rem;
            border-radius: var(--border-radius);
            cursor: pointer;
            font-weight: 600;
            transition: var(--transition);
        }
        
        .btn-specs:hover {
            background: var(--enterprise-primary);
            color: white;
        }
        
        .model-viewer {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.9);
            z-index: 10000;
            display: none;
            align-items: center;
            justify-content: center;
        }
        
        .viewer-content {
            background: white;
            border-radius: var(--border-radius);
            width: 90%;
            max-width: 1200px;
            height: 80%;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        
        .viewer-header {
            background: var(--enterprise-gradient);
            color: white;
            padding: 1.5rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .viewer-close {
            background: none;
            border: none;
            color: white;
            font-size: 1.5rem;
            cursor: pointer;
        }
        
        .viewer-body {
            flex: 1;
            display: flex;
        }
        
        .viewer-3d {
            flex: 2;
            background: #f8f9fa;
            position: relative;
        }
        
        .viewer-controls {
            flex: 1;
            padding: 2rem;
            background: white;
            overflow-y: auto;
        }
        
        .control-section {
            margin-bottom: 2rem;
        }
        
        .control-section h3 {
            color: var(--enterprise-primary);
            margin-bottom: 1rem;
        }
        
        .control-buttons {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin-bottom: 1rem;
        }
        
        .control-btn {
            padding: 0.75rem;
            background: var(--enterprise-light);
            border: 1px solid #ddd;
            border-radius: var(--border-radius);
            cursor: pointer;
            transition: var(--transition);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }
        
        .control-btn:hover,
        .control-btn.active {
            background: var(--enterprise-primary);
            color: white;
        }
        
        .learning-objectives {
            background: var(--enterprise-light);
            padding: 2rem;
            border-radius: var(--border-radius);
            margin: 3rem 0;
        }
        
        .objectives-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
        }
        
        .objective-item {
            display: flex;
            align-items: flex-start;
            gap: 1rem;
        }
        
        .objective-icon {
            background: var(--enterprise-primary);
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
        }
        
        .progress-tracker {
            background: white;
            padding: 2rem;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-enterprise);
            margin: 2rem 0;
        }
        
        .progress-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .progress-bar {
            width: 200px;
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            background: var(--enterprise-success);
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="enterprise-nav">
        <div class="container">
            <div class="nav-content">
                <div class="nav-brand">
                    <a href="index.html">
                        <img src="images/virtualx-pro-logo.png" alt="VirtualX Pro" class="nav-logo" onerror="this.style.display='none'">
                        <span class="brand-text">VirtualX Pro</span>
                    </a>
                </div>
                <ul class="main-nav">
                    <li><a href="index.html"><i class="fas fa-home"></i> Dashboard</a></li>
                    <li><a href="system-models.html" class="active"><i class="fas fa-cogs"></i> System Models</a></li>
                    <li><a href="training-modules.html"><i class="fas fa-graduation-cap"></i> Training Modules</a></li>
                    <li><a href="simulation.html"><i class="fas fa-cube"></i> 3D Simulation</a></li>
                    <li><a href="ai-diagnostics.html"><i class="fas fa-stethoscope"></i> AI Diagnostics</a></li>
                    <li><a href="collaboration.html"><i class="fas fa-users"></i> Remote Support</a></li>
                    <li><a href="analytics.html"><i class="fas fa-chart-line"></i> Analytics</a></li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="models-container">
        <!-- Header Section -->
        <div class="models-header animate__animated animate__fadeInDown">
            <h1>Interactive 3D System Models</h1>
            <p>Explore comprehensive X-ray system models from leading manufacturers with immersive 3D visualization and hands-on learning experiences</p>
            <div style="margin-top: 2rem;">
                <div class="stat-item" style="display: inline-block; margin: 0 2rem;">
                    <div class="stat-number" style="font-size: 2rem; font-weight: bold;">50+</div>
                    <div class="stat-label">System Models</div>
                </div>
                <div class="stat-item" style="display: inline-block; margin: 0 2rem;">
                    <div class="stat-number" style="font-size: 2rem; font-weight: bold;">6</div>
                    <div class="stat-label">Manufacturers</div>
                </div>
                <div class="stat-item" style="display: inline-block; margin: 0 2rem;">
                    <div class="stat-number" style="font-size: 2rem; font-weight: bold;">100%</div>
                    <div class="stat-label">Interactive</div>
                </div>
            </div>
        </div>

        <!-- Learning Objectives -->
        <div class="learning-objectives">
            <h2 style="text-align: center; color: var(--enterprise-primary); margin-bottom: 1rem;">Learning Objectives</h2>
            <p style="text-align: center; color: #666; margin-bottom: 2rem;">Master X-ray system components and configurations through interactive 3D exploration</p>
            
            <div class="objectives-grid">
                <div class="objective-item">
                    <div class="objective-icon">
                        <i class="fas fa-eye"></i>
                    </div>
                    <div>
                        <h4>Visual Component Recognition</h4>
                        <p>Identify and understand the function of every component in X-ray systems from major manufacturers</p>
                    </div>
                </div>
                
                <div class="objective-item">
                    <div class="objective-icon">
                        <i class="fas fa-tools"></i>
                    </div>
                    <div>
                        <h4>Assembly & Disassembly</h4>
                        <p>Practice virtual assembly procedures and understand component relationships safely</p>
                    </div>
                </div>
                
                <div class="objective-item">
                    <div class="objective-icon">
                        <i class="fas fa-balance-scale"></i>
                    </div>
                    <div>
                        <h4>Cross-Vendor Comparison</h4>
                        <p>Compare similar systems across different manufacturers to understand design variations</p>
                    </div>
                </div>
                
                <div class="objective-item">
                    <div class="objective-icon">
                        <i class="fas fa-clipboard-check"></i>
                    </div>
                    <div>
                        <h4>Technical Specifications</h4>
                        <p>Learn detailed technical specifications and operational parameters for each system</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Vendor Tabs -->
        <div class="vendor-tabs">
            <div class="vendor-tab active" data-vendor="all">
                <i class="fas fa-globe"></i>
                All Manufacturers
            </div>
            <div class="vendor-tab" data-vendor="ge">
                <i class="fas fa-industry"></i>
                GE Healthcare
            </div>
            <div class="vendor-tab" data-vendor="philips">
                <i class="fas fa-heartbeat"></i>
                Philips Healthcare
            </div>
            <div class="vendor-tab" data-vendor="siemens">
                <i class="fas fa-cog"></i>
                Siemens Healthineers
            </div>
            <div class="vendor-tab" data-vendor="canon">
                <i class="fas fa-camera"></i>
                Canon Medical
            </div>
        </div>

        <!-- Models Grid -->
        <div class="models-grid" id="models-grid">
            <!-- GE Healthcare Models -->
            <div class="model-card" data-vendor="ge">
                <div class="model-image">
                    <img src="images/systems/ge-discovery-xr656.jpg" alt="GE Discovery XR656" onerror="this.src='https://via.placeholder.com/400x250?text=GE+Discovery+XR656'">
                    <div class="model-3d-preview" title="View in 3D">
                        <i class="fas fa-cube"></i>
                    </div>
                </div>
                <div class="model-info">
                    <div class="model-title">Discovery XR656</div>
                    <div class="model-category">GE Healthcare - Digital Radiography</div>
                    <div class="model-specs">
                        <div class="spec-item">
                            <span class="spec-label">Generator Type</span>
                            <span class="spec-value">High Frequency</span>
                        </div>
                        <div class="spec-item">
                            <span class="spec-label">kVp Range</span>
                            <span class="spec-value">40-150 kVp</span>
                        </div>
                        <div class="spec-item">
                            <span class="spec-label">Detector Size</span>
                            <span class="spec-value">43 x 43 cm</span>
                        </div>
                        <div class="spec-item">
                            <span class="spec-label">Image Matrix</span>
                            <span class="spec-value">3072 x 3072</span>
                        </div>
                    </div>
                    <div class="model-features">
                        <span class="feature-tag">AutoExposure Control</span>
                        <span class="feature-tag">Flat Panel Detector</span>
                        <span class="feature-tag">Digital Workflow</span>
                        <span class="feature-tag">DICOM Compatible</span>
                    </div>
                    <div class="model-actions">
                        <button type="button" class="btn-3d" onclick="open3DViewer('ge-discovery-xr656')">
                            <i class="fas fa-cube"></i>
                            Explore 3D
                        </button>
                        <button type="button" class="btn-specs" onclick="showSpecifications('ge-discovery-xr656')">
                            <i class="fas fa-file-alt"></i>
                            Full Specs
                        </button>
                    </div>
                </div>
            </div>

            <!-- Philips Healthcare Models -->
            <div class="model-card" data-vendor="philips">
                <div class="model-image">
                    <img src="images/systems/philips-digitaldiagnost-c90.jpg" alt="Philips DigitalDiagnost C90" onerror="this.src='https://via.placeholder.com/400x250?text=Philips+DigitalDiagnost+C90'">
                    <div class="model-3d-preview" title="View in 3D">
                        <i class="fas fa-cube"></i>
                    </div>
                </div>
                <div class="model-info">
                    <div class="model-title">DigitalDiagnost C90</div>
                    <div class="model-category">Philips Healthcare - Premium Digital Radiography</div>
                    <div class="model-specs">
                        <div class="spec-item">
                            <span class="spec-label">Generator Type</span>
                            <span class="spec-value">High Frequency</span>
                        </div>
                        <div class="spec-item">
                            <span class="spec-label">kVp Range</span>
                            <span class="spec-value">40-150 kVp</span>
                        </div>
                        <div class="spec-item">
                            <span class="spec-label">Detector Size</span>
                            <span class="spec-value">43 x 43 cm</span>
                        </div>
                        <div class="spec-item">
                            <span class="spec-label">Suspension</span>
                            <span class="spec-value">SkyFlow</span>
                        </div>
                    </div>
                    <div class="model-features">
                        <span class="feature-tag">SkyFlow Suspension</span>
                        <span class="feature-tag">Eleva UI</span>
                        <span class="feature-tag">Smart Positioning</span>
                        <span class="feature-tag">Dose Management</span>
                    </div>
                    <div class="model-actions">
                        <button type="button" class="btn-3d" onclick="open3DViewer('philips-digitaldiagnost-c90')">
                            <i class="fas fa-cube"></i>
                            Explore 3D
                        </button>
                        <button type="button" class="btn-specs" onclick="showSpecifications('philips-digitaldiagnost-c90')">
                            <i class="fas fa-file-alt"></i>
                            Full Specs
                        </button>
                    </div>
                </div>
            </div>

            <!-- Siemens Healthineers Models -->
            <div class="model-card" data-vendor="siemens">
                <div class="model-image">
                    <img src="images/systems/siemens-ysio-max.jpg" alt="Siemens Ysio Max" onerror="this.src='https://via.placeholder.com/400x250?text=Siemens+Ysio+Max'">
                    <div class="model-3d-preview" title="View in 3D">
                        <i class="fas fa-cube"></i>
                    </div>
                </div>
                <div class="model-info">
                    <div class="model-title">Ysio Max</div>
                    <div class="model-category">Siemens Healthineers - Digital Radiography</div>
                    <div class="model-specs">
                        <div class="spec-item">
                            <span class="spec-label">Generator Type</span>
                            <span class="spec-value">High Frequency</span>
                        </div>
                        <div class="spec-item">
                            <span class="spec-label">kVp Range</span>
                            <span class="spec-value">40-150 kVp</span>
                        </div>
                        <div class="spec-item">
                            <span class="spec-label">Detector Size</span>
                            <span class="spec-value">43 x 43 cm</span>
                        </div>
                        <div class="spec-item">
                            <span class="spec-label">Technology</span>
                            <span class="spec-value">CARE+CLEAR</span>
                        </div>
                    </div>
                    <div class="model-features">
                        <span class="feature-tag">CARE+CLEAR Technology</span>
                        <span class="feature-tag">Smart Workflow</span>
                        <span class="feature-tag">Dose Optimization</span>
                        <span class="feature-tag">Image Enhancement</span>
                    </div>
                    <div class="model-actions">
                        <button type="button" class="btn-3d" onclick="open3DViewer('siemens-ysio-max')">
                            <i class="fas fa-cube"></i>
                            Explore 3D
                        </button>
                        <button type="button" class="btn-specs" onclick="showSpecifications('siemens-ysio-max')">
                            <i class="fas fa-file-alt"></i>
                            Full Specs
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Progress Tracker -->
        <div class="progress-tracker">
            <h3 style="color: var(--enterprise-primary); margin-bottom: 1.5rem;">
                <i class="fas fa-chart-line"></i> Your Learning Progress
            </h3>
            
            <div class="progress-item">
                <div>
                    <strong>GE Healthcare Systems</strong>
                    <div style="font-size: 0.9rem; color: #666;">5 of 15 models explored</div>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 33%"></div>
                </div>
            </div>
            
            <div class="progress-item">
                <div>
                    <strong>Philips Healthcare Systems</strong>
                    <div style="font-size: 0.9rem; color: #666;">3 of 12 models explored</div>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 25%"></div>
                </div>
            </div>
            
            <div class="progress-item">
                <div>
                    <strong>Siemens Healthineers Systems</strong>
                    <div style="font-size: 0.9rem; color: #666;">7 of 18 models explored</div>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 39%"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- 3D Model Viewer Modal -->
    <div class="model-viewer" id="model-viewer">
        <div class="viewer-content">
            <div class="viewer-header">
                <h3 id="viewer-title">3D Model Viewer</h3>
                <button type="button" class="viewer-close" onclick="close3DViewer()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="viewer-body">
                <div class="viewer-3d" id="viewer-3d">
                    <canvas id="model-canvas" style="width: 100%; height: 100%;"></canvas>
                </div>
                <div class="viewer-controls">
                    <div class="control-section">
                        <h3>View Controls</h3>
                        <div class="control-buttons">
                            <button type="button" class="control-btn active" data-action="rotate">
                                <i class="fas fa-sync-alt"></i>
                                Rotate
                            </button>
                            <button type="button" class="control-btn" data-action="zoom">
                                <i class="fas fa-search-plus"></i>
                                Zoom
                            </button>
                            <button type="button" class="control-btn" data-action="explode">
                                <i class="fas fa-expand-arrows-alt"></i>
                                Explode
                            </button>
                            <button type="button" class="control-btn" data-action="xray">
                                <i class="fas fa-eye"></i>
                                X-ray View
                            </button>
                        </div>
                    </div>
                    
                    <div class="control-section">
                        <h3>Component Highlighting</h3>
                        <div class="control-buttons">
                            <button type="button" class="control-btn" data-component="tube">
                                <i class="fas fa-circle"></i>
                                X-ray Tube
                            </button>
                            <button type="button" class="control-btn" data-component="generator">
                                <i class="fas fa-bolt"></i>
                                Generator
                            </button>
                            <button type="button" class="control-btn" data-component="detector">
                                <i class="fas fa-tv"></i>
                                Detector
                            </button>
                            <button type="button" class="control-btn" data-component="collimator">
                                <i class="fas fa-adjust"></i>
                                Collimator
                            </button>
                        </div>
                    </div>
                    
                    <div class="control-section">
                        <h3>Learning Mode</h3>
                        <div class="control-buttons">
                            <button type="button" class="control-btn" data-mode="explore">
                                <i class="fas fa-search"></i>
                                Explore
                            </button>
                            <button type="button" class="control-btn" data-mode="assembly">
                                <i class="fas fa-tools"></i>
                                Assembly
                            </button>
                            <button type="button" class="control-btn" data-mode="quiz">
                                <i class="fas fa-question-circle"></i>
                                Quiz Mode
                            </button>
                            <button type="button" class="control-btn" data-mode="compare">
                                <i class="fas fa-balance-scale"></i>
                                Compare
                            </button>
                        </div>
                    </div>
                    
                    <div class="control-section">
                        <h3>Component Information</h3>
                        <div id="component-info" style="background: #f8f9fa; padding: 1rem; border-radius: 8px; min-height: 100px;">
                            <p style="color: #666; text-align: center;">Click on a component to see detailed information</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="js/system-models.js"></script>
</body>
</html>
