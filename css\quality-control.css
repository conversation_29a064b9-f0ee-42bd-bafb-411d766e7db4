/* CSS for X-ray System Quality Control Module */

/* Overview Section */
.overview {
    padding: 3rem 0;
    background-color: #f8f9fa;
}

.info-box {
    display: flex;
    background-color: white;
    border-radius: 8px;
    padding: 1.5rem;
    margin-top: 2rem;
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1);
    gap: 1.5rem;
}

.info-icon {
    font-size: 2.5rem;
    color: #3498db;
    flex-shrink: 0;
}

.info-content h3 {
    margin-top: 0;
    color: #2c3e50;
    margin-bottom: 1rem;
}

.info-content ul {
    margin-left: 1.5rem;
}

.info-content li {
    margin-bottom: 0.5rem;
}

/* QC Frequency Table */
.qc-frequency {
    margin-top: 3rem;
    background-color: white;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1);
}

.qc-frequency h3 {
    margin-top: 0;
    color: #2c3e50;
    margin-bottom: 1.5rem;
}

.frequency-table {
    overflow-x: auto;
}

.frequency-table table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 1rem;
}

.frequency-table th, .frequency-table td {
    padding: 0.75rem;
    text-align: center;
    border: 1px solid #e9ecef;
}

.frequency-table th {
    background-color: #3498db;
    color: white;
    font-weight: 600;
}

.frequency-table tr:nth-child(even) {
    background-color: #f8f9fa;
}

.frequency-table td:first-child {
    text-align: left;
    font-weight: 600;
}

.frequency-table i.fa-check {
    color: #2ecc71;
}

.note {
    font-style: italic;
    color: #7f8c8d;
    margin-top: 1rem;
}

/* QC Sections */
.qc-section {
    padding: 4rem 0;
}

.qc-section:nth-child(odd) {
    background-color: white;
}

.qc-section:nth-child(even) {
    background-color: #f8f9fa;
}

.qc-test-container {
    display: flex;
    flex-direction: column;
    gap: 2rem;
    margin-top: 2rem;
}

.qc-test {
    background-color: white;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1);
}

.qc-test h3 {
    margin-top: 0;
    color: #2c3e50;
    margin-bottom: 1.5rem;
    border-bottom: 2px solid #3498db;
    padding-bottom: 0.5rem;
}

.test-content {
    display: flex;
    flex-wrap: wrap;
    gap: 2rem;
}

.test-image {
    flex: 1 1 300px;
    display: flex;
    justify-content: center;
    align-items: flex-start;
}

.test-image img {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.test-details {
    flex: 2 1 400px;
}

.test-details h4 {
    color: #3498db;
    margin-bottom: 0.5rem;
}

.test-details ul, .test-details ol {
    margin-left: 1.5rem;
    margin-bottom: 1.5rem;
}

.test-details li {
    margin-bottom: 0.5rem;
}

.test-procedure-btn {
    margin-top: 1rem;
}

/* QC Tools Grid */
.tools-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.tool-card {
    background-color: white;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.tool-card:hover {
    transform: translateY(-5px);
}

.tool-image {
    height: 150px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 1rem;
}

.tool-image img {
    max-width: 100%;
    max-height: 100%;
    border-radius: 8px;
}

.tool-card h3 {
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.tool-card p {
    color: #7f8c8d;
    font-size: 0.95rem;
}

/* Test Procedure Modal */
.procedure-modal-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.procedure-modal {
    background-color: white;
    border-radius: 8px;
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    overflow: auto;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

.modal-header {
    padding: 1rem;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0 0.5rem;
}

.modal-body {
    padding: 1.5rem;
}

.modal-body h4 {
    color: #3498db;
    margin-top: 1.5rem;
    margin-bottom: 0.5rem;
}

.modal-body ul, .modal-body ol {
    margin-left: 1.5rem;
    margin-bottom: 1rem;
}

.modal-body li {
    margin-bottom: 0.5rem;
}

.modal-body .note {
    background-color: #f8f9fa;
    padding: 1rem;
    border-radius: 8px;
    border-left: 4px solid #3498db;
    margin: 1rem 0;
}

/* Navigation Buttons */
.navigation-buttons {
    padding: 2rem 0;
    background-color: #f8f9fa;
    border-top: 1px solid #e9ecef;
}

.nav-buttons {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 1rem;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .info-box {
        flex-direction: column;
    }
    
    .test-content {
        flex-direction: column;
    }
    
    .test-image, .test-details {
        flex: none;
        width: 100%;
    }
    
    .tools-grid {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    }
    
    .nav-buttons {
        justify-content: center;
    }
    
    .nav-buttons .btn {
        width: 100%;
        text-align: center;
        margin-bottom: 0.5rem;
    }
}
