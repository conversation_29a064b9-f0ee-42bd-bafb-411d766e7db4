<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>X-ray System Block Diagrams | Training Module</title>
    <link rel="stylesheet" href="../css/main.css">
    <link rel="stylesheet" href="../css/block-diagrams.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <header>
        <div class="container">
            <h1>X-ray System Block Diagrams</h1>
            <p>Understanding system architecture and signal flow in X-ray systems</p>
        </div>
    </header>

    <nav>
        <div class="container">
            <ul>
                <li><a href="../index.html">Home</a></li>
                <li><a href="#overview" class="active">Overview</a></li>
                <li><a href="#general-system">General System</a></li>
                <li><a href="#power-supply">Power Supply</a></li>
                <li><a href="#control-system">Control System</a></li>
                <li><a href="#image-chain">Image Chain</a></li>
            </ul>
        </div>
    </nav>

    <main>
        <section id="overview" class="overview">
            <div class="container">
                <h2>Block Diagrams Overview</h2>
                <p>Block diagrams are essential tools for biomedical engineers to understand the system architecture of X-ray equipment. They provide a high-level view of how different components interact, showing signal flow, power distribution, and control mechanisms.</p>
                
                <div class="info-box">
                    <div class="info-icon"><i class="fas fa-info-circle"></i></div>
                    <div class="info-content">
                        <h3>Why Block Diagrams Matter</h3>
                        <p>Block diagrams help biomedical engineers:</p>
                        <ul>
                            <li>Understand the overall system architecture</li>
                            <li>Trace signal and power flow through the system</li>
                            <li>Identify potential failure points</li>
                            <li>Plan maintenance and troubleshooting approaches</li>
                            <li>Communicate system concepts with other technical staff</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

        <section id="general-system" class="diagram-section">
            <div class="container">
                <h2>General X-ray System Block Diagram</h2>
                <p>This diagram shows the major subsystems of a typical diagnostic X-ray system and how they interact.</p>
                
                <div class="diagram-container">
                    <div class="interactive-diagram" id="general-system-diagram">
                        <!-- SVG diagram will be inserted by JavaScript -->
                        <svg width="800" height="500" viewBox="0 0 800 500" class="block-diagram">
                            <!-- Power Supply Section -->
                            <rect x="50" y="50" width="150" height="80" class="block power-block" data-block="power-supply" />
                            <text x="125" y="90" class="block-title">Power Supply</text>
                            
                            <!-- Control System Section -->
                            <rect x="325" y="50" width="150" height="80" class="block control-block" data-block="control-system" />
                            <text x="400" y="90" class="block-title">Control System</text>
                            
                            <!-- X-ray Generator Section -->
                            <rect x="50" y="200" width="150" height="80" class="block generator-block" data-block="generator" />
                            <text x="125" y="240" class="block-title">X-ray Generator</text>
                            
                            <!-- X-ray Tube Section -->
                            <rect x="50" y="350" width="150" height="80" class="block tube-block" data-block="tube" />
                            <text x="125" y="390" class="block-title">X-ray Tube</text>
                            
                            <!-- Collimator Section -->
                            <rect x="325" y="350" width="150" height="80" class="block collimator-block" data-block="collimator" />
                            <text x="400" y="390" class="block-title">Collimator</text>
                            
                            <!-- Detector Section -->
                            <rect x="600" y="350" width="150" height="80" class="block detector-block" data-block="detector" />
                            <text x="675" y="390" class="block-title">Detector System</text>
                            
                            <!-- Image Processing Section -->
                            <rect x="600" y="200" width="150" height="80" class="block processing-block" data-block="processing" />
                            <text x="675" y="240" class="block-title">Image Processing</text>
                            
                            <!-- Display Section -->
                            <rect x="600" y="50" width="150" height="80" class="block display-block" data-block="display" />
                            <text x="675" y="90" class="block-title">Display System</text>
                            
                            <!-- Connection Lines -->
                            <!-- Power to Control -->
                            <line x1="200" y1="90" x2="325" y2="90" class="connection" />
                            <!-- Power to Generator -->
                            <line x1="125" y1="130" x2="125" y2="200" class="connection" />
                            <!-- Control to Generator -->
                            <line x1="325" y1="90" x2="125" y2="90" class="connection" />
                            <line x1="325" y1="90" x2="325" y2="240" class="connection" />
                            <line x1="325" y1="240" x2="200" y2="240" class="connection" />
                            <!-- Generator to Tube -->
                            <line x1="125" y1="280" x2="125" y2="350" class="connection" />
                            <!-- Tube to Collimator -->
                            <line x1="200" y1="390" x2="325" y2="390" class="connection" />
                            <!-- Collimator to Detector -->
                            <line x1="475" y1="390" x2="600" y2="390" class="connection" />
                            <!-- Detector to Processing -->
                            <line x1="675" y1="350" x2="675" y2="280" class="connection" />
                            <!-- Processing to Display -->
                            <line x1="675" y1="200" x2="675" y2="130" class="connection" />
                            <!-- Control to Display -->
                            <line x1="475" y1="90" x2="600" y2="90" class="connection" />
                            <!-- Control to Processing -->
                            <line x1="400" y1="130" x2="400" y2="240" class="connection" />
                            <line x1="400" y1="240" x2="600" y2="240" class="connection" />
                        </svg>
                    </div>
                    
                    <div class="diagram-info" id="general-system-info">
                        <h3>System Component Details</h3>
                        <p>Click on any block in the diagram to see detailed information.</p>
                        <div class="component-details" id="component-details">
                            <div class="default-info">
                                <p>This block diagram shows the major components of a diagnostic X-ray system and their interconnections. The system can be divided into several subsystems:</p>
                                <ul>
                                    <li><strong>Power Supply:</strong> Provides electrical power to all system components</li>
                                    <li><strong>Control System:</strong> Manages exposure parameters and system operation</li>
                                    <li><strong>X-ray Generator:</strong> Produces high voltage for the X-ray tube</li>
                                    <li><strong>X-ray Tube:</strong> Generates X-ray radiation</li>
                                    <li><strong>Collimator:</strong> Shapes and restricts the X-ray beam</li>
                                    <li><strong>Detector System:</strong> Captures X-rays to form an image</li>
                                    <li><strong>Image Processing:</strong> Enhances and processes the raw image data</li>
                                    <li><strong>Display System:</strong> Presents the final image to the operator</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section id="power-supply" class="diagram-section">
            <div class="container">
                <h2>Power Supply Block Diagram</h2>
                <p>The power supply subsystem converts and distributes electrical power to all components of the X-ray system.</p>
                
                <div class="diagram-container">
                    <div class="interactive-diagram" id="power-supply-diagram">
                        <img src="../images/power-supply-diagram.png" alt="Power Supply Block Diagram" onerror="this.src='https://via.placeholder.com/800x400?text=Power+Supply+Block+Diagram'">
                    </div>
                    
                    <div class="diagram-info">
                        <h3>Power Supply Components</h3>
                        <ul>
                            <li><strong>Main Input:</strong> Connects to facility power (typically 220-240V AC)</li>
                            <li><strong>Circuit Breakers:</strong> Provide overcurrent protection</li>
                            <li><strong>EMI Filter:</strong> Reduces electromagnetic interference</li>
                            <li><strong>Power Factor Correction:</strong> Improves power efficiency</li>
                            <li><strong>DC Power Supplies:</strong> Convert AC to various DC voltages for electronics</li>
                            <li><strong>High Voltage Supply:</strong> Provides power to the X-ray generator</li>
                            <li><strong>Battery Backup:</strong> Maintains critical functions during power interruptions</li>
                        </ul>
                        
                        <div class="common-issues">
                            <h4>Common Issues</h4>
                            <ul>
                                <li>Power fluctuations affecting image quality</li>
                                <li>Circuit breaker trips due to overloads</li>
                                <li>Capacitor failures in power supplies</li>
                                <li>Overheating of power components</li>
                                <li>Battery backup failure</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section id="control-system" class="diagram-section">
            <div class="container">
                <h2>Control System Block Diagram</h2>
                <p>The control system manages all aspects of X-ray system operation, from user interface to exposure control.</p>
                
                <div class="diagram-container">
                    <div class="interactive-diagram" id="control-system-diagram">
                        <img src="../images/control-system-diagram.png" alt="Control System Block Diagram" onerror="this.src='https://via.placeholder.com/800x400?text=Control+System+Block+Diagram'">
                    </div>
                    
                    <div class="diagram-info">
                        <h3>Control System Components</h3>
                        <ul>
                            <li><strong>Main CPU:</strong> Central processing unit that coordinates all system functions</li>
                            <li><strong>User Interface:</strong> Touchscreen, buttons, and controls for operator interaction</li>
                            <li><strong>Exposure Control:</strong> Manages kVp, mA, and exposure timing</li>
                            <li><strong>Safety Interlocks:</strong> Prevents operation under unsafe conditions</li>
                            <li><strong>System Monitoring:</strong> Monitors temperatures, voltages, and system status</li>
                            <li><strong>Communication Bus:</strong> Connects all subsystems for data exchange</li>
                            <li><strong>Calibration System:</strong> Maintains system accuracy and consistency</li>
                        </ul>
                        
                        <div class="common-issues">
                            <h4>Common Issues</h4>
                            <ul>
                                <li>User interface failures or unresponsiveness</li>
                                <li>Communication errors between subsystems</li>
                                <li>Calibration drift affecting image quality</li>
                                <li>Software bugs or crashes</li>
                                <li>Safety interlock malfunctions</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section id="image-chain" class="diagram-section">
            <div class="container">
                <h2>Image Chain Block Diagram</h2>
                <p>The image chain represents the path from X-ray generation to final image display, showing how the diagnostic image is formed and processed.</p>
                
                <div class="diagram-container">
                    <div class="interactive-diagram" id="image-chain-diagram">
                        <img src="../images/image-chain-diagram.png" alt="Image Chain Block Diagram" onerror="this.src='https://via.placeholder.com/800x400?text=Image+Chain+Block+Diagram'">
                    </div>
                    
                    <div class="diagram-info">
                        <h3>Image Chain Components</h3>
                        <ul>
                            <li><strong>X-ray Source:</strong> Generates X-ray photons</li>
                            <li><strong>Beam Filtration:</strong> Removes low-energy X-rays to reduce patient dose</li>
                            <li><strong>Collimation:</strong> Shapes the X-ray beam to the area of interest</li>
                            <li><strong>Patient:</strong> X-rays interact with patient tissues</li>
                            <li><strong>Anti-scatter Grid:</strong> Reduces scattered radiation reaching the detector</li>
                            <li><strong>Detector:</strong> Converts X-rays to electronic signals</li>
                            <li><strong>Analog-to-Digital Conversion:</strong> Converts signals to digital data</li>
                            <li><strong>Image Processing:</strong> Enhances image quality and applies corrections</li>
                            <li><strong>Image Storage:</strong> Saves images to local or network storage</li>
                            <li><strong>Display:</strong> Presents the final image to the operator</li>
                        </ul>
                        
                        <div class="common-issues">
                            <h4>Common Issues</h4>
                            <ul>
                                <li>Detector calibration errors causing artifacts</li>
                                <li>Grid alignment issues causing grid lines in images</li>
                                <li>Image processing errors affecting diagnostic quality</li>
                                <li>Network or storage failures causing image loss</li>
                                <li>Display calibration issues affecting image interpretation</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        
        <section class="navigation-buttons">
            <div class="container">
                <div class="nav-buttons">
                    <a href="components.html" class="btn"><i class="fas fa-arrow-left"></i> Previous: X-ray Components</a>
                    <a href="circuit-diagrams.html" class="btn">Next: Circuit Diagrams <i class="fas fa-arrow-right"></i></a>
                </div>
            </div>
        </section>
    </main>

    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>X-ray System Training</h3>
                    <p>A comprehensive training platform for biomedical engineers specializing in X-ray systems.</p>
                </div>
                <div class="footer-section">
                    <h3>Quick Links</h3>
                    <ul>
                        <li><a href="../index.html">Home</a></li>
                        <li><a href="#overview">Overview</a></li>
                        <li><a href="#general-system">General System</a></li>
                        <li><a href="#power-supply">Power Supply</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h3>Contact</h3>
                    <p>Email: <EMAIL></p>
                    <p>Phone: +****************</p>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2025 X-ray System Interactive Training Module. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script src="../js/main.js"></script>
    <script src="../js/block-diagrams.js"></script>
</body>
</html>
