<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interactive X-Ray Demonstrator</title>
    <style>
        :root {
            --bg-color: #f0f4f8; /* Light grayish blue */
            --panel-bg-color: #ffffff;
            --text-color: #333333;
            --primary-color: #4a90e2; /* Muted blue */
            --secondary-color: #7aa9dd;
            --border-color: #d1d8e0;
            --shadow-color: rgba(0, 0, 0, 0.1);
            --xray-beam-color: rgba(129, 212, 250, 0.6); /* Light blue */
            --torso-fill-color: rgba(189, 195, 199, 0.3); /* Light gray */
            --organ-absorb-color-base: 50, 50, 50; /* Dark gray for absorption visualization */
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: var(--bg-color);
            color: var(--text-color);
            line-height: 1.6;
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }

        .app-container {
            display: flex;
            flex-direction: column;
            flex-grow: 1;
        }

        header {
            background-color: var(--primary-color);
            color: white;
            padding: 1rem 1.5rem;
            text-align: center;
            box-shadow: 0 2px 4px var(--shadow-color);
        }

        header h1 {
            margin: 0;
            font-size: 1.8rem;
        }

        main {
            display: flex;
            flex-wrap: wrap;
            padding: 1.5rem;
            gap: 1.5rem;
            flex-grow: 1;
        }

        .controls-panel, .xray-simulation-area {
            background-color: var(--panel-bg-color);
            border-radius: 8px;
            padding: 1.5rem;
            box-shadow: 0 1px 3px var(--shadow-color);
        }

        .controls-panel {
            flex: 1 1 350px; /* Grow and shrink, base width 350px */
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }

        .xray-simulation-area {
            flex: 2 1 400px; /* Grow more, base width 400px */
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 1rem;
        }
        
        section h2 {
            margin-top: 0;
            font-size: 1.3rem;
            color: var(--primary-color);
            border-bottom: 2px solid var(--secondary-color);
            padding-bottom: 0.5rem;
        }

        /* Density Controls */
        .density-controls label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: bold;
        }
        #densitySlider {
            width: 100%;
            margin-bottom: 0.5rem;
            cursor: pointer;
        }
        .presets {
            display: flex;
            gap: 0.5rem;
            margin-top: 0.5rem;
            margin-bottom: 1rem;
            flex-wrap: wrap;
        }
        .preset-btn, .machine-btn, #takeXrayButton {
            padding: 0.6rem 1rem;
            background-color: var(--secondary-color);
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.2s ease;
            font-size: 0.9rem;
        }
        .preset-btn:hover, .machine-btn:hover, #takeXrayButton:hover {
            background-color: var(--primary-color);
        }
        .machine-btn.active {
            background-color: var(--primary-color);
            box-shadow: 0 0 0 2px white, 0 0 0 4px var(--primary-color);
        }
        #densityExplanation {
            font-size: 0.9rem;
            background-color: #e9f1f8;
            padding: 0.8rem;
            border-radius: 4px;
            border-left: 4px solid var(--secondary-color);
        }

        /* Machine Selection */
        #machineButtons {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            margin-bottom: 1rem;
        }
        #machineIconContainer {
            font-size: 2.5rem;
            text-align: center;
            margin: 0.5rem 0;
            min-height: 40px; /* Ensure space even if no icon */
            color: var(--primary-color);
        }
        #machineDescription {
            font-size: 0.9rem;
            min-height: 60px; /* To prevent layout jumps */
        }

        /* X-Ray Simulation Display */
        .xray-machine-view {
            width: 100%;
            max-width: 450px; /* Max width for the simulation elements */
            aspect-ratio: 1 / 1.2; /* Maintain a somewhat portrait aspect ratio */
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: space-around; /* Distribute space for source, patient, button */
            position: relative; /* For beam positioning */
            background-color: #e0e6eb; /* Slightly darker background for contrast */
            border-radius: 6px;
            padding: 1rem;
            overflow: hidden; /* To contain beam if it goes wild */
        }

        #xraySourceElement {
            font-size: 2rem;
            padding: 0.25rem 0.5rem;
            background-color: #333;
            color: #fff;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
            z-index: 10; /* Above beam */
        }

        #xrayBeamEffect {
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            width: 30px; /* Beam width */
            background: var(--xray-beam-color);
            opacity: 0;
            height: 0;
            border-radius: 0 0 5px 5px;
            z-index: 1; /* Below source, above patientRepresentation items if needed */
            /* Transition handled by JS for start/end */
        }
        #xrayBeamEffect.active {
            opacity: 1;
            transition: height 0.4s ease-in-out, opacity 0.2s ease-in;
        }
        #xrayBeamEffect.fading {
            opacity: 0;
            transition: opacity 0.3s ease-out;
        }


        #patientRepresentation {
            position: relative; /* Crucial for positioning organ and detector image */
            width: 150px; /* Size of the torso visual */
            height: 200px; /* Size of the torso visual */
            display: flex; /* To center organ related items */
            align-items: center;
            justify-content: center;
        }

        #torsoVisual {
            width: 100%;
            height: 100%;
            background-color: var(--torso-fill-color);
            /* CSS Torso Shape */
            border-radius: 50% 50% 25% 25% / 60% 60% 40% 40%;
            position: relative; /* For simulatedMaterial positioning */
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 2; /* In front of detector image */
            box-shadow: inset 0 0 10px rgba(0,0,0,0.1);
        }
        
        #simulatedMaterial { /* The "organ" whose density is controlled */
            width: 60px;
            height: 60px;
            border-radius: 50%;
            /* Background color (opacity part) set by JS */
            position: relative; /* To be on top of torsoVisual background */
            z-index: 3; /* Above torsoVisual background, below beam if beam overlaps */
            border: 1px solid rgba(0,0,0,0.1);
        }

        #materialImageOnDetector { /* The "X-ray image" of the organ */
            width: 60px; /* Same size as simulatedMaterial */
            height: 60px; /* Same size as simulatedMaterial */
            border-radius: 50%; /* Same shape */
            position: absolute; /* Positioned "behind" simulatedMaterial */
            /* Top/left set by JS to match simulatedMaterial if needed, or centered like this */
            /* Background color (grayscale) set by JS */
            z-index: 1; /* Behind torsoVisual and simulatedMaterial */
            border: 1px solid #ccc;
            box-shadow: 0 0 5px rgba(0,0,0,0.2);
        }

        #takeXrayButton {
            margin-top: 1rem;
            padding: 0.8rem 1.5rem;
            font-size: 1rem;
            width: 60%;
            max-width: 200px;
        }

        footer {
            text-align: center;
            padding: 1rem;
            background-color: #333;
            color: #f0f0f0;
            font-size: 0.9rem;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            main {
                flex-direction: column;
                padding: 1rem;
                gap: 1rem;
            }
            .controls-panel, .xray-simulation-area {
                flex-basis: auto; /* Allow them to take full width */
                padding: 1rem;
            }
            header h1 {
                font-size: 1.5rem;
            }
            .xray-machine-view {
                max-width: 100%; /* Allow simulation to use available width */
                min-height: 350px; /* Ensure enough height */
            }
            #patientRepresentation {
                width: 120px;
                height: 160px;
            }
            #simulatedMaterial, #materialImageOnDetector {
                width: 50px;
                height: 50px;
            }
        }
    </style>
</head>
<body>
    <div class="app-container">
        <header>
            <h1>Interactive X-Ray Demonstrator</h1>
        </header>

        <main>
            <div class="controls-panel">
                <section class="density-controls">
                    <h2>Material Density</h2>
                    <label for="densitySlider">Adjust Material Density:</label>
                    <input type="range" id="densitySlider" min="0" max="100" value="30">
                    <div id="densityValue" style="text-align: center; font-weight: bold; margin-bottom: 0.5rem;">Density: 30%</div>
                    <div class="presets">
                        <button data-density="5" class="preset-btn">Air</button>
                        <button data-density="30" class="preset-btn">Soft Tissue</button>
                        <button data-density="85" class="preset-btn">Bone</button>
                    </div>
                    <p id="densityExplanation">Adjust the slider or use presets to see how material density affects X-ray absorption and the resulting image.</p>
                </section>

                <section class="machine-selection">
                    <h2>X-Ray Machine Types</h2>
                    <div id="machineButtons">
                        <button class="machine-btn" data-machine="radiography">Conventional</button>
                        <button class="machine-btn" data-machine="ct">CT Scan</button>
                        <button class="machine-btn" data-machine="angiography">Angiography</button>
                        <button class="machine-btn" data-machine="mammography">Mammography</button>
                        <button class="machine-btn" data-machine="fluoroscopy">Fluoroscopy</button>
                    </div>
                    <div id="machineIconContainer"></div>
                    <p id="machineDescription">Select a machine type to learn more about it.</p>
                </section>
            </div>

            <div class="xray-simulation-area">
                <div class="xray-machine-view">
                    <div id="xraySourceElement">☢️</div>
                    <div id="xrayBeamEffect"></div>
                    
                    <div id="patientRepresentation">
                        <div id="torsoVisual">
                            <div id="simulatedMaterial"></div>
                        </div>
                        <!-- This is positioned via CSS to be 'behind' the simulatedMaterial area -->
                        <div id="materialImageOnDetector"></div> 
                    </div>
                </div>
                <button id="takeXrayButton">Take X-Ray</button>
            </div>
        </main>

        <footer>
            <p>Explore the principles of medical X-rays. For educational purposes only.</p>
        </footer>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const densitySlider = document.getElementById('densitySlider');
            const densityValueDisplay = document.getElementById('densityValue');
            const densityExplanation = document.getElementById('densityExplanation');
            const presetButtons = document.querySelectorAll('.preset-btn');
            
            const simulatedMaterial = document.getElementById('simulatedMaterial');
            const materialImageOnDetector = document.getElementById('materialImageOnDetector');

            const takeXrayButton = document.getElementById('takeXrayButton');
            const xrayBeamEffect = document.getElementById('xrayBeamEffect');
            const xraySourceElement = document.getElementById('xraySourceElement');
            const patientRepresentation = document.getElementById('patientRepresentation');

            const machineButtons = document.querySelectorAll('.machine-btn');
            const machineIconContainer = document.getElementById('machineIconContainer');
            const machineDescription = document.getElementById('machineDescription');

            const machineInfo = {
                radiography: { name: "Conventional Radiography", icon: "🦴", description: "Uses X-rays to produce 2D images, primarily showing bones and some dense tissues. It's quick, common, and often the first imaging test." },
                ct: { name: "Computed Tomography (CT)", icon: "🔄", description: "Combines multiple X-ray images taken from different angles to create cross-sectional (slice) images. Provides more detailed images than conventional X-rays." },
                angiography: { name: "Angiography", icon: "🩸", description: "Visualizes blood vessels. Often involves injecting a contrast agent to make vessels visible on X-ray images. Used to detect blockages or abnormalities." },
                mammography: { name: "Mammography", icon: "🌸", description: "A specialized X-ray technique for examining breast tissue. Uses low-dose X-rays to detect early signs of breast cancer." },
                fluoroscopy: { name: "Fluoroscopy", icon: "🎥", description: "Produces real-time, moving X-ray images, like an X-ray 'movie'. Used to observe bodily functions, guide procedures, or view contrast agents moving through the body." }
            };

            function updateDensityVisuals(density) {
                const absorptionFactor = density / 100; // 0 to 1

                // Update simulated material (organ) - represents absorption
                // More density = more absorption = darker material (more opaque black)
                const organBaseR = parseInt(getComputedStyle(document.documentElement).getPropertyValue('--organ-absorb-color-base').split(',')[0]);
                const organBaseG = parseInt(getComputedStyle(document.documentElement).getPropertyValue('--organ-absorb-color-base').split(',')[1]);
                const organBaseB = parseInt(getComputedStyle(document.documentElement).getPropertyValue('--organ-absorb-color-base').split(',')[2]);
                simulatedMaterial.style.backgroundColor = `rgba(${organBaseR}, ${organBaseG}, ${organBaseB}, ${absorptionFactor})`;

                // Update detector image - represents transmitted X-rays
                // More absorption = less transmission = darker on detector (closer to black)
                // White (255) = no absorption, Black (0) = full absorption
                const grayscaleValue = Math.round(255 * (1 - absorptionFactor));
                materialImageOnDetector.style.backgroundColor = `rgb(${grayscaleValue}, ${grayscaleValue}, ${grayscaleValue})`;

                densityValueDisplay.textContent = `Density: ${density}%`;
                updateDensityExplanationText(density, absorptionFactor);
            }

            function updateDensityExplanationText(density, absorptionFactor) {
                let explanation = `Material Density: ${density}%. `;
                if (absorptionFactor < 0.2) {
                    explanation += "This material has very low density (like air). It absorbs very few X-rays. The corresponding area on the X-ray image appears very light (closer to white).";
                } else if (absorptionFactor < 0.6) {
                    explanation += "This material has medium density (like soft tissue). It absorbs a moderate amount of X-rays. The image shows some contrast, appearing as shades of gray.";
                } else {
                    explanation += "This material has high density (like bone). It absorbs many X-rays. The corresponding area on the X-ray image appears dark (closer to black).";
                }
                densityExplanation.innerHTML = explanation; // Use innerHTML if you might add tags, textContent otherwise
            }
            
            densitySlider.addEventListener('input', () => {
                updateDensityVisuals(parseInt(densitySlider.value));
            });

            presetButtons.forEach(button => {
                button.addEventListener('click', () => {
                    const density = parseInt(button.dataset.density);
                    densitySlider.value = density;
                    updateDensityVisuals(density);
                });
            });

            takeXrayButton.addEventListener('click', () => {
                if (xrayBeamEffect.classList.contains('active')) return; // Prevent re-triggering if already active

                const sourceRect = xraySourceElement.getBoundingClientRect();
                const patientRect = patientRepresentation.getBoundingClientRect();
                const machineViewRect = xrayBeamEffect.parentElement.getBoundingClientRect();

                // Calculate top relative to xray-machine-view
                const beamTopStart = sourceRect.bottom - machineViewRect.top;
                // Calculate height to reach top of patient representation
                const beamHeight = patientRect.top - sourceRect.bottom + (patientRect.height / 2); // Target middle of patient

                xrayBeamEffect.style.top = `${beamTopStart}px`;
                xrayBeamEffect.style.height = '0px'; // Reset height before animation

                // Force reflow to apply initial styles before transition
                void xrayBeamEffect.offsetWidth; 

                xrayBeamEffect.classList.add('active');
                xrayBeamEffect.style.height = `${Math.max(0, beamHeight)}px`; // Animate to calculated height

                setTimeout(() => {
                    xrayBeamEffect.classList.remove('active');
                    xrayBeamEffect.classList.add('fading'); // Start fading out
                    setTimeout(() => {
                        xrayBeamEffect.style.height = '0px'; // Reset height after fade
                        xrayBeamEffect.classList.remove('fading');
                    }, 300); // Match fading transition duration
                }, 400); // Match active transition duration
            });

            machineButtons.forEach(button => {
                button.addEventListener('click', () => {
                    machineButtons.forEach(btn => btn.classList.remove('active'));
                    button.classList.add('active');
                    const machineKey = button.dataset.machine;
                    const info = machineInfo[machineKey];
                    if (info) {
                        machineIconContainer.textContent = info.icon;
                        machineDescription.textContent = info.description;
                    }
                });
            });

            // Initial setup
            updateDensityVisuals(parseInt(densitySlider.value));
            // Select first machine by default
            if (machineButtons.length > 0) {
                machineButtons[0].click();
            }
        });
    </script>
</body>
</html>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>X-Ray System Interactive Learning</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f0f0f0;
            color: #333;
            display: flex;
            flex-direction: column;
            align-items: center;
            min-height: 100vh;
        }

        #app-container {
            width: 100%;
            max-width: 900px;
            margin: 20px auto;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 0 15px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        #header {
            background-color: #007bff;
            color: white;
            padding: 15px;
            text-align: center;
            font-size: 1.5em;
        }

        #controls-area {
            padding: 15px;
            text-align: center;
            border-bottom: 1px solid #eee;
        }

        #controls-area button {
            padding: 10px 20px;
            margin: 0 10px;
            font-size: 1em;
            cursor: pointer;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            transition: background-color 0.3s;
        }

        #controls-area button:hover {
            background-color: #0056b3;
        }
        #controls-area button.active {
            background-color: #0056b3;
            font-weight: bold;
        }

        #illustration-wrapper {
            width: 100%;
            padding-bottom: 60%; /* Aspect ratio (e.g., 5:3). Adjust as needed. */
            position: relative;
            background-color: #e0e0e0; /* Room floor/background */
            overflow: hidden;
        }

        #illustration-container {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }

        /* Visual components */
        .xray-room-element {
            position: absolute;
            background-color: #ccc;
            border: 1px solid #999;
            box-sizing: border-box;
        }

        #wall-background { /* A side wall for the wall bucky */
            top: 0;
            left: 75%;
            width: 25%;
            height: 100%;
            background-color: #d0d0d0;
            border: none;
        }

        #xray-table-visual {
            top: 55%;
            left: 10%;
            width: 70%;
            height: 30%;
            background-color: #b0c4de; /* Light steel blue */
            border: 2px solid #708090; /* Slate gray */
            z-index: 2;
            display: flex;
            justify-content: center;
            align-items: flex-start;
            padding-top: 1%;
        }
        #table-bucky-slot-visual { /* Visual cue for table bucky */
            width: 50%;
            height: 10%;
            background-color: #a0b4ce;
            border: 1px dashed #607080;
        }


        #xray-tube-assembly-visual {
            top: 15%;
            left: 35%;
            width: 30%;
            height: 25%;
            z-index: 3;
            /* No specific background, hotspots will define it */
        }
        #xray-tube-arm-visual { /* Support arm */
            top: 0%;
            left: 48%; /* Centered on arm */
            width: 4%;
            height: 100%;
            background-color: #a9a9a9; /* Dark gray */
            border: 1px solid #696969;
        }
        #xray-tube-head-visual {
            top: 70%; /* Bottom part of assembly */
            left: 35%; /* Relative to assembly container */
            width: 30%;
            height: 30%;
            background-color: #d3d3d3; /* Light gray */
            border: 2px solid #808080; /* Gray */
            border-radius: 5px;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 0.7em;
            color: #555;
            text-align: center;
        }
         #tube-controls-visual-indicator { /* Visual cue for tube controls */
            top: 40%;
            left: 20%;
            width: 60%;
            height: 20%;
            background-color: #c0c0c0;
            border: 1px dashed #777;
            font-size: 0.6em;
            text-align: center;
            padding: 2px;
            box-sizing: border-box;
            overflow: hidden;
        }


        #wall-bucky-visual {
            top: 20%;
            left: 80%;
            width: 15%;
            height: 60%;
            background-color: #d3d3d3; /* Light gray */
            border: 2px solid #808080; /* Gray */
            z-index: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 5%;
            box-sizing: border-box;
        }
        #wall-bucky-handrail-visual {
            width: 80%;
            height: 8%;
            background-color: #a9a9a9;
            margin-top: 10%;
            border-radius: 3px;
        }
        #wall-bucky-controls-visual {
             width: 60%;
            height: 10%;
            background-color: #c0c0c0;
            border: 1px dashed #777;
            margin-top: auto; /* Pushes to bottom */
            margin-bottom: 5%;
            font-size: 0.5em;
            text-align: center;
            padding: 1px;
        }


        .hotspot {
            position: absolute;
            /* background-color: rgba(255, 0, 0, 0.1); */ /* For debugging */
            border: 2px dashed transparent;
            box-sizing: border-box;
            cursor: pointer;
            z-index: 10;
        }

        .hotspot.highlighted {
            border: 3px solid #ffcc00; /* Bright yellow */
            box-shadow: 0 0 15px #ffcc00;
            background-color: rgba(255, 204, 0, 0.2);
        }

        #info-area {
            padding: 20px;
            min-height: 100px;
            border-top: 1px solid #eee;
        }

        #info-area h3 {
            margin-top: 0;
            color: #007bff;
        }
        #info-area p {
            line-height: 1.6;
        }
        #info-area button {
            padding: 8px 15px;
            margin-top: 10px;
            font-size: 0.9em;
            cursor: pointer;
            background-color: #28a745;
            color: white;
            border: none;
            border-radius: 4px;
        }
        #info-area button:hover {
            background-color: #218838;
        }

        .feedback {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
        }
        .feedback.correct {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .feedback.incorrect {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        /* Responsive adjustments */
        @media (max-width: 600px) {
            #header {
                font-size: 1.2em;
            }
            #controls-area button {
                padding: 8px 12px;
                font-size: 0.9em;
                margin: 5px;
            }
            #info-area {
                padding: 15px;
            }
            #info-area h3 {
                font-size: 1.1em;
            }
            #info-area p {
                font-size: 0.9em;
            }
             #xray-tube-head-visual {
                font-size: 0.5em; /* Smaller text on mobile */
            }
            #tube-controls-visual-indicator {
                font-size: 0.4em;
            }
            #wall-bucky-controls-visual {
                font-size: 0.4em;
            }
        }

    </style>
</head>
<body>

    <div id="app-container">
        <div id="header">Diagnostic X-Ray System Explorer</div>

        <div id="controls-area">
            <button id="walkthrough-btn">Walkthrough Mode</button>
            <button id="quiz-btn">Quiz Mode</button>
        </div>

        <div id="illustration-wrapper">
            <div id="illustration-container">
                <!-- Visual background elements -->
                <div id="wall-background" class="xray-room-element"></div>

                <!-- X-Ray Table -->
                <div id="xray-table-visual" class="xray-room-element">
                    <div id="table-bucky-slot-visual">Bucky Slot</div>
                </div>

                <!-- X-Ray Tube Assembly -->
                <div id="xray-tube-assembly-visual" class="xray-room-element">
                    <div id="xray-tube-arm-visual" class="xray-room-element"></div>
                    <div id="xray-tube-head-visual" class="xray-room-element">Tube Head</div>
                    <div id="tube-controls-visual-indicator" class="xray-room-element">Tube Controls Area</div>
                </div>

                <!-- Wall Bucky -->
                <div id="wall-bucky-visual" class="xray-room-element">
                    Wall Bucky
                    <div id="wall-bucky-handrail-visual"></div>
                    <div id="wall-bucky-controls-visual">Height Adj.</div>
                </div>

                <!-- Hotspots will be dynamically added here by JavaScript -->
            </div>
        </div>

        <div id="info-area">
            <p>Welcome! Select a mode above to begin learning about the X-Ray system.</p>
        </div>
    </div>

    <script>
        const componentsData = [
            {
                id: 'xray-tube',
                name: 'The X-ray Tube',
                description: 'The X-ray tube is the heart of the x-ray machine. It\'s a vacuum tube that produces x-rays when electrons, accelerated by high voltage, strike a metal target (anode).',
                hotspotTargetId: 'hotspot-xray-tube-head',
                walkthroughOrder: 1
            },
            {
                id: 'xray-tube-controls',
                name: 'The X-ray Tube Controls',
                description: 'These are the mechanisms and interfaces (often handles, buttons, and locks on the tube support assembly) that allow the radiographer to precisely position the x-ray tube. This includes adjusting its height, moving it longitudinally and transversely, and angulating it.',
                hotspotTargetId: 'hotspot-tube-assembly-controls',
                walkthroughOrder: 2
            },
            {
                id: 'angulation-controls',
                name: 'The Angulation Controls',
                description: 'Part of the x-ray tube controls, these specifically allow the operator to tilt or rotate the x-ray tube head. This changes the angle of the x-ray beam relative to the patient and image receptor, crucial for various radiographic projections.',
                hotspotTargetId: 'hotspot-tube-assembly-controls',
                walkthroughOrder: 3
            },
            {
                id: 'vertical-controls',
                name: 'The Vertical (Height) Controls',
                description: 'These controls, part of the tube manipulation system, allow the x-ray tube to be moved up or down. This adjusts the Source-to-Image Distance (SID) and centers the beam to the area of interest.',
                hotspotTargetId: 'hotspot-tube-assembly-controls',
                walkthroughOrder: 4
            },
            {
                id: 'transverse-controls',
                name: 'The Transverse (Width) Controls',
                description: 'These controls allow the x-ray tube to be moved side-to-side (laterally) across the x-ray table or patient. This is essential for precise centering of the x-ray beam.',
                hotspotTargetId: 'hotspot-tube-assembly-controls',
                walkthroughOrder: 5
            },
            {
                id: 'longitudinal-controls',
                name: 'The Longitudinal Controls',
                description: 'These controls enable movement of the x-ray tube along the length of the x-ray table (head-to-foot direction). This allows for positioning the beam over different parts of the patient.',
                hotspotTargetId: 'hotspot-tube-assembly-controls',
                walkthroughOrder: 6
            },
            {
                id: 'detent-locks',
                name: 'The Detent and Locks',
                description: 'Detents are pre-set positions where the x-ray tube "clicks" into place, often aligned with the center of the table or wall bucky. Locks are mechanisms (manual or electromagnetic) that secure the tube in a desired position, preventing accidental movement.',
                hotspotTargetId: 'hotspot-tube-assembly-controls',
                walkthroughOrder: 7
            },
            {
                id: 'wall-bucky',
                name: 'The Wall Bucky',
                description: 'A vertical cassette holder or digital detector unit, typically mounted on a wall. It is used for upright examinations, such as chest x-rays or standing abdomen views. It contains a grid and holds the image receptor.',
                hotspotTargetId: 'hotspot-wall-bucky-main',
                walkthroughOrder: 8
            },
            {
                id: 'wall-bucky-handrail',
                name: 'The Handrail on the Wall Bucky',
                description: 'A handrail or grip attached to the wall bucky, providing support and stability for patients during upright examinations.',
                hotspotTargetId: 'hotspot-wall-bucky-handrail',
                walkthroughOrder: 9
            },
            {
                id: 'wall-bucky-height-controls',
                name: 'The Controls to Adjust Wall Bucky Height',
                description: 'Mechanisms (often a lever or button) that allow the radiographer to move the wall bucky up or down to align it with the specific anatomy of the patient being imaged.',
                hotspotTargetId: 'hotspot-wall-bucky-height-adj',
                walkthroughOrder: 10
            },
            {
                id: 'xray-table',
                name: 'The X-ray Table',
                description: 'A specialized table designed to support the patient during radiographic procedures. Many tables can be raised, lowered, tilted, or have a "floating" tabletop for easy patient positioning.',
                hotspotTargetId: 'hotspot-xray-table-top',
                walkthroughOrder: 11
            },
            {
                id: 'table-bucky',
                name: 'The Table Bucky',
                description: 'A tray or drawer located beneath the x-ray tabletop. It holds the image receptor (e.g., cassette or digital detector) and a grid. The Bucky mechanism often allows the grid to move during exposure to blur grid lines.',
                hotspotTargetId: 'hotspot-table-bucky-tray',
                walkthroughOrder: 12
            },
            {
                id: 'table-controls',
                name: 'The Table Controls',
                description: 'Controls, often foot pedals or buttons on the side of the table, used to adjust the table\'s height, tilt (if applicable), and to lock/unlock the tabletop movement (floating top).',
                hotspotTargetId: 'hotspot-table-movement-controls',
                walkthroughOrder: 13
            },
            {
                id: 'grid',
                name: 'The Grid',
                description: 'A device made of very thin lead strips, placed between the patient and the image receptor (housed within both table and wall buckies). Its purpose is to absorb scatter radiation, which improves the contrast and quality of the x-ray image.',
                hotspotTargetIds: ['hotspot-table-bucky-tray', 'hotspot-wall-bucky-main'], // Can be in either
                walkthroughOrder: 14
            },
            {
                id: 'image-receptor',
                name: 'The Image Receptor',
                description: 'The device that captures the x-ray photons that pass through the patient, thereby forming the radiographic image. This can be a film-screen cassette, a CR imaging plate, or a flat-panel digital detector (DR). It is placed in the table bucky or wall bucky.',
                hotspotTargetIds: ['hotspot-table-bucky-tray', 'hotspot-wall-bucky-main'], // Can be in either
                walkthroughOrder: 15
            }
        ];

        // Define the visual hotspots: their ID, position, and a user-friendly name for feedback
        const hotspotsDefinition = [
            { id: 'hotspot-xray-tube-head', nameForFeedback: 'X-ray Tube Head', top: '32%', left: '47%', width: '10%', height: '10%' }, // On the tube head visual
            { id: 'hotspot-tube-assembly-controls', nameForFeedback: 'X-ray Tube Controls Area', top: '20%', left: '30%', width: '40%', height: '15%' }, // Covers general controls area
            { id: 'hotspot-wall-bucky-main', nameForFeedback: 'Wall Bucky Unit', top: '20%', left: '80%', width: '15%', height: '60%' },
            { id: 'hotspot-wall-bucky-handrail', nameForFeedback: 'Wall Bucky Handrail', top: '30%', left: '81.5%', width: '12%', height: '6%' }, // On the handrail visual
            { id: 'hotspot-wall-bucky-height-adj', nameForFeedback: 'Wall Bucky Height Controls', top: '68%', left: '82.5%', width: '10%', height: '8%' }, // On the controls visual
            { id: 'hotspot-xray-table-top', nameForFeedback: 'X-ray Table', top: '55%', left: '10%', width: '70%', height: '30%' },
            { id: 'hotspot-table-bucky-tray', nameForFeedback: 'Table Bucky (tray area)', top: '56%', left: '30%', width: '30%', height: '8%' }, // Over the visual slot
            { id: 'hotspot-table-movement-controls', nameForFeedback: 'Table Controls (e.g., foot pedals - represented by area near base)', top: '85%', left: '35%', width: '20%', height: '10%' } // Area below table for conceptual foot pedals
        ];

        const illustrationContainer = document.getElementById('illustration-container');
        const infoArea = document.getElementById('info-area');
        const walkthroughBtn = document.getElementById('walkthrough-btn');
        const quizBtn = document.getElementById('quiz-btn');

        let currentMode = null; // 'walkthrough' or 'quiz'
        let currentWalkthroughStep = 0;
        let currentQuizQuestion = null;
        let hotspotElements = {}; // Store created hotspot DOM elements

        function init() {
            createHotspots();
            walkthroughBtn.addEventListener('click', () => setMode('walkthrough'));
            quizBtn.addEventListener('click', () => setMode('quiz'));
            // Sort componentsData by walkthroughOrder for sequential walkthrough
            componentsData.sort((a, b) => a.walkthroughOrder - b.walkthroughOrder);
            setMode('walkthrough'); // Start in walkthrough mode by default
        }

        function createHotspots() {
            hotspotsDefinition.forEach(hsDef => {
                const hotspotEl = document.createElement('div');
                hotspotEl.classList.add('hotspot');
                hotspotEl.id = hsDef.id;
                hotspotEl.style.top = hsDef.top;
                hotspotEl.style.left = hsDef.left;
                hotspotEl.style.width = hsDef.width;
                hotspotEl.style.height = hsDef.height;
                hotspotEl.dataset.nameForFeedback = hsDef.nameForFeedback; // Store name for feedback
                
                hotspotEl.addEventListener('click', () => handleHotspotClick(hsDef.id));
                
                illustrationContainer.appendChild(hotspotEl);
                hotspotElements[hsDef.id] = hotspotEl;
            });
        }
        
        function setMode(mode) {
            currentMode = mode;
            clearHighlights();
            if (mode === 'walkthrough') {
                walkthroughBtn.classList.add('active');
                quizBtn.classList.remove('active');
                startWalkthrough();
            } else if (mode === 'quiz') {
                quizBtn.classList.add('active');
                walkthroughBtn.classList.remove('active');
                startQuiz();
            }
        }

        function clearHighlights() {
            Object.values(hotspotElements).forEach(el => el.classList.remove('highlighted'));
        }

        function highlightHotspot(hotspotId) {
            if (hotspotElements[hotspotId]) {
                hotspotElements[hotspotId].classList.add('highlighted');
            }
        }
        
        function highlightHotspots(hotspotIds) {
            if (Array.isArray(hotspotIds)) {
                hotspotIds.forEach(id => highlightHotspot(id));
            } else {
                highlightHotspot(hotspotIds);
            }
        }


        // --- Walkthrough Mode ---
        function startWalkthrough() {
            currentWalkthroughStep = 0;
            displayWalkthroughStep();
        }

        function displayWalkthroughStep() {
            clearHighlights();
            if (currentWalkthroughStep < componentsData.length) {
                const component = componentsData[currentWalkthroughStep];
                infoArea.innerHTML = `
                    <h3>${currentWalkthroughStep + 1}. ${component.name}</h3>
                    <p>${component.description}</p>
                    <button id="next-step-btn">Next</button>
                `;
                document.getElementById('next-step-btn').addEventListener('click', nextWalkthroughStep);
                
                if (component.hotspotTargetId) {
                    highlightHotspot(component.hotspotTargetId);
                } else if (component.hotspotTargetIds) {
                    component.hotspotTargetIds.forEach(id => highlightHotspot(id));
                }
            } else {
                infoArea.innerHTML = `
                    <h3>Walkthrough Complete!</h3>
                    <p>You have learned about all the components. Try the Quiz mode to test your knowledge or restart the walkthrough.</p>
                    <button id="restart-walkthrough-btn">Restart Walkthrough</button>
                `;
                document.getElementById('restart-walkthrough-btn').addEventListener('click', startWalkthrough);
            }
        }

        function nextWalkthroughStep() {
            currentWalkthroughStep++;
            displayWalkthroughStep();
        }

        // --- Quiz Mode ---
        function startQuiz() {
            generateQuizQuestion();
        }

        function generateQuizQuestion() {
            clearHighlights();
            const randomIndex = Math.floor(Math.random() * componentsData.length);
            currentQuizQuestion = componentsData[randomIndex];
            
            infoArea.innerHTML = `
                <h3>Quiz Question</h3>
                <p>Click on: <strong>${currentQuizQuestion.name}</strong></p>
                <div id="feedback-area"></div>
            `;
        }

        function handleHotspotClick(clickedHotspotId) {
            if (currentMode !== 'quiz' || !currentQuizQuestion) return;

            const feedbackArea = document.getElementById('feedback-area');
            let isCorrect = false;

            if (currentQuizQuestion.hotspotTargetId) {
                isCorrect = (clickedHotspotId === currentQuizQuestion.hotspotTargetId);
            } else if (currentQuizQuestion.hotspotTargetIds) {
                isCorrect = currentQuizQuestion.hotspotTargetIds.includes(clickedHotspotId);
            }

            clearHighlights(); // Clear previous highlights before showing new one

            const clickedHotspotName = hotspotElements[clickedHotspotId] ? hotspotElements[clickedHotspotId].dataset.nameForFeedback : 'the selected area';

            if (isCorrect) {
                feedbackArea.innerHTML = `<div class="feedback correct"><strong>Correct!</strong> You identified the ${currentQuizQuestion.name}.</div>`;
                // Highlight the correct one(s)
                if (currentQuizQuestion.hotspotTargetId) highlightHotspot(currentQuizQuestion.hotspotTargetId);
                if (currentQuizQuestion.hotspotTargetIds) currentQuizQuestion.hotspotTargetIds.forEach(id => highlightHotspot(id));
            } else {
                let correctHotspotName = "the correct area";
                if (currentQuizQuestion.hotspotTargetId) {
                    correctHotspotName = hotspotElements[currentQuizQuestion.hotspotTargetId] ? hotspotElements[currentQuizQuestion.hotspotTargetId].dataset.nameForFeedback : "the correct component";
                    highlightHotspot(currentQuizQuestion.hotspotTargetId);
                } else if (currentQuizQuestion.hotspotTargetIds && currentQuizQuestion.hotspotTargetIds.length > 0) {
                    // For multiple possible correct spots, name the first one or a general term
                    correctHotspotName = hotspotElements[currentQuizQuestion.hotspotTargetIds[0]] ? hotspotElements[currentQuizQuestion.hotspotTargetIds[0]].dataset.nameForFeedback : "the correct component";
                    if (currentQuizQuestion.hotspotTargetIds.length > 1) correctHotspotName += " (or other Bucky)";
                    currentQuizQuestion.hotspotTargetIds.forEach(id => highlightHotspot(id));
                }
                
                feedbackArea.innerHTML = `
                    <div class="feedback incorrect">
                        <strong>Incorrect.</strong> You clicked on ${clickedHotspotName}.
                        The ${currentQuizQuestion.name} is ${ (currentQuizQuestion.hotspotTargetId || currentQuizQuestion.hotspotTargetIds) ? `located at/is the ${correctHotspotName}.` : 'described above.'}
                    </div>`;
            }

            infoArea.innerHTML += `<button id="next-question-btn">Next Question</button>`;
            document.getElementById('next-question-btn').addEventListener('click', generateQuizQuestion);
        }

        // Initialize the app
        document.addEventListener('DOMContentLoaded', init);

    </script>
</body>
</html>
