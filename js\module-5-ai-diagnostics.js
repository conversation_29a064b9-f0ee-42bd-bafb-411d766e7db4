/**
 * VirtualX Pro - Module 5: AI-Powered Diagnostics
 * Interactive machine learning and diagnostic case analysis
 */

class AIModule {
    constructor() {
        this.trainingProgress = 73;
        this.isTraining = false;
        this.currentCategory = 'generator';
        this.layers = {
            input: {
                title: 'Input Layer - Data Processing',
                description: 'Receives and preprocesses sensor data from X-ray systems',
                neurons: 128,
                activation: 'Linear',
                details: [
                    'Voltage and current measurements',
                    'Temperature sensor readings',
                    'Vibration and acoustic data',
                    'Image quality metrics',
                    'System performance parameters'
                ]
            },
            hidden1: {
                title: 'Hidden Layer 1 - Feature Extraction',
                description: 'Extracts meaningful features from raw sensor data',
                neurons: 256,
                activation: 'ReLU',
                details: [
                    'Statistical feature extraction',
                    'Frequency domain analysis',
                    'Pattern recognition algorithms',
                    'Anomaly detection filters',
                    'Correlation analysis'
                ]
            },
            hidden2: {
                title: 'Hidden Layer 2 - Pattern Recognition',
                description: 'Identifies complex patterns and relationships',
                neurons: 128,
                activation: 'ReLU',
                details: [
                    'Multi-dimensional pattern matching',
                    'Temporal sequence analysis',
                    'Cross-system correlation',
                    'Predictive modeling',
                    'Classification algorithms'
                ]
            },
            output: {
                title: 'Output Layer - Classification',
                description: 'Provides diagnostic classification and confidence scores',
                neurons: 64,
                activation: 'Softmax',
                details: [
                    'Fault classification categories',
                    'Confidence probability scores',
                    'Severity level assessment',
                    'Recommended actions',
                    'Maintenance predictions'
                ]
            },
            training: {
                title: 'Training Algorithm - Backpropagation',
                description: 'Optimizes network weights using supervised learning',
                algorithm: 'Adam Optimizer',
                learningRate: 0.001,
                details: [
                    'Gradient descent optimization',
                    'Loss function minimization',
                    'Weight and bias updates',
                    'Regularization techniques',
                    'Cross-validation methods'
                ]
            },
            validation: {
                title: 'Validation Testing - Performance Metrics',
                description: 'Evaluates model performance and prevents overfitting',
                metrics: 'Accuracy, Precision, Recall',
                testSize: '20%',
                details: [
                    'K-fold cross-validation',
                    'ROC curve analysis',
                    'Confusion matrix evaluation',
                    'F1-score calculation',
                    'Model generalization testing'
                ]
            }
        };
        this.caseDatabase = {
            generator: {
                title: 'Generator System Issues',
                totalCases: 2847,
                resolvedCases: 2712,
                accuracy: 95.3,
                commonIssues: [
                    'IGBT switching failures',
                    'Voltage regulation problems',
                    'Power factor correction issues',
                    'Thermal management failures',
                    'Control circuit malfunctions'
                ]
            },
            detector: {
                title: 'Detector Problems',
                totalCases: 1923,
                resolvedCases: 1834,
                accuracy: 95.4,
                commonIssues: [
                    'Flat panel degradation',
                    'TFT array failures',
                    'Readout electronics issues',
                    'Scintillator damage',
                    'Calibration drift'
                ]
            },
            tube: {
                title: 'X-ray Tube Faults',
                totalCases: 3156,
                resolvedCases: 3001,
                accuracy: 95.1,
                commonIssues: [
                    'Anode wear and pitting',
                    'Filament degradation',
                    'Bearing failures',
                    'Vacuum loss',
                    'Housing overheating'
                ]
            },
            control: {
                title: 'Control System',
                totalCases: 1542,
                resolvedCases: 1467,
                accuracy: 95.1,
                commonIssues: [
                    'Software glitches',
                    'Communication errors',
                    'Sensor calibration issues',
                    'User interface problems',
                    'Safety interlock failures'
                ]
            },
            mechanical: {
                title: 'Mechanical Issues',
                totalCases: 779,
                resolvedCases: 742,
                accuracy: 95.3,
                commonIssues: [
                    'Motor drive failures',
                    'Positioning accuracy loss',
                    'Mechanical wear',
                    'Lubrication problems',
                    'Structural vibrations'
                ]
            }
        };
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.startTrainingSimulation();
        this.updateDatabaseStats();
        this.loadCaseCategory('generator');
    }

    setupEventListeners() {
        // Neural node interactions
        document.querySelectorAll('.neural-node').forEach(node => {
            node.addEventListener('mouseenter', this.highlightNeuralPath.bind(this));
            node.addEventListener('mouseleave', this.resetNeuralHighlight.bind(this));
        });

        // Case button interactions
        document.querySelectorAll('.case-btn').forEach(btn => {
            btn.addEventListener('click', this.updateActiveCaseBtn.bind(this));
        });
    }

    highlightNeuralPath(event) {
        const node = event.target.closest('.neural-node');
        const lines = document.querySelectorAll('.neural-line');
        
        // Highlight all neural connections
        lines.forEach(line => {
            line.style.background = 'var(--napkin-accent)';
            line.style.height = '3px';
            line.style.opacity = '1';
            line.style.boxShadow = '0 0 15px var(--napkin-accent)';
        });
        
        // Add glow effect to node
        node.style.boxShadow = '0 0 30px var(--napkin-accent)';
    }

    resetNeuralHighlight() {
        const lines = document.querySelectorAll('.neural-line');
        const nodes = document.querySelectorAll('.neural-node');
        
        lines.forEach(line => {
            line.style.background = 'var(--napkin-primary)';
            line.style.height = '2px';
            line.style.opacity = '0.4';
            line.style.boxShadow = 'none';
        });
        
        nodes.forEach(node => {
            node.style.boxShadow = 'var(--napkin-shadow)';
        });
    }

    updateActiveCaseBtn(event) {
        const clickedBtn = event.target.closest('.case-btn');
        if (!clickedBtn) return;
        
        // Update active state
        document.querySelectorAll('.case-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        clickedBtn.classList.add('active');
    }

    startTrainingSimulation() {
        // Simulate training progress
        setInterval(() => {
            if (this.isTraining && this.trainingProgress < 100) {
                this.trainingProgress += Math.random() * 0.5;
                this.updateTrainingDisplay();
            }
        }, 1000);
    }

    updateTrainingDisplay() {
        const progressBar = document.getElementById('trainingProgress');
        const progressPercent = document.getElementById('progressPercent');
        
        if (progressBar) {
            progressBar.style.width = `${Math.min(this.trainingProgress, 100)}%`;
        }
        if (progressPercent) {
            progressPercent.textContent = `${Math.min(this.trainingProgress, 100).toFixed(1)}%`;
        }
        
        // Update training metrics with realistic variations
        this.updateTrainingMetrics();
    }

    updateTrainingMetrics() {
        const metrics = {
            trainingAccuracy: 94.8 + Math.random() * 2,
            validationAccuracy: 92.3 + Math.random() * 1.5,
            trainingLoss: 0.142 + (Math.random() - 0.5) * 0.02,
            validationLoss: 0.198 + (Math.random() - 0.5) * 0.03
        };
        
        Object.entries(metrics).forEach(([id, value]) => {
            const element = document.getElementById(id);
            if (element) {
                if (id.includes('Accuracy')) {
                    element.textContent = `${value.toFixed(1)}%`;
                } else {
                    element.textContent = value.toFixed(3);
                }
            }
        });
    }

    updateDatabaseStats() {
        // Update overall database statistics
        const totalCases = Object.values(this.caseDatabase).reduce((sum, cat) => sum + cat.totalCases, 0);
        const resolvedCases = Object.values(this.caseDatabase).reduce((sum, cat) => sum + cat.resolvedCases, 0);
        const avgAccuracy = Object.values(this.caseDatabase).reduce((sum, cat) => sum + cat.accuracy, 0) / Object.keys(this.caseDatabase).length;
        
        if (document.getElementById('totalCases')) {
            document.getElementById('totalCases').textContent = totalCases.toLocaleString();
        }
        if (document.getElementById('resolvedCases')) {
            document.getElementById('resolvedCases').textContent = resolvedCases.toLocaleString();
        }
        if (document.getElementById('accuracyRate')) {
            document.getElementById('accuracyRate').textContent = `${avgAccuracy.toFixed(1)}%`;
        }
    }

    loadCaseCategory(category) {
        this.currentCategory = category;
        const caseData = this.caseDatabase[category];
        if (!caseData) return;
        
        const caseDisplay = document.getElementById('caseDisplay');
        if (!caseDisplay) return;
        
        caseDisplay.innerHTML = `
            <div style="padding: 1.5rem;">
                <h4 style="color: var(--napkin-accent); margin-bottom: 1rem;">${caseData.title}</h4>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 1rem; margin-bottom: 1.5rem;">
                    <div style="text-align: center;">
                        <div style="font-size: 1.5rem; font-weight: bold; color: var(--napkin-accent);">${caseData.totalCases}</div>
                        <div style="font-size: 0.9rem; opacity: 0.8;">Total Cases</div>
                    </div>
                    <div style="text-align: center;">
                        <div style="font-size: 1.5rem; font-weight: bold; color: var(--napkin-success);">${caseData.resolvedCases}</div>
                        <div style="font-size: 0.9rem; opacity: 0.8;">Resolved</div>
                    </div>
                    <div style="text-align: center;">
                        <div style="font-size: 1.5rem; font-weight: bold; color: var(--napkin-warning);">${caseData.accuracy}%</div>
                        <div style="font-size: 0.9rem; opacity: 0.8;">Accuracy</div>
                    </div>
                </div>
                <h5 style="color: var(--napkin-accent); margin-bottom: 1rem;">Common Issues:</h5>
                <div style="display: grid; gap: 0.5rem;">
                    ${caseData.commonIssues.map(issue => `
                        <div style="display: flex; align-items: center; gap: 0.75rem; padding: 0.5rem; background: rgba(255,255,255,0.1); border-radius: 6px;">
                            <div style="width: 6px; height: 6px; background: var(--napkin-accent); border-radius: 50%;"></div>
                            <span style="font-size: 0.9rem;">${issue}</span>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
    }
}

// Global functions for HTML onclick handlers
function showAIOverview() {
    showModal({
        title: 'AI Diagnostic Engine Overview',
        content: `
            <div style="padding: 2rem;">
                <div style="text-align: center; margin-bottom: 2rem;">
                    <div style="font-size: 4rem; color: var(--napkin-primary); margin-bottom: 1rem;">
                        <i class="fas fa-brain"></i>
                    </div>
                    <h3 style="margin-bottom: 1rem;">Intelligent Fault Detection System</h3>
                    <p style="color: #6b7280; margin-bottom: 2rem;">
                        Advanced neural network architecture trained on 10,000+ real service cases 
                        to provide accurate, fast, and reliable diagnostic capabilities.
                    </p>
                </div>
                
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1.5rem;">
                    <div style="background: #f8fafc; padding: 1.5rem; border-radius: 10px; border-left: 4px solid var(--napkin-primary);">
                        <h4 style="color: var(--napkin-primary); margin-bottom: 1rem;">
                            <i class="fas fa-layer-group"></i> Deep Learning
                        </h4>
                        <p style="font-size: 0.9rem; color: #374151;">
                            Multi-layer neural network with 576 neurons for complex pattern recognition
                        </p>
                    </div>
                    
                    <div style="background: #f8fafc; padding: 1.5rem; border-radius: 10px; border-left: 4px solid var(--napkin-accent);">
                        <h4 style="color: var(--napkin-accent); margin-bottom: 1rem;">
                            <i class="fas fa-database"></i> Big Data Training
                        </h4>
                        <p style="font-size: 0.9rem; color: #374151;">
                            Trained on 10,000+ validated service cases from global installations
                        </p>
                    </div>
                    
                    <div style="background: #f8fafc; padding: 1.5rem; border-radius: 10px; border-left: 4px solid var(--napkin-success);">
                        <h4 style="color: var(--napkin-success); margin-bottom: 1rem;">
                            <i class="fas fa-bullseye"></i> High Accuracy
                        </h4>
                        <p style="font-size: 0.9rem; color: #374151;">
                            95.2% diagnostic accuracy with <2 second response time
                        </p>
                    </div>
                    
                    <div style="background: #f8fafc; padding: 1.5rem; border-radius: 10px; border-left: 4px solid var(--napkin-warning);">
                        <h4 style="color: var(--napkin-warning); margin-bottom: 1rem;">
                            <i class="fas fa-chart-line"></i> Predictive Analytics
                        </h4>
                        <p style="font-size: 0.9rem; color: #374151;">
                            Proactive maintenance recommendations and failure prediction
                        </p>
                    </div>
                </div>
                
                <div style="text-align: center; margin-top: 2rem;">
                    <button onclick="exploreNeuralNetwork()" style="background: var(--napkin-primary); color: white; border: none; padding: 1rem 2rem; border-radius: 25px; cursor: pointer; font-weight: 500; margin-right: 1rem;">
                        <i class="fas fa-sitemap"></i> Explore Network
                    </button>
                    <button onclick="viewTrainingData()" style="background: var(--napkin-accent); color: white; border: none; padding: 1rem 2rem; border-radius: 25px; cursor: pointer; font-weight: 500;">
                        <i class="fas fa-database"></i> View Training Data
                    </button>
                </div>
            </div>
        `
    });
}

function showLayer(layerId) {
    const layer = window.aiModule.layers[layerId];
    if (!layer) return;
    
    showModal({
        title: layer.title,
        content: `
            <div style="padding: 2rem;">
                <p style="font-size: 1.1rem; margin-bottom: 2rem; color: #374151;">
                    ${layer.description}
                </p>
                
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem; margin-bottom: 2rem;">
                    <div>
                        <h4 style="color: var(--napkin-primary); margin-bottom: 1rem;">Layer Specifications</h4>
                        <div style="background: #f8fafc; padding: 1rem; border-radius: 8px;">
                            ${layer.neurons ? `
                                <div style="display: flex; justify-content: space-between; padding: 0.5rem 0; border-bottom: 1px solid #e5e7eb;">
                                    <span style="font-weight: 500;">Neurons:</span>
                                    <span style="color: var(--napkin-primary);">${layer.neurons}</span>
                                </div>
                            ` : ''}
                            ${layer.activation ? `
                                <div style="display: flex; justify-content: space-between; padding: 0.5rem 0; border-bottom: 1px solid #e5e7eb;">
                                    <span style="font-weight: 500;">Activation:</span>
                                    <span style="color: var(--napkin-primary);">${layer.activation}</span>
                                </div>
                            ` : ''}
                            ${layer.algorithm ? `
                                <div style="display: flex; justify-content: space-between; padding: 0.5rem 0; border-bottom: 1px solid #e5e7eb;">
                                    <span style="font-weight: 500;">Algorithm:</span>
                                    <span style="color: var(--napkin-primary);">${layer.algorithm}</span>
                                </div>
                            ` : ''}
                            ${layer.learningRate ? `
                                <div style="display: flex; justify-content: space-between; padding: 0.5rem 0; border-bottom: 1px solid #e5e7eb;">
                                    <span style="font-weight: 500;">Learning Rate:</span>
                                    <span style="color: var(--napkin-primary);">${layer.learningRate}</span>
                                </div>
                            ` : ''}
                            ${layer.metrics ? `
                                <div style="display: flex; justify-content: space-between; padding: 0.5rem 0; border-bottom: 1px solid #e5e7eb;">
                                    <span style="font-weight: 500;">Metrics:</span>
                                    <span style="color: var(--napkin-primary);">${layer.metrics}</span>
                                </div>
                            ` : ''}
                            ${layer.testSize ? `
                                <div style="display: flex; justify-content: space-between; padding: 0.5rem 0;">
                                    <span style="font-weight: 500;">Test Size:</span>
                                    <span style="color: var(--napkin-primary);">${layer.testSize}</span>
                                </div>
                            ` : ''}
                        </div>
                    </div>
                    
                    <div>
                        <h4 style="color: var(--napkin-primary); margin-bottom: 1rem;">Key Functions</h4>
                        <div style="background: #f8fafc; padding: 1rem; border-radius: 8px;">
                            ${layer.details.map(detail => `
                                <div style="display: flex; align-items: center; gap: 0.75rem; padding: 0.5rem 0;">
                                    <div style="width: 8px; height: 8px; background: var(--napkin-primary); border-radius: 50%;"></div>
                                    <span>${detail}</span>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                </div>
                
                <div style="text-align: center;">
                    <button onclick="visualizeLayer('${layerId}')" style="background: var(--napkin-primary); color: white; border: none; padding: 0.75rem 2rem; border-radius: 25px; cursor: pointer; font-weight: 500; margin-right: 1rem;">
                        <i class="fas fa-eye"></i> Visualize Layer
                    </button>
                    <button onclick="analyzePerformance('${layerId}')" style="background: var(--napkin-accent); color: white; border: none; padding: 0.75rem 2rem; border-radius: 25px; cursor: pointer; font-weight: 500;">
                        <i class="fas fa-chart-line"></i> Performance Analysis
                    </button>
                </div>
            </div>
        `
    });
}

function loadCaseCategory(category) {
    if (window.aiModule) {
        window.aiModule.loadCaseCategory(category);
    }
}

function startTraining() {
    if (window.aiModule) {
        window.aiModule.isTraining = true;
        window.aiModule.trainingProgress = 0;
        
        // Show notification
        showNotification('Training started successfully!', 'success');
    }
}

function pauseTraining() {
    if (window.aiModule) {
        window.aiModule.isTraining = false;
        showNotification('Training paused.', 'warning');
    }
}

function exportModel() {
    showNotification('Model exported successfully!', 'success');
    alert('AI model export functionality will be available in the full platform.');
}

function showDiagnosticDetail(diagnosticId) {
    alert(`Detailed diagnostic analysis for ${diagnosticId} will be displayed in the full platform.`);
}

function viewSolution(diagnosticId) {
    alert(`Solution guide for ${diagnosticId} will be shown in the full platform.`);
}

function scheduleRepair(diagnosticId) {
    alert(`Repair scheduling for ${diagnosticId} will be available in the full platform.`);
}

function runCalibration(diagnosticId) {
    alert(`Calibration procedure for ${diagnosticId} will start in the full platform.`);
}

function planReplacement(diagnosticId) {
    alert(`Replacement planning for ${diagnosticId} will be available in the full platform.`);
}

function runDiagnostic(diagnosticId) {
    alert(`Extended diagnostic for ${diagnosticId} will run in the full platform.`);
}

function exploreNeuralNetwork() {
    closeModal();
    document.querySelector('.ai-mindmap').scrollIntoView({ behavior: 'smooth' });
}

function viewTrainingData() {
    closeModal();
    document.querySelector('.case-database').scrollIntoView({ behavior: 'smooth' });
}

function visualizeLayer(layerId) {
    alert(`Layer visualization for ${layerId} will be available in the full platform.`);
    closeModal();
}

function analyzePerformance(layerId) {
    alert(`Performance analysis for ${layerId} will be shown in the full platform.`);
    closeModal();
}

function showNotification(message, type) {
    const notification = document.createElement('div');
    const colors = {
        success: 'var(--napkin-success)',
        warning: 'var(--napkin-warning)',
        error: 'var(--napkin-danger)'
    };
    
    notification.style.cssText = `
        position: fixed;
        top: 2rem;
        right: 2rem;
        background: ${colors[type] || colors.success};
        color: white;
        padding: 1rem 2rem;
        border-radius: 8px;
        z-index: 10001;
        animation: slideIn 0.3s ease;
    `;
    notification.textContent = message;
    document.body.appendChild(notification);
    
    setTimeout(() => notification.remove(), 3000);
}

function showModal({ title, content }) {
    const modal = document.createElement('div');
    modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.8);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10000;
        animation: fadeIn 0.3s ease;
    `;
    
    modal.innerHTML = `
        <div style="background: white; border-radius: 15px; max-width: 800px; width: 90%; max-height: 80%; overflow-y: auto; position: relative;">
            <div style="background: var(--napkin-gradient); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;">
                <h3 style="margin: 0; font-size: 1.3rem;">${title}</h3>
                <button onclick="closeModal()" style="position: absolute; top: 1rem; right: 1rem; background: none; border: none; color: white; font-size: 1.5rem; cursor: pointer;">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div>${content}</div>
        </div>
    `;
    
    document.body.appendChild(modal);
    window.currentModal = modal;
    
    // Close on outside click
    modal.addEventListener('click', (e) => {
        if (e.target === modal) closeModal();
    });
}

function closeModal() {
    if (window.currentModal) {
        window.currentModal.remove();
        window.currentModal = null;
    }
}

// Add CSS for animations
const style = document.createElement('style');
style.textContent = `
    @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
    }
    
    @keyframes slideIn {
        from { transform: translateX(100%); }
        to { transform: translateX(0); }
    }
`;
document.head.appendChild(style);

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.aiModule = new AIModule();
});
