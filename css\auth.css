/* CSS for Authentication Pages */

/* Auth Page Layout */
.auth-page {
    background-color: #f8f9fa;
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 2rem 1rem;
}

.auth-container {
    width: 100%;
    max-width: 450px;
}

.auth-box {
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

/* Auth Header */
.auth-header {
    padding: 2rem;
    text-align: center;
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.auth-logo {
    max-width: 150px;
    height: auto;
    margin-bottom: 1rem;
}

.auth-header h1 {
    margin: 0;
    font-size: 1.5rem;
    color: #2c3e50;
}

/* Auth Tabs */
.auth-tabs {
    display: flex;
    border-bottom: 1px solid #e9ecef;
}

.auth-tab {
    flex: 1;
    padding: 1rem;
    text-align: center;
    background: none;
    border: none;
    font-weight: 600;
    color: #7f8c8d;
    cursor: pointer;
    transition: all 0.3s ease;
}

.auth-tab:hover {
    color: #3498db;
}

.auth-tab.active {
    color: #3498db;
    border-bottom: 2px solid #3498db;
}

/* Auth Forms */
.auth-content {
    padding: 2rem;
}

.auth-form {
    display: none;
}

.auth-form.active {
    display: block;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #2c3e50;
}

.input-with-icon {
    position: relative;
}

.input-with-icon i {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #7f8c8d;
}

.input-with-icon input {
    width: 100%;
    padding: 0.75rem 1rem 0.75rem 2.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.input-with-icon input:focus {
    border-color: #3498db;
    outline: none;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.toggle-password {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #7f8c8d;
    cursor: pointer;
    padding: 0;
}

.toggle-password:focus {
    outline: none;
}

/* Password Strength Meter */
.password-strength {
    margin-top: 0.5rem;
}

.strength-meter {
    height: 5px;
    background-color: #e9ecef;
    border-radius: 3px;
    overflow: hidden;
    margin-bottom: 0.25rem;
}

.strength-bar {
    height: 100%;
    width: 0;
    background-color: #ff4d4d;
    transition: width 0.3s ease, background-color 0.3s ease;
}

.strength-text {
    font-size: 0.8rem;
    color: #7f8c8d;
}

/* Remember Me & Forgot Password */
.remember-me {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.checkbox-container {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 0.9rem;
    color: #7f8c8d;
}

.checkbox-container input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;
}

.checkmark {
    height: 18px;
    width: 18px;
    background-color: #f8f9fa;
    border: 1px solid #ddd;
    border-radius: 3px;
    margin-right: 0.5rem;
    position: relative;
}

.checkbox-container:hover input ~ .checkmark {
    background-color: #e9ecef;
}

.checkbox-container input:checked ~ .checkmark {
    background-color: #3498db;
    border-color: #3498db;
}

.checkmark:after {
    content: "";
    position: absolute;
    display: none;
}

.checkbox-container input:checked ~ .checkmark:after {
    display: block;
}

.checkbox-container .checkmark:after {
    left: 6px;
    top: 2px;
    width: 5px;
    height: 10px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

.forgot-password {
    font-size: 0.9rem;
    color: #3498db;
    text-decoration: none;
}

.forgot-password:hover {
    text-decoration: underline;
}

/* Buttons */
.btn-block {
    width: 100%;
    padding: 0.75rem;
    font-size: 1rem;
}

/* Auth Messages */
.auth-message {
    margin-top: 1rem;
    padding: 0.75rem;
    border-radius: 4px;
    text-align: center;
    font-size: 0.9rem;
}

.auth-message.error {
    background-color: #ffecec;
    color: #e74c3c;
    border: 1px solid #f8d7da;
}

.auth-message.success {
    background-color: #ecffec;
    color: #2ecc71;
    border: 1px solid #d7f8da;
}

/* Auth Footer */
.auth-footer {
    padding: 1rem 2rem;
    background-color: #f8f9fa;
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.auth-footer p {
    margin: 0;
    font-size: 0.8rem;
    color: #7f8c8d;
}

/* RTL Support */
.lang-ar .input-with-icon i {
    left: auto;
    right: 1rem;
}

.lang-ar .input-with-icon input {
    padding: 0.75rem 2.5rem 0.75rem 1rem;
}

.lang-ar .toggle-password {
    right: auto;
    left: 1rem;
}

.lang-ar .checkmark {
    margin-right: 0;
    margin-left: 0.5rem;
}

/* Responsive Adjustments */
@media (max-width: 480px) {
    .auth-container {
        max-width: 100%;
    }
    
    .auth-header {
        padding: 1.5rem;
    }
    
    .auth-content {
        padding: 1.5rem;
    }
    
    .auth-tab {
        padding: 0.75rem;
    }
    
    .remember-me {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .forgot-password {
        margin-top: 0.5rem;
    }
}
