/* CSS for Content Protection */

/* Protected Content */
.protected-content {
    position: relative;
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 2rem;
    margin: 1rem 0;
    min-height: 150px;
    border: 1px solid #e9ecef;
    overflow: hidden;
}

.protected-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: repeating-linear-gradient(
        45deg,
        rgba(0, 0, 0, 0.03),
        rgba(0, 0, 0, 0.03) 10px,
        rgba(0, 0, 0, 0.05) 10px,
        rgba(0, 0, 0, 0.05) 20px
    );
    z-index: 1;
}

/* Access Denied Message */
.access-denied {
    position: relative;
    z-index: 2;
    text-align: center;
    padding: 2rem;
}

.access-denied i {
    font-size: 3rem;
    color: #e74c3c;
    margin-bottom: 1rem;
}

.access-denied h3 {
    color: #e74c3c;
    margin-bottom: 1rem;
}

.access-denied p {
    color: #7f8c8d;
}

/* Decrypted Content */
.decrypted-content {
    position: relative;
    animation: fadeIn 0.5s ease;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

/* Content Access Levels */
.content-access-badge {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 600;
    margin-left: 0.5rem;
}

.access-level-basic {
    background-color: #e9f7fe;
    color: #3498db;
    border: 1px solid #3498db;
}

.access-level-advanced {
    background-color: #fef5e9;
    color: #e67e22;
    border: 1px solid #e67e22;
}

.access-level-premium {
    background-color: #f9e9f9;
    color: #9b59b6;
    border: 1px solid #9b59b6;
}

/* Module Lock Icons */
.module-card.locked {
    position: relative;
}

.module-card.locked::after {
    content: '\f023';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    position: absolute;
    top: 1rem;
    right: 1rem;
    font-size: 1.5rem;
    color: #e74c3c;
}

.module-card.locked .module-icon {
    opacity: 0.5;
}

.module-card.locked h3,
.module-card.locked p {
    opacity: 0.7;
}

.module-card.locked .btn {
    background-color: #e74c3c;
}

.module-card.locked .btn:hover {
    background-color: #c0392b;
}

/* Encrypted Content Placeholder */
.encrypted-placeholder {
    background-color: #f8f9fa;
    border: 1px dashed #ddd;
    border-radius: 8px;
    padding: 1rem;
    margin: 1rem 0;
    text-align: center;
    color: #7f8c8d;
}

.encrypted-placeholder i {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    color: #3498db;
}

/* Content Access Request */
.access-request {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1.5rem;
    margin: 1rem 0;
    text-align: center;
}

.access-request h3 {
    margin-bottom: 1rem;
    color: #2c3e50;
}

.access-request p {
    margin-bottom: 1.5rem;
    color: #7f8c8d;
}

.access-request .btn {
    margin-top: 0.5rem;
}

/* RTL Support */
.lang-ar .content-access-badge {
    margin-left: 0;
    margin-right: 0.5rem;
}

.lang-ar .module-card.locked::after {
    right: auto;
    left: 1rem;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .protected-content {
        padding: 1.5rem;
        min-height: 120px;
    }
    
    .access-denied {
        padding: 1rem;
    }
    
    .access-denied i {
        font-size: 2rem;
    }
}
