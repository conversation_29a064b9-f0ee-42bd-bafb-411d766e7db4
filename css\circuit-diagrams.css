/* CSS for X-ray System Electrical Circuit Diagrams Module */

/* Overview Section */
.overview {
    padding: 3rem 0;
    background-color: #f8f9fa;
}

.info-box {
    display: flex;
    background-color: white;
    border-radius: 8px;
    padding: 1.5rem;
    margin-top: 2rem;
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1);
    gap: 1.5rem;
}

.info-icon {
    font-size: 2.5rem;
    color: #3498db;
    flex-shrink: 0;
}

.info-content h3 {
    margin-top: 0;
    color: #2c3e50;
    margin-bottom: 1rem;
}

.info-content ul {
    margin-left: 1.5rem;
}

.info-content li {
    margin-bottom: 0.5rem;
}

/* Circuit Symbols */
.circuit-symbols {
    margin-top: 3rem;
}

.circuit-symbols h3 {
    margin-bottom: 1.5rem;
    color: #2c3e50;
}

.symbols-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 1.5rem;
}

.symbol-item {
    background-color: white;
    border-radius: 8px;
    padding: 1rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    text-align: center;
    transition: transform 0.3s ease;
}

.symbol-item:hover {
    transform: translateY(-5px);
}

.symbol {
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 0.5rem;
}

.symbol img {
    max-width: 100%;
    max-height: 100%;
}

.symbol-name {
    font-weight: 600;
    color: #2c3e50;
}

/* Circuit Sections */
.circuit-section {
    padding: 4rem 0;
}

.circuit-section:nth-child(odd) {
    background-color: white;
}

.circuit-section:nth-child(even) {
    background-color: #f8f9fa;
}

.circuit-container {
    display: flex;
    flex-wrap: wrap;
    gap: 2rem;
    margin-top: 2rem;
}

.circuit-diagram {
    flex: 3 1 500px;
    background-color: white;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: auto;
}

.circuit-diagram img {
    max-width: 100%;
    height: auto;
}

.circuit-details {
    flex: 2 1 300px;
    background-color: white;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1);
}

.circuit-details h3 {
    margin-top: 0;
    color: #2c3e50;
    margin-bottom: 1rem;
    border-bottom: 2px solid #3498db;
    padding-bottom: 0.5rem;
}

.circuit-details ul, .circuit-details ol {
    margin-left: 1.5rem;
    margin-bottom: 1.5rem;
}

.circuit-details li {
    margin-bottom: 0.5rem;
}

/* Circuit Tabs */
.circuit-tabs {
    display: flex;
    border-bottom: 2px solid #e9ecef;
    margin-bottom: 1.5rem;
}

.tab-btn {
    padding: 0.75rem 1.5rem;
    background: none;
    border: none;
    border-bottom: 2px solid transparent;
    margin-bottom: -2px;
    cursor: pointer;
    font-weight: 600;
    color: #7f8c8d;
    transition: all 0.3s ease;
}

.tab-btn:hover {
    color: #3498db;
}

.tab-btn.active {
    color: #3498db;
    border-bottom-color: #3498db;
}

.tab-content {
    min-height: 300px;
}

.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block;
}

/* Troubleshooting Items */
.troubleshooting-item {
    margin-bottom: 1.5rem;
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 1rem;
    border-left: 4px solid #e74c3c;
}

.troubleshooting-item h4 {
    margin-top: 0;
    color: #e74c3c;
    margin-bottom: 0.5rem;
}

.troubleshooting-item ul {
    margin-bottom: 0;
}

.common-issues {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 1rem;
    border-left: 4px solid #e74c3c;
    margin-top: 1.5rem;
}

.common-issues h4 {
    margin-top: 0;
    color: #e74c3c;
    margin-bottom: 0.5rem;
}

.common-issues ul {
    margin-bottom: 0;
}

/* Navigation Buttons */
.navigation-buttons {
    padding: 2rem 0;
    background-color: #f8f9fa;
    border-top: 1px solid #e9ecef;
}

.nav-buttons {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 1rem;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .info-box {
        flex-direction: column;
    }
    
    .circuit-container {
        flex-direction: column;
    }
    
    .circuit-diagram, .circuit-details {
        flex: none;
        width: 100%;
    }
    
    .circuit-tabs {
        flex-wrap: wrap;
    }
    
    .tab-btn {
        flex: 1 1 auto;
        text-align: center;
        padding: 0.5rem;
    }
    
    .nav-buttons {
        justify-content: center;
    }
    
    .nav-buttons .btn {
        width: 100%;
        text-align: center;
        margin-bottom: 0.5rem;
    }
    
    .symbols-grid {
        grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    }
}
