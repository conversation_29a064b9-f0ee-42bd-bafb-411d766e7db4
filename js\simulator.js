/**
 * JavaScript for X-ray Physics Simulator
 */

document.addEventListener('DOMContentLoaded', function() {
    // Get DOM elements
    const kvSlider = document.getElementById('kv-slider');
    const maSlider = document.getElementById('ma-slider');
    const timeSlider = document.getElementById('time-slider');
    const kvValue = document.getElementById('kv-value');
    const maValue = document.getElementById('ma-value');
    const timeValue = document.getElementById('time-value');
    const materialSelect = document.getElementById('material-select');
    const exposeBtn = document.getElementById('expose-btn');
    
    const radiationOutput = document.getElementById('radiation-output');
    const imageSnr = document.getElementById('image-snr');
    const contrastRatio = document.getElementById('contrast-ratio');
    
    const xrayBeam = document.getElementById('xray-beam');
    const phantom = document.getElementById('phantom');
    const phantomStructure = document.getElementById('phantom-structure');
    const detector = document.getElementById('detector');
    const xrayImage = document.getElementById('xray-image');
    
    // Material properties (density and atomic number relative values)
    const materialProperties = {
        'soft-tissue': { density: 0.3, atomicNumber: 0.2, color: 'rgba(255, 200, 200, 0.8)' },
        'bone': { density: 0.8, atomicNumber: 0.5, color: 'rgba(255, 255, 255, 0.9)' },
        'lung': { density: 0.1, atomicNumber: 0.2, color: 'rgba(200, 200, 255, 0.5)' },
        'contrast': { density: 0.6, atomicNumber: 0.8, color: 'rgba(255, 255, 200, 0.8)' },
        'metal': { density: 1.0, atomicNumber: 1.0, color: 'rgba(200, 200, 200, 1.0)' }
    };
    
    // Update display values when sliders change
    kvSlider.addEventListener('input', function() {
        kvValue.textContent = `${this.value} kVp`;
    });
    
    maSlider.addEventListener('input', function() {
        maValue.textContent = `${this.value} mA`;
    });
    
    timeSlider.addEventListener('input', function() {
        timeValue.textContent = `${this.value} ms`;
    });
    
    // Update phantom appearance when material changes
    materialSelect.addEventListener('change', function() {
        const material = this.value;
        const properties = materialProperties[material];
        
        if (properties) {
            phantomStructure.style.backgroundColor = properties.color;
            // Adjust opacity based on density
            phantomStructure.style.opacity = 0.3 + (properties.density * 0.7);
        }
    });
    
    // Take X-ray exposure
    exposeBtn.addEventListener('click', function() {
        // Get current parameter values
        const kv = parseInt(kvSlider.value);
        const ma = parseInt(maSlider.value);
        const time = parseInt(timeSlider.value);
        const material = materialSelect.value;
        const materialProps = materialProperties[material];
        
        // Disable button during exposure
        this.disabled = true;
        
        // Simulate X-ray beam
        xrayBeam.classList.add('active');
        
        // Calculate exposure metrics
        const mAs = ma * (time / 1000); // Convert ms to seconds
        const radiationDose = calculateRadiationDose(kv, mAs);
        const snr = calculateSNR(kv, mAs, materialProps);
        const contrast = calculateContrast(kv, materialProps);
        
        // Update measurements after a delay
        setTimeout(() => {
            radiationOutput.textContent = `${radiationDose.toFixed(2)} mGy`;
            imageSnr.textContent = snr.toFixed(1);
            contrastRatio.textContent = contrast.toFixed(2);
        }, 500);
        
        // Generate X-ray image after a delay
        setTimeout(() => {
            generateXrayImage(kv, mAs, materialProps);
            
            // Turn off beam
            xrayBeam.classList.remove('active');
            
            // Re-enable button
            exposeBtn.disabled = false;
        }, 1500);
    });
    
    /**
     * Calculate radiation dose based on exposure parameters
     * @param {number} kv - kVp value
     * @param {number} mAs - mAs value
     * @returns {number} - Radiation dose in mGy
     */
    function calculateRadiationDose(kv, mAs) {
        // Simple model: dose ~ kV^2 * mAs / 100
        return (kv * kv * mAs) / 10000;
    }
    
    /**
     * Calculate signal-to-noise ratio
     * @param {number} kv - kVp value
     * @param {number} mAs - mAs value
     * @param {object} material - Material properties
     * @returns {number} - SNR value
     */
    function calculateSNR(kv, mAs, material) {
        // Simple model: SNR ~ sqrt(mAs) * (1 - material.density * 0.5)
        const signal = Math.sqrt(mAs) * 2;
        const noise = 1 + (material.density * 0.5);
        return signal / noise;
    }
    
    /**
     * Calculate contrast ratio
     * @param {number} kv - kVp value
     * @param {object} material - Material properties
     * @returns {number} - Contrast ratio
     */
    function calculateContrast(kv, material) {
        // Simple model: contrast decreases with kV, increases with atomic number difference
        const kvFactor = 1 - (kv - 40) / 110; // 1.0 at 40kV, 0.0 at 150kV
        const materialFactor = 0.5 + material.atomicNumber;
        return kvFactor * materialFactor * 5;
    }
    
    /**
     * Generate X-ray image based on exposure parameters and material
     * @param {number} kv - kVp value
     * @param {number} mAs - mAs value
     * @param {object} material - Material properties
     */
    function generateXrayImage(kv, mAs, material) {
        // Clear previous image
        xrayImage.innerHTML = '';
        
        // Create canvas for X-ray image
        const canvas = document.createElement('canvas');
        canvas.width = 300;
        canvas.height = 200;
        canvas.style.width = '100%';
        canvas.style.height = 'auto';
        canvas.style.maxWidth = '300px';
        canvas.style.borderRadius = '8px';
        canvas.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.2)';
        
        const ctx = canvas.getContext('2d');
        
        // Fill background (white = no attenuation, black = complete attenuation)
        ctx.fillStyle = '#e0e0e0'; // Light gray background
        ctx.fillRect(0, 0, canvas.width, canvas.height);
        
        // Calculate attenuation based on material and kV
        const attenuation = calculateAttenuation(kv, material);
        
        // Draw phantom outline
        ctx.fillStyle = '#d0d0d0';
        drawRoundedRect(ctx, 75, 50, 150, 100, 10);
        
        // Draw structure with attenuation
        const grayValue = Math.floor(255 * (1 - attenuation));
        ctx.fillStyle = `rgb(${grayValue}, ${grayValue}, ${grayValue})`;
        ctx.beginPath();
        ctx.arc(150, 100, 30, 0, Math.PI * 2);
        ctx.fill();
        
        // Add noise based on mAs (lower mAs = more noise)
        addNoise(ctx, canvas.width, canvas.height, mAs);
        
        // Add image to the DOM
        xrayImage.appendChild(canvas);
        
        // Add exposure info
        const exposureInfo = document.createElement('div');
        exposureInfo.className = 'exposure-info';
        exposureInfo.style.marginTop = '1rem';
        exposureInfo.style.fontSize = '0.9rem';
        exposureInfo.style.color = '#666';
        exposureInfo.innerHTML = `
            <p><strong>Exposure Parameters:</strong> ${kv} kVp, ${mAs.toFixed(2)} mAs</p>
            <p><strong>Material:</strong> ${materialSelect.options[materialSelect.selectedIndex].text}</p>
        `;
        xrayImage.appendChild(exposureInfo);
    }
    
    /**
     * Calculate X-ray attenuation based on kV and material
     * @param {number} kv - kVp value
     * @param {object} material - Material properties
     * @returns {number} - Attenuation value (0-1)
     */
    function calculateAttenuation(kv, material) {
        // Simple model: attenuation decreases with kV, increases with density and atomic number
        const kvFactor = 1 - (kv - 40) / 150; // 1.0 at 40kV, ~0.27 at 150kV
        return material.density * material.atomicNumber * kvFactor;
    }
    
    /**
     * Add noise to the image based on mAs
     * @param {CanvasRenderingContext2D} ctx - Canvas context
     * @param {number} width - Canvas width
     * @param {number} height - Canvas height
     * @param {number} mAs - mAs value
     */
    function addNoise(ctx, width, height, mAs) {
        // Get image data
        const imageData = ctx.getImageData(0, 0, width, height);
        const data = imageData.data;
        
        // Calculate noise level (lower mAs = more noise)
        const noiseLevel = 20 / Math.sqrt(mAs);
        
        // Add random noise to each pixel
        for (let i = 0; i < data.length; i += 4) {
            const noise = (Math.random() - 0.5) * noiseLevel;
            
            // Add noise to RGB channels
            data[i] = Math.min(255, Math.max(0, data[i] + noise));
            data[i + 1] = Math.min(255, Math.max(0, data[i + 1] + noise));
            data[i + 2] = Math.min(255, Math.max(0, data[i + 2] + noise));
            // Alpha channel remains unchanged
        }
        
        // Put the modified image data back
        ctx.putImageData(imageData, 0, 0);
    }
    
    /**
     * Draw a rounded rectangle
     * @param {CanvasRenderingContext2D} ctx - Canvas context
     * @param {number} x - X position
     * @param {number} y - Y position
     * @param {number} width - Rectangle width
     * @param {number} height - Rectangle height
     * @param {number} radius - Corner radius
     */
    function drawRoundedRect(ctx, x, y, width, height, radius) {
        ctx.beginPath();
        ctx.moveTo(x + radius, y);
        ctx.lineTo(x + width - radius, y);
        ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
        ctx.lineTo(x + width, y + height - radius);
        ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
        ctx.lineTo(x + radius, y + height);
        ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
        ctx.lineTo(x, y + radius);
        ctx.quadraticCurveTo(x, y, x + radius, y);
        ctx.closePath();
        ctx.fill();
    }
    
    // Initialize with default material
    materialSelect.dispatchEvent(new Event('change'));
});
