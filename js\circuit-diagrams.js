/**
 * JavaScript for X-ray System Electrical Circuit Diagrams Module
 */

document.addEventListener('DOMContentLoaded', function() {
    // Navigation active state management
    const navLinks = document.querySelectorAll('nav ul li a');
    const sections = document.querySelectorAll('section[id]');
    
    // Smooth scrolling for navigation links
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            // Only prevent default if it's an anchor link
            if (this.getAttribute('href').startsWith('#')) {
                e.preventDefault();
                
                const targetId = this.getAttribute('href');
                const targetSection = document.querySelector(targetId);
                
                if (targetSection) {
                    window.scrollTo({
                        top: targetSection.offsetTop - 60, // Adjust for nav height
                        behavior: 'smooth'
                    });
                    
                    // Update active link
                    navLinks.forEach(link => link.classList.remove('active'));
                    this.classList.add('active');
                }
            }
        });
    });
    
    // Update active navigation link on scroll
    window.addEventListener('scroll', function() {
        let current = '';
        
        sections.forEach(section => {
            const sectionTop = section.offsetTop;
            const sectionHeight = section.clientHeight;
            
            if (pageYOffset >= sectionTop - 100) {
                current = section.getAttribute('id');
            }
        });
        
        navLinks.forEach(link => {
            link.classList.remove('active');
            const href = link.getAttribute('href');
            if (href && href === `#${current}`) {
                link.classList.add('active');
            }
        });
    });
    
    // Initialize circuit diagram tabs
    initializeCircuitTabs();
    
    // Initialize interactive circuit diagrams
    initializeInteractiveCircuits();
});

/**
 * Initialize tab functionality for circuit details
 */
function initializeCircuitTabs() {
    const tabButtons = document.querySelectorAll('.tab-btn');
    
    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Get the parent tab container
            const tabContainer = this.closest('.circuit-details');
            if (!tabContainer) return;
            
            // Get the tab content container
            const tabContent = tabContainer.querySelector('.tab-content');
            if (!tabContent) return;
            
            // Get the tab to show
            const tabName = this.getAttribute('data-tab');
            
            // Remove active class from all buttons in this container
            tabContainer.querySelectorAll('.tab-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            
            // Add active class to clicked button
            this.classList.add('active');
            
            // Hide all tab panes in this container
            tabContent.querySelectorAll('.tab-pane').forEach(pane => {
                pane.classList.remove('active');
            });
            
            // Show the selected tab pane
            // First try with specific ID for the section
            let targetPane = tabContent.querySelector(`#${tabName}-tab`);
            
            // If not found, try generic tab name
            if (!targetPane) {
                targetPane = tabContent.querySelector(`#${tabName}`);
            }
            
            // If still not found, try first child with class tab-pane
            if (!targetPane) {
                targetPane = tabContent.querySelector('.tab-pane');
            }
            
            if (targetPane) {
                targetPane.classList.add('active');
            }
        });
    });
}

/**
 * Initialize interactive circuit diagrams
 */
function initializeInteractiveCircuits() {
    // High Voltage Circuit Interactivity
    const highVoltageCircuit = document.getElementById('high-voltage-diagram');
    if (highVoltageCircuit) {
        // Add hover effects or click events for components
        addCircuitComponentInteractivity(highVoltageCircuit);
    }
    
    // Filament Circuit Interactivity
    const filamentCircuit = document.getElementById('filament-diagram');
    if (filamentCircuit) {
        // Add hover effects or click events for components
        addCircuitComponentInteractivity(filamentCircuit);
    }
    
    // Control Circuit Interactivity
    const controlCircuit = document.getElementById('control-diagram');
    if (controlCircuit) {
        // Add hover effects or click events for components
        addCircuitComponentInteractivity(controlCircuit);
    }
}

/**
 * Add interactivity to circuit diagram components
 * @param {HTMLElement} circuitElement - The circuit diagram container
 */
function addCircuitComponentInteractivity(circuitElement) {
    // This function would add hover or click effects to components in the circuit diagram
    // For placeholder images, we'll add a simple hover effect
    const circuitImage = circuitElement.querySelector('img');
    
    if (circuitImage) {
        // Add hover effect to the entire diagram
        circuitImage.addEventListener('mouseenter', function() {
            this.style.transform = 'scale(1.02)';
            this.style.transition = 'transform 0.3s ease';
            this.style.cursor = 'zoom-in';
        });
        
        circuitImage.addEventListener('mouseleave', function() {
            this.style.transform = 'scale(1)';
        });
        
        // Add click to zoom functionality
        circuitImage.addEventListener('click', function() {
            // Create a modal to show the enlarged circuit
            createCircuitModal(this.src, this.alt);
        });
    }
}

/**
 * Create a modal to display an enlarged circuit diagram
 * @param {string} imageSrc - The source URL of the image
 * @param {string} imageAlt - The alt text of the image
 */
function createCircuitModal(imageSrc, imageAlt) {
    // Create modal container
    const modalContainer = document.createElement('div');
    modalContainer.className = 'circuit-modal-container';
    modalContainer.style.position = 'fixed';
    modalContainer.style.top = '0';
    modalContainer.style.left = '0';
    modalContainer.style.width = '100%';
    modalContainer.style.height = '100%';
    modalContainer.style.backgroundColor = 'rgba(0, 0, 0, 0.8)';
    modalContainer.style.display = 'flex';
    modalContainer.style.justifyContent = 'center';
    modalContainer.style.alignItems = 'center';
    modalContainer.style.zIndex = '1000';
    
    // Create image container
    const imageContainer = document.createElement('div');
    imageContainer.style.position = 'relative';
    imageContainer.style.maxWidth = '90%';
    imageContainer.style.maxHeight = '90%';
    imageContainer.style.overflow = 'auto';
    
    // Create image
    const image = document.createElement('img');
    image.src = imageSrc;
    image.alt = imageAlt;
    image.style.maxWidth = '100%';
    image.style.maxHeight = '90vh';
    image.style.display = 'block';
    image.style.margin = '0 auto';
    image.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.4)';
    
    // Create close button
    const closeButton = document.createElement('button');
    closeButton.innerHTML = '&times;';
    closeButton.style.position = 'absolute';
    closeButton.style.top = '10px';
    closeButton.style.right = '10px';
    closeButton.style.background = 'rgba(0, 0, 0, 0.5)';
    closeButton.style.color = 'white';
    closeButton.style.border = 'none';
    closeButton.style.borderRadius = '50%';
    closeButton.style.width = '40px';
    closeButton.style.height = '40px';
    closeButton.style.fontSize = '24px';
    closeButton.style.cursor = 'pointer';
    closeButton.style.display = 'flex';
    closeButton.style.justifyContent = 'center';
    closeButton.style.alignItems = 'center';
    
    // Add caption
    const caption = document.createElement('div');
    caption.textContent = imageAlt;
    caption.style.color = 'white';
    caption.style.textAlign = 'center';
    caption.style.padding = '1rem';
    caption.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
    caption.style.borderRadius = '0 0 8px 8px';
    
    // Assemble modal
    imageContainer.appendChild(image);
    imageContainer.appendChild(closeButton);
    modalContainer.appendChild(imageContainer);
    modalContainer.appendChild(caption);
    
    // Add close functionality
    closeButton.addEventListener('click', function() {
        document.body.removeChild(modalContainer);
    });
    
    // Add click outside to close
    modalContainer.addEventListener('click', function(e) {
        if (e.target === modalContainer) {
            document.body.removeChild(modalContainer);
        }
    });
    
    // Add to document
    document.body.appendChild(modalContainer);
}
