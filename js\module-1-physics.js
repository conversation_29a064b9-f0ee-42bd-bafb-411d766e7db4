/**
 * VirtualX Pro - Module 1: X-ray Physics Fundamentals
 * Interactive demonstrations and mind map navigation
 */

class PhysicsModule {
    constructor() {
        this.currentAnimation = null;
        this.animationCanvas = document.getElementById('animationCanvas');
        this.concepts = {
            production: {
                title: 'X-ray Production',
                description: 'Understanding Bremsstrahlung and Characteristic radiation',
                details: [
                    'Electron acceleration in electric field',
                    'Target interaction mechanisms',
                    'Energy conversion efficiency',
                    'Spectrum characteristics'
                ]
            },
            interaction: {
                title: 'Matter Interaction',
                description: 'How X-rays interact with matter',
                details: [
                    'Photoelectric absorption',
                    'Compton scattering',
                    'Coherent scattering',
                    'Pair production (high energy)'
                ]
            },
            attenuation: {
                title: 'Beam Attenuation',
                description: 'Beer-Lambert law and exponential decay',
                details: [
                    'Linear attenuation coefficient',
                    'Mass attenuation coefficient',
                    'Half-value layer (HVL)',
                    'Beam hardening effects'
                ]
            },
            safety: {
                title: 'Radiation Safety',
                description: 'ALARA principles and protection methods',
                details: [
                    'Time, distance, shielding',
                    'Dose rate calculations',
                    'Shielding design',
                    'Regulatory compliance'
                ]
            }
        };
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.initializeAnimations();
        this.startParticleEffects();
    }

    setupEventListeners() {
        // Mind map node interactions
        document.querySelectorAll('.branch-node').forEach(node => {
            node.addEventListener('mouseenter', this.highlightConnections.bind(this));
            node.addEventListener('mouseleave', this.resetConnections.bind(this));
        });

        // Demo tool hover effects
        document.querySelectorAll('.demo-tool').forEach(tool => {
            tool.addEventListener('mouseenter', this.animateToolIcon.bind(this));
        });
    }

    highlightConnections(event) {
        const node = event.target.closest('.branch-node');
        const nodeClass = Array.from(node.classList).find(cls => cls.startsWith('node-'));
        const lineClass = nodeClass.replace('node-', 'line-');
        
        // Highlight corresponding connection line
        const line = document.querySelector(`.${lineClass}`);
        if (line) {
            line.style.background = 'var(--napkin-accent)';
            line.style.height = '4px';
            line.style.opacity = '1';
            line.style.boxShadow = '0 0 10px var(--napkin-accent)';
        }
    }

    resetConnections() {
        document.querySelectorAll('.connection-line').forEach(line => {
            line.style.background = 'var(--napkin-primary)';
            line.style.height = '2px';
            line.style.opacity = '0.6';
            line.style.boxShadow = 'none';
        });
    }

    animateToolIcon(event) {
        const icon = event.target.querySelector('.tool-icon');
        if (icon) {
            icon.style.transform = 'scale(1.1) rotate(5deg)';
            icon.style.transition = 'all 0.3s ease';
            
            setTimeout(() => {
                icon.style.transform = 'scale(1) rotate(0deg)';
            }, 300);
        }
    }

    initializeAnimations() {
        this.animations = {
            bremsstrahlung: {
                title: 'Bremsstrahlung Radiation',
                description: 'Continuous X-ray spectrum from electron deceleration',
                frames: this.createBremsstrahlungAnimation()
            },
            characteristic: {
                title: 'Characteristic X-rays',
                description: 'Discrete energy X-rays from electron shell transitions',
                frames: this.createCharacteristicAnimation()
            },
            photoelectric: {
                title: 'Photoelectric Effect',
                description: 'Complete absorption of X-ray photon',
                frames: this.createPhotoelectricAnimation()
            },
            compton: {
                title: 'Compton Scattering',
                description: 'Partial energy transfer and photon scattering',
                frames: this.createComptonAnimation()
            }
        };
    }

    createBremsstrahlungAnimation() {
        return [
            { time: 0, description: 'High-energy electron approaches tungsten nucleus' },
            { time: 1000, description: 'Electron deflected by nuclear electric field' },
            { time: 2000, description: 'Kinetic energy converted to X-ray photon' },
            { time: 3000, description: 'Continuous spectrum X-ray emitted' },
            { time: 4000, description: 'Process repeats with varying energy loss' }
        ];
    }

    createCharacteristicAnimation() {
        return [
            { time: 0, description: 'Incident electron ejects K-shell electron' },
            { time: 1000, description: 'Vacancy created in inner electron shell' },
            { time: 2000, description: 'L-shell electron drops to fill K-shell vacancy' },
            { time: 3000, description: 'Characteristic X-ray photon emitted' },
            { time: 4000, description: 'Discrete energy equals binding energy difference' }
        ];
    }

    createPhotoelectricAnimation() {
        return [
            { time: 0, description: 'X-ray photon approaches atom' },
            { time: 1000, description: 'Photon energy absorbed by K-shell electron' },
            { time: 2000, description: 'Photoelectron ejected with kinetic energy' },
            { time: 3000, description: 'Characteristic radiation may follow' },
            { time: 4000, description: 'Complete photon absorption achieved' }
        ];
    }

    createComptonAnimation() {
        return [
            { time: 0, description: 'X-ray photon collides with outer shell electron' },
            { time: 1000, description: 'Partial energy transfer occurs' },
            { time: 2000, description: 'Scattered photon with reduced energy' },
            { time: 3000, description: 'Recoil electron gains kinetic energy' },
            { time: 4000, description: 'Scattering angle determines energy loss' }
        ];
    }

    startParticleEffects() {
        const container = document.querySelector('.module-header');
        setInterval(() => {
            this.createParticle(container);
        }, 2000);
    }

    createParticle(container) {
        const particle = document.createElement('div');
        particle.style.cssText = `
            position: absolute;
            width: 4px;
            height: 4px;
            background: rgba(255, 255, 255, 0.6);
            border-radius: 50%;
            pointer-events: none;
            left: ${Math.random() * 100}%;
            top: 100%;
            animation: particleFloat 8s linear forwards;
        `;
        
        container.appendChild(particle);
        
        setTimeout(() => {
            particle.remove();
        }, 8000);
    }
}

// Global functions for HTML onclick handlers
function showCentralConcept() {
    showModal({
        title: 'X-ray Physics Fundamentals',
        content: `
            <div style="text-align: center; padding: 2rem;">
                <div style="font-size: 4rem; color: var(--napkin-primary); margin-bottom: 1rem;">
                    <i class="fas fa-atom"></i>
                </div>
                <h3 style="margin-bottom: 1rem;">Core Physics Principles</h3>
                <p style="margin-bottom: 2rem; color: #6b7280;">
                    Master the fundamental physics behind X-ray generation, interaction with matter, 
                    and radiation safety through interactive simulations and real-world applications.
                </p>
                <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 1rem; text-align: left;">
                    <div style="background: #f8fafc; padding: 1rem; border-radius: 8px;">
                        <strong>Learning Objectives:</strong>
                        <ul style="margin-top: 0.5rem; padding-left: 1rem;">
                            <li>X-ray production mechanisms</li>
                            <li>Matter interaction principles</li>
                            <li>Attenuation calculations</li>
                            <li>Safety protocols</li>
                        </ul>
                    </div>
                    <div style="background: #f8fafc; padding: 1rem; border-radius: 8px;">
                        <strong>Interactive Tools:</strong>
                        <ul style="margin-top: 0.5rem; padding-left: 1rem;">
                            <li>3D simulations</li>
                            <li>Spectrum analyzer</li>
                            <li>HVL calculator</li>
                            <li>Shielding designer</li>
                        </ul>
                    </div>
                </div>
            </div>
        `
    });
}

function showConcept(conceptId) {
    const concept = window.physicsModule.concepts[conceptId];
    if (!concept) return;
    
    showModal({
        title: concept.title,
        content: `
            <div style="padding: 2rem;">
                <p style="font-size: 1.1rem; margin-bottom: 2rem; color: #374151;">
                    ${concept.description}
                </p>
                <h4 style="margin-bottom: 1rem; color: var(--napkin-primary);">Key Concepts:</h4>
                <div style="display: grid; gap: 0.75rem;">
                    ${concept.details.map(detail => `
                        <div style="display: flex; align-items: center; gap: 0.75rem; padding: 0.75rem; background: #f8fafc; border-radius: 8px;">
                            <div style="width: 8px; height: 8px; background: var(--napkin-primary); border-radius: 50%;"></div>
                            <span>${detail}</span>
                        </div>
                    `).join('')}
                </div>
                <div style="margin-top: 2rem; text-align: center;">
                    <button onclick="openDemo('${conceptId}')" style="background: var(--napkin-primary); color: white; border: none; padding: 0.75rem 2rem; border-radius: 25px; cursor: pointer; font-weight: 500;">
                        <i class="fas fa-play"></i> Start Interactive Demo
                    </button>
                </div>
            </div>
        `
    });
}

function openDemo(demoId) {
    const demos = {
        'electron-target': {
            title: '3D Electron-Target Interaction Simulator',
            description: 'Visualize electron beam interactions with tungsten targets',
            url: 'demos/electron-target-simulator.html'
        },
        'spectrum-analyzer': {
            title: 'X-ray Spectrum Analyzer',
            description: 'Interactive spectrum analysis and filtration effects',
            url: 'demos/spectrum-analyzer.html'
        },
        'hvl-calculator': {
            title: 'HVL & Filtration Calculator',
            description: 'Calculate half-value layers and optimize filtration',
            url: 'demos/hvl-calculator.html'
        },
        'shielding-designer': {
            title: 'Radiation Shielding Designer',
            description: 'Design protective barriers per NCRP guidelines',
            url: 'demos/shielding-designer.html'
        },
        'production': 'electron-target',
        'interaction': 'spectrum-analyzer',
        'attenuation': 'hvl-calculator',
        'safety': 'shielding-designer'
    };
    
    const demo = demos[demoId] || demos[demos[demoId]];
    if (!demo) return;
    
    showModal({
        title: demo.title,
        content: `
            <div style="padding: 2rem; text-align: center;">
                <div style="font-size: 3rem; color: var(--napkin-primary); margin-bottom: 1rem;">
                    <i class="fas fa-cogs"></i>
                </div>
                <p style="margin-bottom: 2rem; color: #6b7280;">
                    ${demo.description}
                </p>
                <div style="background: #f8fafc; padding: 2rem; border-radius: 10px; margin-bottom: 2rem;">
                    <p style="color: #374151; margin-bottom: 1rem;">
                        <strong>Demo Features:</strong>
                    </p>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 1rem;">
                        <div style="background: white; padding: 1rem; border-radius: 8px;">
                            <i class="fas fa-cube" style="color: var(--napkin-primary); margin-bottom: 0.5rem;"></i>
                            <div>3D Visualization</div>
                        </div>
                        <div style="background: white; padding: 1rem; border-radius: 8px;">
                            <i class="fas fa-sliders-h" style="color: var(--napkin-primary); margin-bottom: 0.5rem;"></i>
                            <div>Interactive Controls</div>
                        </div>
                        <div style="background: white; padding: 1rem; border-radius: 8px;">
                            <i class="fas fa-chart-line" style="color: var(--napkin-primary); margin-bottom: 0.5rem;"></i>
                            <div>Real-time Analysis</div>
                        </div>
                    </div>
                </div>
                <button onclick="launchDemo('${demo.url || '#'}')" style="background: var(--napkin-primary); color: white; border: none; padding: 1rem 2rem; border-radius: 25px; cursor: pointer; font-weight: 500; font-size: 1.1rem;">
                    <i class="fas fa-external-link-alt"></i> Launch Demo
                </button>
            </div>
        `
    });
}

function playAnimation(animationType) {
    const animation = window.physicsModule.animations[animationType];
    if (!animation) return;
    
    const canvas = document.getElementById('animationCanvas');
    canvas.innerHTML = `
        <div style="padding: 2rem; color: white;">
            <h4 style="text-align: center; margin-bottom: 1rem; color: var(--napkin-accent);">
                ${animation.title}
            </h4>
            <p style="text-align: center; margin-bottom: 2rem; opacity: 0.8;">
                ${animation.description}
            </p>
            <div id="animationFrames" style="background: #1f2937; border-radius: 8px; padding: 1.5rem; min-height: 200px;">
                <div style="text-align: center; padding: 2rem;">
                    <div style="width: 60px; height: 60px; border: 3px solid var(--napkin-accent); border-top: 3px solid transparent; border-radius: 50%; margin: 0 auto 1rem; animation: spin 1s linear infinite;"></div>
                    <p>Loading animation...</p>
                </div>
            </div>
            <div style="text-align: center; margin-top: 1rem;">
                <button onclick="restartAnimation('${animationType}')" style="background: var(--napkin-accent); color: white; border: none; padding: 0.5rem 1rem; border-radius: 20px; cursor: pointer;">
                    <i class="fas fa-redo"></i> Restart
                </button>
            </div>
        </div>
    `;
    
    // Simulate animation frames
    setTimeout(() => {
        const framesContainer = document.getElementById('animationFrames');
        let currentFrame = 0;
        
        const showFrame = () => {
            if (currentFrame < animation.frames.length) {
                const frame = animation.frames[currentFrame];
                framesContainer.innerHTML = `
                    <div style="text-align: center; padding: 2rem;">
                        <div style="font-size: 2rem; margin-bottom: 1rem;">
                            Frame ${currentFrame + 1} of ${animation.frames.length}
                        </div>
                        <p style="font-size: 1.1rem; color: var(--napkin-accent);">
                            ${frame.description}
                        </p>
                        <div style="margin-top: 1rem; width: 100%; height: 4px; background: #374151; border-radius: 2px;">
                            <div style="width: ${((currentFrame + 1) / animation.frames.length) * 100}%; height: 100%; background: var(--napkin-accent); border-radius: 2px; transition: width 0.3s ease;"></div>
                        </div>
                    </div>
                `;
                currentFrame++;
                setTimeout(showFrame, 2000);
            } else {
                framesContainer.innerHTML += `
                    <div style="text-align: center; margin-top: 1rem; padding: 1rem; background: var(--napkin-success); border-radius: 8px;">
                        <i class="fas fa-check-circle" style="font-size: 1.5rem; margin-right: 0.5rem;"></i>
                        Animation Complete!
                    </div>
                `;
            }
        };
        
        showFrame();
    }, 1000);
}

function restartAnimation(animationType) {
    playAnimation(animationType);
}

function launchDemo(url) {
    if (url === '#') {
        alert('Demo will be available in the full platform. This is a preview of the interface.');
    } else {
        window.open(url, '_blank');
    }
    closeModal();
}

function showModal({ title, content }) {
    const modal = document.createElement('div');
    modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.8);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10000;
        animation: fadeIn 0.3s ease;
    `;
    
    modal.innerHTML = `
        <div style="background: white; border-radius: 15px; max-width: 600px; width: 90%; max-height: 80%; overflow-y: auto; position: relative;">
            <div style="background: var(--napkin-gradient); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;">
                <h3 style="margin: 0; font-size: 1.3rem;">${title}</h3>
                <button onclick="closeModal()" style="position: absolute; top: 1rem; right: 1rem; background: none; border: none; color: white; font-size: 1.5rem; cursor: pointer;">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div>${content}</div>
        </div>
    `;
    
    document.body.appendChild(modal);
    window.currentModal = modal;
    
    // Close on outside click
    modal.addEventListener('click', (e) => {
        if (e.target === modal) closeModal();
    });
}

function closeModal() {
    if (window.currentModal) {
        window.currentModal.remove();
        window.currentModal = null;
    }
}

// Add CSS for animations
const style = document.createElement('style');
style.textContent = `
    @keyframes particleFloat {
        0% { transform: translateY(0) rotate(0deg); opacity: 0; }
        10% { opacity: 1; }
        90% { opacity: 1; }
        100% { transform: translateY(-400px) rotate(360deg); opacity: 0; }
    }
    
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
    
    @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
    }
`;
document.head.appendChild(style);

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.physicsModule = new PhysicsModule();
});
