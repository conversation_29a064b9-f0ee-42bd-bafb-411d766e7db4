/**
 * Language Toggle Functionality for X-ray System Training Module
 * Supports switching between English and Arabic languages
 */

// Default language is English
let currentLanguage = localStorage.getItem('preferredLanguage') || 'en';

// Language translations
const translations = {
    // Header and Navigation
    'header-title': {
        'en': 'X-ray System Interactive Training Module',
        'ar': 'وحدة تدريب تفاعلية لنظام الأشعة السينية'
    },
    'header-subtitle': {
        'en': 'A comprehensive training platform for Biomedical Engineers',
        'ar': 'منصة تدريب شاملة لمهندسي الطب الحيوي'
    },
    'nav-home': {
        'en': 'Home',
        'ar': 'الرئيسية'
    },
    'nav-about': {
        'en': 'About',
        'ar': 'حول'
    },
    'nav-modules': {
        'en': 'Training Modules',
        'ar': 'وحدات التدريب'
    },
    'nav-resources': {
        'en': 'Resources',
        'ar': 'الموارد'
    },
    'nav-contact': {
        'en': 'Contact',
        'ar': 'اتصل بنا'
    },

    // Hero Section
    'hero-title': {
        'en': 'Master X-ray System Engineering',
        'ar': 'إتقان هندسة نظام الأشعة السينية'
    },
    'hero-description': {
        'en': 'Interactive training modules designed specifically for biomedical engineers to understand, maintain, and troubleshoot X-ray systems.',
        'ar': 'وحدات تدريب تفاعلية مصممة خصيصًا لمهندسي الطب الحيوي لفهم وصيانة وإصلاح أنظمة الأشعة السينية.'
    },
    'explore-modules': {
        'en': 'Explore Modules',
        'ar': 'استكشاف الوحدات'
    },

    // About Section
    'about-title': {
        'en': 'About This Training Platform',
        'ar': 'حول منصة التدريب هذه'
    },
    'about-description': {
        'en': 'This interactive training module is designed to provide biomedical engineers with comprehensive knowledge about X-ray systems, their components, operation principles, and maintenance procedures. Through interactive simulations and detailed diagrams, engineers can gain practical experience without the risks associated with real equipment.',
        'ar': 'تم تصميم وحدة التدريب التفاعلية هذه لتزويد مهندسي الطب الحيوي بمعرفة شاملة حول أنظمة الأشعة السينية ومكوناتها ومبادئ التشغيل وإجراءات الصيانة. من خلال المحاكاة التفاعلية والرسوم البيانية التفصيلية، يمكن للمهندسين اكتساب خبرة عملية دون المخاطر المرتبطة بالمعدات الحقيقية.'
    },

    // Feature Titles
    'feature-components': {
        'en': 'Component Understanding',
        'ar': 'فهم المكونات'
    },
    'feature-integration': {
        'en': 'System Integration',
        'ar': 'تكامل النظام'
    },
    'feature-troubleshooting': {
        'en': 'Troubleshooting Skills',
        'ar': 'مهارات استكشاف الأخطاء وإصلاحها'
    },
    'feature-quality': {
        'en': 'Quality Control',
        'ar': 'مراقبة الجودة'
    },

    // Feature Descriptions
    'feature-components-desc': {
        'en': 'Learn about each component of an X-ray system and how they work together.',
        'ar': 'تعرف على كل مكون من مكونات نظام الأشعة السينية وكيفية عملها معًا.'
    },
    'feature-integration-desc': {
        'en': 'Understand how different components integrate to form a functional X-ray system.',
        'ar': 'فهم كيفية تكامل المكونات المختلفة لتشكيل نظام أشعة سينية وظيفي.'
    },
    'feature-troubleshooting-desc': {
        'en': 'Develop practical skills to identify and resolve common X-ray system issues.',
        'ar': 'تطوير المهارات العملية لتحديد وحل مشكلات نظام الأشعة السينية الشائعة.'
    },
    'feature-quality-desc': {
        'en': 'Learn essential quality control procedures to ensure optimal system performance.',
        'ar': 'تعلم إجراءات مراقبة الجودة الأساسية لضمان الأداء الأمثل للنظام.'
    },

    // Modules Section
    'modules-title': {
        'en': 'Training Modules',
        'ar': 'وحدات التدريب'
    },
    'module-components': {
        'en': 'X-ray System Components',
        'ar': 'مكونات نظام الأشعة السينية'
    },
    'module-block-diagrams': {
        'en': 'Block Diagrams',
        'ar': 'المخططات الكتلية'
    },
    'module-circuit-diagrams': {
        'en': 'Electrical Circuit Diagrams',
        'ar': 'مخططات الدوائر الكهربائية'
    },
    'module-quality-control': {
        'en': 'Quality Control',
        'ar': 'مراقبة الجودة'
    },
    'module-troubleshooting': {
        'en': 'Troubleshooting Guide',
        'ar': 'دليل استكشاف الأخطاء وإصلاحها'
    },
    'module-simulator': {
        'en': 'X-ray Physics Simulator',
        'ar': 'محاكي فيزياء الأشعة السينية'
    },

    // Module Descriptions
    'module-components-desc': {
        'en': 'Explore the fundamental components of an X-ray system including X-ray tube, generator, collimator, and detector systems.',
        'ar': 'استكشف المكونات الأساسية لنظام الأشعة السينية بما في ذلك أنبوب الأشعة السينية والمولد والمجمع وأنظمة الكشف.'
    },
    'module-block-diagrams-desc': {
        'en': 'Understand the system architecture through interactive block diagrams showing signal and power flow.',
        'ar': 'فهم بنية النظام من خلال المخططات الكتلية التفاعلية التي توضح تدفق الإشارة والطاقة.'
    },
    'module-circuit-diagrams-desc': {
        'en': 'Study detailed schematic electrical circuit diagrams of X-ray systems and their subsystems.',
        'ar': 'دراسة مخططات الدوائر الكهربائية التفصيلية لأنظمة الأشعة السينية وأنظمتها الفرعية.'
    },
    'module-quality-control-desc': {
        'en': 'Learn about quality control procedures, exposure parameter monitoring, and output verification.',
        'ar': 'تعرف على إجراءات مراقبة الجودة ومراقبة معلمات التعرض والتحقق من المخرجات.'
    },
    'module-troubleshooting-desc': {
        'en': 'Interactive troubleshooting scenarios for common X-ray system failures and their solutions.',
        'ar': 'سيناريوهات تفاعلية لاستكشاف الأخطاء وإصلاحها للأعطال الشائعة في نظام الأشعة السينية وحلولها.'
    },
    'module-simulator-desc': {
        'en': 'Simulate X-ray generation, interaction with matter, and image formation principles.',
        'ar': 'محاكاة توليد الأشعة السينية والتفاعل مع المادة ومبادئ تكوين الصورة.'
    },
    'start-module': {
        'en': 'Start Module',
        'ar': 'بدء الوحدة'
    },

    // Resources Section
    'resources-title': {
        'en': 'Additional Resources',
        'ar': 'موارد إضافية'
    },
    'resource-documentation': {
        'en': 'Technical Documentation',
        'ar': 'الوثائق التقنية'
    },
    'resource-videos': {
        'en': 'Video Tutorials',
        'ar': 'دروس الفيديو'
    },
    'resource-safety': {
        'en': 'Safety Guidelines',
        'ar': 'إرشادات السلامة'
    },
    'resource-forum': {
        'en': 'Community Forum',
        'ar': 'منتدى المجتمع'
    },
    'view-resources': {
        'en': 'View Resources',
        'ar': 'عرض الموارد'
    },
    'view-tutorials': {
        'en': 'View Tutorials',
        'ar': 'عرض الدروس'
    },
    'download-pdfs': {
        'en': 'Download PDFs',
        'ar': 'تنزيل ملفات PDF'
    },
    'join-forum': {
        'en': 'Join Forum',
        'ar': 'الانضمام إلى المنتدى'
    },

    // Footer
    'footer-title': {
        'en': 'X-ray System Training',
        'ar': 'تدريب نظام الأشعة السينية'
    },
    'footer-description': {
        'en': 'A comprehensive training platform for biomedical engineers specializing in X-ray systems.',
        'ar': 'منصة تدريب شاملة لمهندسي الطب الحيوي المتخصصين في أنظمة الأشعة السينية.'
    },
    'quick-links': {
        'en': 'Quick Links',
        'ar': 'روابط سريعة'
    },
    'contact': {
        'en': 'Contact',
        'ar': 'اتصل بنا'
    },
    'copyright': {
        'en': '© 2025 X-ray System Interactive Training Module. All rights reserved.',
        'ar': '© 2025 وحدة تدريب تفاعلية لنظام الأشعة السينية. جميع الحقوق محفوظة.'
    },
    
    // Language Toggle
    'language-toggle': {
        'en': 'العربية',
        'ar': 'English'
    }
};

/**
 * Initialize language toggle functionality
 */
function initializeLanguageToggle() {
    // Create language toggle button if it doesn't exist
    if (!document.getElementById('language-toggle-btn')) {
        createLanguageToggleButton();
    }
    
    // Apply initial language
    applyLanguage(currentLanguage);
    
    // Add event listener to language toggle button
    document.getElementById('language-toggle-btn').addEventListener('click', function() {
        // Toggle language
        currentLanguage = currentLanguage === 'en' ? 'ar' : 'en';
        
        // Save preference to localStorage
        localStorage.setItem('preferredLanguage', currentLanguage);
        
        // Apply new language
        applyLanguage(currentLanguage);
        
        // Update button text
        this.textContent = translations['language-toggle'][currentLanguage];
    });
}

/**
 * Create language toggle button
 */
function createLanguageToggleButton() {
    const toggleBtn = document.createElement('button');
    toggleBtn.id = 'language-toggle-btn';
    toggleBtn.className = 'language-toggle-btn';
    toggleBtn.textContent = translations['language-toggle'][currentLanguage];
    
    // Create container for the button
    const toggleContainer = document.createElement('div');
    toggleContainer.className = 'language-toggle-container';
    toggleContainer.appendChild(toggleBtn);
    
    // Add to document
    document.body.appendChild(toggleContainer);
}

/**
 * Apply selected language to the page
 * @param {string} lang - Language code ('en' or 'ar')
 */
function applyLanguage(lang) {
    // Set document direction
    document.documentElement.dir = lang === 'ar' ? 'rtl' : 'ltr';
    document.documentElement.lang = lang;
    
    // Add language class to body
    document.body.classList.remove('lang-en', 'lang-ar');
    document.body.classList.add(`lang-${lang}`);
    
    // Update all elements with data-lang attribute
    const elements = document.querySelectorAll('[data-lang]');
    elements.forEach(element => {
        const key = element.getAttribute('data-lang');
        if (translations[key] && translations[key][lang]) {
            element.innerHTML = translations[key][lang];
        }
    });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', initializeLanguageToggle);
