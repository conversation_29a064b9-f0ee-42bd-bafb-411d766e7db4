<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Module 5: AI-Powered Diagnostics - VirtualX Pro</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --napkin-primary: #7c3aed;
            --napkin-secondary: #6d28d9;
            --napkin-accent: #a855f7;
            --napkin-success: #10b981;
            --napkin-warning: #f59e0b;
            --napkin-danger: #ef4444;
            --napkin-dark: #1f2937;
            --napkin-light: #faf5ff;
            --napkin-gradient: linear-gradient(135deg, #7c3aed 0%, #a855f7 100%);
            --napkin-shadow: 0 10px 25px rgba(124, 58, 237, 0.1);
            --napkin-shadow-lg: 0 20px 40px rgba(124, 58, 237, 0.15);
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #faf5ff 0%, #f3e8ff 100%);
            min-height: 100vh;
            line-height: 1.6;
        }

        .training-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }

        .module-header {
            text-align: center;
            margin-bottom: 3rem;
            background: var(--napkin-gradient);
            color: white;
            padding: 3rem 2rem;
            border-radius: 20px;
            box-shadow: var(--napkin-shadow-lg);
            position: relative;
            overflow: hidden;
        }

        .module-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="3" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="80" r="2.5" fill="rgba(255,255,255,0.1)"/><path d="M10,10 Q50,30 90,10" stroke="rgba(255,255,255,0.05)" fill="none"/></svg>');
            animation: float 20s ease-in-out infinite;
        }

        .module-title {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 1rem;
            position: relative;
            z-index: 1;
        }

        .module-subtitle {
            font-size: 1.3rem;
            opacity: 0.9;
            margin-bottom: 2rem;
            position: relative;
            z-index: 1;
        }

        /* AI Neural Network Mind Map */
        .ai-mindmap {
            position: relative;
            background: white;
            border-radius: 20px;
            padding: 4rem;
            margin: 2rem 0;
            box-shadow: var(--napkin-shadow);
            overflow: hidden;
            min-height: 700px;
        }

        .ai-core {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 220px;
            height: 220px;
            background: var(--napkin-gradient);
            border-radius: 50%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 1.1rem;
            text-align: center;
            box-shadow: var(--napkin-shadow-lg);
            cursor: pointer;
            transition: all 0.3s ease;
            z-index: 10;
            animation: pulse 3s ease-in-out infinite;
        }

        .ai-core:hover {
            transform: translate(-50%, -50%) scale(1.1);
            box-shadow: 0 25px 50px rgba(124, 58, 237, 0.3);
        }

        .ai-icon {
            font-size: 3.5rem;
            margin-bottom: 0.5rem;
        }

        .neural-node {
            position: absolute;
            width: 160px;
            height: 160px;
            background: white;
            border: 3px solid var(--napkin-primary);
            border-radius: 50%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: var(--napkin-shadow);
        }

        .neural-node:hover {
            transform: scale(1.1);
            background: var(--napkin-primary);
            color: white;
            box-shadow: var(--napkin-shadow-lg);
        }

        .neural-icon {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
            color: var(--napkin-primary);
        }

        .neural-node:hover .neural-icon {
            color: white;
        }

        /* Neural network positioning */
        .input-layer { top: 15%; left: 10%; }
        .hidden-layer1 { top: 15%; right: 10%; }
        .hidden-layer2 { bottom: 15%; left: 10%; }
        .output-layer { bottom: 15%; right: 10%; }
        .training-node { top: 50%; left: 5%; transform: translateY(-50%); }
        .validation-node { top: 50%; right: 5%; transform: translateY(-50%); }

        /* Neural connection lines */
        .neural-line {
            position: absolute;
            background: var(--napkin-primary);
            height: 2px;
            transform-origin: left center;
            opacity: 0.4;
            z-index: 1;
            border-radius: 1px;
            animation: dataFlow 3s ease-in-out infinite;
        }

        .line-input { top: 30%; left: 25%; width: 200px; transform: rotate(10deg); }
        .line-hidden1 { top: 30%; right: 25%; width: 200px; transform: rotate(-10deg); transform-origin: right center; }
        .line-hidden2 { bottom: 30%; left: 25%; width: 200px; transform: rotate(-10deg); }
        .line-output { bottom: 30%; right: 25%; width: 200px; transform: rotate(10deg); transform-origin: right center; }
        .line-training { top: 50%; left: 20%; width: 150px; }
        .line-validation { top: 50%; right: 20%; width: 150px; transform-origin: right center; }

        /* Case Database Viewer */
        .case-database {
            background: var(--napkin-dark);
            border-radius: 20px;
            padding: 2rem;
            margin: 3rem 0;
            color: white;
            position: relative;
            overflow: hidden;
        }

        .database-header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .database-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            padding: 1.5rem;
            border-radius: 12px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            color: var(--napkin-accent);
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: rgba(255, 255, 255, 0.8);
            font-size: 0.9rem;
        }

        .case-viewer {
            background: #111827;
            border-radius: 15px;
            padding: 1.5rem;
            margin-top: 2rem;
            border: 2px solid #374151;
        }

        .case-controls {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin-bottom: 1.5rem;
            flex-wrap: wrap;
        }

        .case-btn {
            background: var(--napkin-primary);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 25px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .case-btn:hover {
            background: var(--napkin-accent);
            transform: translateY(-2px);
        }

        .case-btn.active {
            background: var(--napkin-accent);
            box-shadow: 0 0 20px rgba(168, 85, 247, 0.5);
        }

        /* AI Model Training */
        .model-training {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            margin: 3rem 0;
            box-shadow: var(--napkin-shadow);
        }

        .training-progress {
            background: var(--napkin-light);
            padding: 2rem;
            border-radius: 15px;
            margin-bottom: 2rem;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e5e7eb;
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 1rem;
        }

        .progress-fill {
            height: 100%;
            background: var(--napkin-gradient);
            border-radius: 10px;
            transition: width 0.3s ease;
            position: relative;
        }

        .progress-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            animation: shimmer 2s infinite;
        }

        .training-metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
        }

        .metric-item {
            text-align: center;
            padding: 1rem;
            background: white;
            border-radius: 8px;
            border-left: 4px solid var(--napkin-primary);
        }

        .metric-value {
            font-size: 1.5rem;
            font-weight: bold;
            color: var(--napkin-primary);
        }

        .metric-label {
            font-size: 0.9rem;
            color: #6b7280;
            margin-top: 0.25rem;
        }

        /* Diagnostic Results */
        .diagnostic-results {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            margin: 3rem 0;
            box-shadow: var(--napkin-shadow);
        }

        .results-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
        }

        .result-card {
            background: var(--napkin-light);
            padding: 1.5rem;
            border-radius: 12px;
            border-left: 4px solid var(--napkin-primary);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .result-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--napkin-shadow);
        }

        .result-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .result-title {
            font-weight: 600;
            color: var(--napkin-dark);
        }

        .confidence-badge {
            background: var(--napkin-success);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .confidence-high { background: var(--napkin-success); }
        .confidence-medium { background: var(--napkin-warning); }
        .confidence-low { background: var(--napkin-danger); }

        .result-description {
            color: #6b7280;
            margin-bottom: 1rem;
            font-size: 0.9rem;
        }

        .result-actions {
            display: flex;
            gap: 0.5rem;
        }

        .action-btn {
            background: var(--napkin-primary);
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            cursor: pointer;
            font-size: 0.8rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .action-btn:hover {
            background: var(--napkin-secondary);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .module-title {
                font-size: 2rem;
            }
            
            .ai-mindmap {
                padding: 2rem;
                min-height: 500px;
            }
            
            .ai-core {
                width: 160px;
                height: 160px;
                font-size: 0.9rem;
            }
            
            .neural-node {
                width: 100px;
                height: 100px;
                font-size: 0.8rem;
            }
            
            .database-stats {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        /* Animations */
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-15px); }
        }

        @keyframes pulse {
            0%, 100% { transform: translate(-50%, -50%) scale(1); }
            50% { transform: translate(-50%, -50%) scale(1.05); }
        }

        @keyframes dataFlow {
            0% { opacity: 0.4; }
            50% { opacity: 1; }
            100% { opacity: 0.4; }
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
</head>
<body>
    <div class="training-container">
        <!-- Module Header -->
        <div class="module-header animate__animated animate__fadeInDown">
            <h1 class="module-title">
                <i class="fas fa-brain"></i>
                AI-Powered Diagnostics
            </h1>
            <p class="module-subtitle">Machine Learning for Intelligent Fault Detection</p>
            <p>Master AI diagnostic algorithms and machine learning techniques for intelligent fault detection and predictive maintenance</p>
            
            <div class="module-stats" style="display: flex; justify-content: center; gap: 3rem; margin-top: 2rem;">
                <div class="stat-item">
                    <span style="font-size: 2.5rem; font-weight: bold; display: block;">10</span>
                    <span style="font-size: 0.9rem; opacity: 0.8;">Hours</span>
                </div>
                <div class="stat-item">
                    <span style="font-size: 2.5rem; font-weight: bold; display: block;">10K+</span>
                    <span style="font-size: 0.9rem; opacity: 0.8;">Cases</span>
                </div>
                <div class="stat-item">
                    <span style="font-size: 2.5rem; font-weight: bold; display: block;">95.2%</span>
                    <span style="font-size: 0.9rem; opacity: 0.8;">Accuracy</span>
                </div>
                <div class="stat-item">
                    <span style="font-size: 2.5rem; font-weight: bold; display: block;">&lt;2s</span>
                    <span style="font-size: 0.9rem; opacity: 0.8;">Response</span>
                </div>
            </div>
        </div>

        <!-- AI Neural Network Mind Map -->
        <div class="ai-mindmap">
            <h2 style="text-align: center; margin-bottom: 3rem; color: var(--napkin-dark); font-size: 1.8rem;">
                Neural Network Architecture Mind Map
            </h2>
            
            <!-- Central AI Core -->
            <div class="ai-core" onclick="showAIOverview()">
                <div class="ai-icon">
                    <i class="fas fa-brain"></i>
                </div>
                <div>AI Diagnostic<br>Engine</div>
            </div>
            
            <!-- Neural Network Nodes -->
            <div class="neural-node input-layer" onclick="showLayer('input')">
                <div class="neural-icon">
                    <i class="fas fa-sign-in-alt"></i>
                </div>
                <div>Input Layer<br>Data Processing</div>
            </div>
            
            <div class="neural-node hidden-layer1" onclick="showLayer('hidden1')">
                <div class="neural-icon">
                    <i class="fas fa-layer-group"></i>
                </div>
                <div>Hidden Layer 1<br>Feature Extraction</div>
            </div>
            
            <div class="neural-node hidden-layer2" onclick="showLayer('hidden2')">
                <div class="neural-icon">
                    <i class="fas fa-layer-group"></i>
                </div>
                <div>Hidden Layer 2<br>Pattern Recognition</div>
            </div>
            
            <div class="neural-node output-layer" onclick="showLayer('output')">
                <div class="neural-icon">
                    <i class="fas fa-sign-out-alt"></i>
                </div>
                <div>Output Layer<br>Classification</div>
            </div>
            
            <div class="neural-node training-node" onclick="showLayer('training')">
                <div class="neural-icon">
                    <i class="fas fa-graduation-cap"></i>
                </div>
                <div>Training<br>Algorithm</div>
            </div>
            
            <div class="neural-node validation-node" onclick="showLayer('validation')">
                <div class="neural-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div>Validation<br>Testing</div>
            </div>
            
            <!-- Neural Connection Lines -->
            <div class="neural-line line-input"></div>
            <div class="neural-line line-hidden1"></div>
            <div class="neural-line line-hidden2"></div>
            <div class="neural-line line-output"></div>
            <div class="neural-line line-training"></div>
            <div class="neural-line line-validation"></div>
        </div>

        <!-- Case Database Viewer -->
        <div class="case-database">
            <div class="database-header">
                <h3 style="font-size: 1.8rem; margin-bottom: 1rem;">
                    <i class="fas fa-database"></i>
                    10,000+ Real Service Case Database
                </h3>
                <p style="opacity: 0.8;">Comprehensive database of real-world service cases with validated outcomes</p>
            </div>
            
            <div class="database-stats">
                <div class="stat-card">
                    <div class="stat-number" id="totalCases">10,247</div>
                    <div class="stat-label">Total Cases</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-number" id="resolvedCases">9,756</div>
                    <div class="stat-label">Resolved Cases</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-number" id="accuracyRate">95.2%</div>
                    <div class="stat-label">Accuracy Rate</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-number" id="avgResponseTime">1.8s</div>
                    <div class="stat-label">Avg Response Time</div>
                </div>
            </div>
            
            <div class="case-viewer">
                <div class="case-controls">
                    <button type="button" class="case-btn active" onclick="loadCaseCategory('generator')">
                        <i class="fas fa-bolt"></i> Generator Issues
                    </button>
                    <button type="button" class="case-btn" onclick="loadCaseCategory('detector')">
                        <i class="fas fa-tv"></i> Detector Problems
                    </button>
                    <button type="button" class="case-btn" onclick="loadCaseCategory('tube')">
                        <i class="fas fa-radiation"></i> X-ray Tube Faults
                    </button>
                    <button type="button" class="case-btn" onclick="loadCaseCategory('control')">
                        <i class="fas fa-cogs"></i> Control System
                    </button>
                    <button type="button" class="case-btn" onclick="loadCaseCategory('mechanical')">
                        <i class="fas fa-tools"></i> Mechanical Issues
                    </button>
                </div>
                
                <div id="caseDisplay" style="color: white; padding: 1rem; background: rgba(0,0,0,0.3); border-radius: 8px;">
                    <div style="text-align: center; padding: 2rem;">
                        <i class="fas fa-database" style="font-size: 3rem; margin-bottom: 1rem; color: var(--napkin-accent);"></i>
                        <p>Select a category to explore real service cases</p>
                        <p style="font-size: 0.9rem; opacity: 0.7; margin-top: 0.5rem;">AI learns from these validated cases to improve diagnostic accuracy</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- AI Model Training -->
        <div class="model-training">
            <h3 style="text-align: center; margin-bottom: 2rem; color: var(--napkin-dark); font-size: 1.8rem;">
                <i class="fas fa-graduation-cap"></i>
                Interactive Model Training Laboratory
            </h3>
            
            <div class="training-progress">
                <h4 style="color: var(--napkin-primary); margin-bottom: 1rem;">Current Training Session</h4>
                <div class="progress-bar">
                    <div class="progress-fill" id="trainingProgress" style="width: 73%;"></div>
                </div>
                <p style="text-align: center; margin-bottom: 1.5rem; color: #6b7280;">
                    Training Progress: <span id="progressPercent">73%</span> - Epoch 147/200
                </p>
                
                <div class="training-metrics">
                    <div class="metric-item">
                        <div class="metric-value" id="trainingAccuracy">94.8%</div>
                        <div class="metric-label">Training Accuracy</div>
                    </div>
                    
                    <div class="metric-item">
                        <div class="metric-value" id="validationAccuracy">92.3%</div>
                        <div class="metric-label">Validation Accuracy</div>
                    </div>
                    
                    <div class="metric-item">
                        <div class="metric-value" id="trainingLoss">0.142</div>
                        <div class="metric-label">Training Loss</div>
                    </div>
                    
                    <div class="metric-item">
                        <div class="metric-value" id="validationLoss">0.198</div>
                        <div class="metric-label">Validation Loss</div>
                    </div>
                    
                    <div class="metric-item">
                        <div class="metric-value" id="learningRate">0.001</div>
                        <div class="metric-label">Learning Rate</div>
                    </div>
                    
                    <div class="metric-item">
                        <div class="metric-value" id="batchSize">32</div>
                        <div class="metric-label">Batch Size</div>
                    </div>
                </div>
            </div>
            
            <div style="text-align: center;">
                <button type="button" onclick="startTraining()" style="background: var(--napkin-primary); color: white; border: none; padding: 1rem 2rem; border-radius: 25px; cursor: pointer; font-weight: 500; margin-right: 1rem;">
                    <i class="fas fa-play"></i> Start New Training
                </button>
                <button type="button" onclick="pauseTraining()" style="background: var(--napkin-warning); color: white; border: none; padding: 1rem 2rem; border-radius: 25px; cursor: pointer; font-weight: 500; margin-right: 1rem;">
                    <i class="fas fa-pause"></i> Pause Training
                </button>
                <button type="button" onclick="exportModel()" style="background: var(--napkin-success); color: white; border: none; padding: 1rem 2rem; border-radius: 25px; cursor: pointer; font-weight: 500;">
                    <i class="fas fa-download"></i> Export Model
                </button>
            </div>
        </div>

        <!-- Diagnostic Results -->
        <div class="diagnostic-results">
            <h3 style="text-align: center; margin-bottom: 2rem; color: var(--napkin-dark); font-size: 1.8rem;">
                <i class="fas fa-search"></i>
                Real-time Diagnostic Results
            </h3>
            
            <div class="results-grid">
                <div class="result-card" onclick="showDiagnosticDetail('generator-fault')">
                    <div class="result-header">
                        <div class="result-title">Generator Voltage Instability</div>
                        <div class="confidence-badge confidence-high">96.7% Confidence</div>
                    </div>
                    <div class="result-description">
                        IGBT switching circuit showing irregular voltage patterns. Likely cause: faulty gate driver circuit or degraded IGBT module.
                    </div>
                    <div class="result-actions">
                        <button type="button" class="action-btn" onclick="viewSolution('generator-fault')">View Solution</button>
                        <button type="button" class="action-btn" onclick="scheduleRepair('generator-fault')">Schedule Repair</button>
                    </div>
                </div>
                
                <div class="result-card" onclick="showDiagnosticDetail('detector-noise')">
                    <div class="result-header">
                        <div class="result-title">Detector Noise Increase</div>
                        <div class="confidence-badge confidence-medium">84.2% Confidence</div>
                    </div>
                    <div class="result-description">
                        Flat panel detector showing increased noise levels in specific regions. Possible TFT array degradation or readout electronics issue.
                    </div>
                    <div class="result-actions">
                        <button type="button" class="action-btn" onclick="viewSolution('detector-noise')">View Solution</button>
                        <button type="button" class="action-btn" onclick="runCalibration('detector-noise')">Run Calibration</button>
                    </div>
                </div>
                
                <div class="result-card" onclick="showDiagnosticDetail('tube-aging')">
                    <div class="result-header">
                        <div class="result-title">X-ray Tube Aging Pattern</div>
                        <div class="confidence-badge confidence-high">91.5% Confidence</div>
                    </div>
                    <div class="result-description">
                        Anode wear pattern indicates 78% of expected tube life consumed. Recommend proactive replacement planning within 6 months.
                    </div>
                    <div class="result-actions">
                        <button type="button" class="action-btn" onclick="viewSolution('tube-aging')">View Analysis</button>
                        <button type="button" class="action-btn" onclick="planReplacement('tube-aging')">Plan Replacement</button>
                    </div>
                </div>
                
                <div class="result-card" onclick="showDiagnosticDetail('cooling-efficiency')">
                    <div class="result-header">
                        <div class="result-title">Cooling System Efficiency</div>
                        <div class="confidence-badge confidence-low">72.8% Confidence</div>
                    </div>
                    <div class="result-description">
                        Thermal patterns suggest reduced cooling efficiency. May indicate pump degradation or coolant circulation issues.
                    </div>
                    <div class="result-actions">
                        <button type="button" class="action-btn" onclick="viewSolution('cooling-efficiency')">View Details</button>
                        <button type="button" class="action-btn" onclick="runDiagnostic('cooling-efficiency')">Run Diagnostic</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="js/module-5-ai-diagnostics.js"></script>
</body>
</html>
