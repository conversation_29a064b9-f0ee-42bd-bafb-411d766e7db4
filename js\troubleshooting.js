/**
 * JavaScript for X-ray System Troubleshooting Guide Module
 */

document.addEventListener('DOMContentLoaded', function() {
    // Navigation active state management
    const navLinks = document.querySelectorAll('nav ul li a');
    const sections = document.querySelectorAll('section[id]');
    
    // Smooth scrolling for navigation links
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            // Only prevent default if it's an anchor link
            if (this.getAttribute('href').startsWith('#')) {
                e.preventDefault();
                
                const targetId = this.getAttribute('href');
                const targetSection = document.querySelector(targetId);
                
                if (targetSection) {
                    window.scrollTo({
                        top: targetSection.offsetTop - 60, // Adjust for nav height
                        behavior: 'smooth'
                    });
                    
                    // Update active link
                    navLinks.forEach(link => link.classList.remove('active'));
                    this.classList.add('active');
                }
            }
        });
    });
    
    // Update active navigation link on scroll
    window.addEventListener('scroll', function() {
        let current = '';
        
        sections.forEach(section => {
            const sectionTop = section.offsetTop;
            const sectionHeight = section.clientHeight;
            
            if (pageYOffset >= sectionTop - 100) {
                current = section.getAttribute('id');
            }
        });
        
        navLinks.forEach(link => {
            link.classList.remove('active');
            const href = link.getAttribute('href');
            if (href && href === `#${current}`) {
                link.classList.add('active');
            }
        });
    });
    
    // Initialize expandable issue cards
    initializeIssueCards();
    
    // Initialize troubleshooting simulator
    initializeTroubleshootingSimulator();
});

/**
 * Initialize expandable issue cards
 */
function initializeIssueCards() {
    const issueHeaders = document.querySelectorAll('.issue-header');
    
    issueHeaders.forEach(header => {
        header.addEventListener('click', function() {
            const content = this.nextElementSibling;
            const expandBtn = this.querySelector('.expand-btn');
            
            // Toggle active class on content
            content.classList.toggle('active');
            
            // Toggle active class on expand button
            expandBtn.classList.toggle('active');
        });
    });
}

/**
 * Initialize troubleshooting simulator
 */
function initializeTroubleshootingSimulator() {
    const scenarioButtons = document.querySelectorAll('.scenario-btn');
    const actionButtons = document.querySelectorAll('.action-btn');
    const simulatorDisplay = document.querySelector('.simulator-display');
    const logContent = document.getElementById('log-content');
    
    // Scenario data
    const scenarios = {
        'no-power': {
            title: 'System Does Not Power On',
            description: 'A radiography room reports that their X-ray system will not power on. The technologists attempted to use the system this morning but nothing happens when they press the power button.',
            info: 'The system is a 5-year-old digital radiography unit that was working normally yesterday. No recent maintenance has been performed. The facility has not reported any power outages.',
            logs: 'No error logs available since the system cannot power on.',
            visual: 'Visual inspection shows no visible damage to the system. The power cable is properly connected. The circuit breaker panel shows that one breaker labeled "X-ray Room 2" is in the tripped position.',
            tests: 'Testing the outlet with a voltage meter shows proper facility voltage. After resetting the circuit breaker, the system powers on normally.',
            solution: 'The issue was a tripped circuit breaker. After resetting the breaker, the system functions normally. Recommend monitoring to see if the breaker trips again, which would indicate a possible overload or short circuit that requires further investigation.',
            root_cause: 'Tripped circuit breaker'
        },
        'no-radiation': {
            title: 'No Radiation Output',
            description: 'A technologist reports that the X-ray system powers on and appears to function normally, but no images are being produced. The exposure indicator light illuminates during exposure, but the resulting image is completely black.',
            info: 'The system is a digital radiography unit that has been in service for 3 years. It was working normally until today. The technologist reports hearing the normal exposure sound but no image appears.',
            logs: 'Error log shows: "E-042: Exposure terminated - No tube current detected"',
            visual: 'Visual inspection shows no visible damage to the tube housing or cables. All connections appear secure. The tube housing feels unusually hot to the touch.',
            tests: 'Testing with a radiation meter confirms no radiation is being produced during exposure. Checking the filament current shows normal values during preparation, but drops to zero during exposure attempt. Thermal protection indicator is illuminated on the control panel.',
            solution: 'The issue was thermal protection circuit activation due to tube overheating. After allowing the tube to cool for 30 minutes, the thermal protection reset automatically and the system began functioning normally. Recommend checking the cooling system and ensuring proper warm-up procedures are followed.',
            root_cause: 'Thermal protection circuit activation'
        },
        'image-artifacts': {
            title: 'Image Artifacts',
            description: 'A radiologist reports consistent white lines appearing on all images from one specific X-ray room. The lines appear in the same location regardless of patient positioning or technique factors.',
            info: 'The system is a digital radiography unit with a flat panel detector. The artifacts began appearing yesterday and are present on all images. The lines are horizontal and appear in the upper third of all images.',
            logs: 'Error log shows: "W-103: Detector calibration recommended"',
            visual: 'Visual inspection of the detector shows no visible damage. The grid appears properly seated. No foreign objects are visible in the beam path.',
            tests: 'Test exposures with the grid removed still show the artifacts. A flat field calibration image shows several detector elements not responding properly. Running the detector diagnostic test confirms multiple dead detector lines.',
            solution: 'The issue was damaged detector elements. A detector calibration was performed to compensate for the dead lines, which improved but did not completely eliminate the artifacts. Recommend scheduling detector replacement as the calibration is only a temporary solution.',
            root_cause: 'Damaged detector elements'
        },
        'exposure-error': {
            title: 'Exposure Errors',
            description: 'A technologist reports that exposures frequently terminate prematurely with an error message. This is happening on approximately 30% of exposures, requiring repeated examinations.',
            info: 'The system is a radiography unit that has been in service for 7 years. The issue began about a week ago and has been getting progressively worse. The error occurs more frequently with higher mA technique settings.',
            logs: 'Error log shows: "E-056: Exposure terminated - mA fluctuation detected"',
            visual: 'Visual inspection shows no obvious issues. All cables are properly connected. There is some corrosion visible on the high voltage cable connections at the tube housing.',
            tests: 'Testing with an oscilloscope shows unstable mA waveform during exposure. When the high voltage cables are wiggled during testing, the waveform becomes even more erratic. Resistance testing of the high voltage cables shows higher than normal values.',
            solution: 'The issue was corroded high voltage cable connections. The connections were cleaned and reseated, which resolved the issue. Recommend replacing the high voltage cables during the next scheduled maintenance as corrosion will likely return.',
            root_cause: 'Corroded high voltage cable connections'
        }
    };
    
    let currentScenario = null;
    let completedActions = [];
    
    // Initialize scenario buttons
    scenarioButtons.forEach(button => {
        button.addEventListener('click', function() {
            const scenarioId = this.getAttribute('data-scenario');
            
            // Reset active state on all scenario buttons
            scenarioButtons.forEach(btn => btn.classList.remove('active'));
            
            // Set active state on clicked button
            this.classList.add('active');
            
            // Set current scenario
            currentScenario = scenarios[scenarioId];
            
            // Reset simulator
            resetSimulator();
            
            // Update simulator display
            updateSimulatorDisplay(currentScenario.description);
            
            // Enable action buttons
            actionButtons.forEach(btn => btn.disabled = false);
            
            // Reset log
            logContent.innerHTML = '<p>New troubleshooting session started.</p>';
            addToLog(`Problem: ${currentScenario.title}`);
            addToLog(`Description: ${currentScenario.description}`);
        });
    });
    
    // Initialize action buttons
    document.getElementById('gather-info-btn').addEventListener('click', function() {
        if (!currentScenario || completedActions.includes('info')) return;
        
        updateSimulatorDisplay(currentScenario.info);
        addToLog('Action: Gathered information from staff');
        addToLog(`Information: ${currentScenario.info}`);
        completedActions.push('info');
        checkProgress();
    });
    
    document.getElementById('check-logs-btn').addEventListener('click', function() {
        if (!currentScenario || completedActions.includes('logs')) return;
        
        updateSimulatorDisplay(currentScenario.logs);
        addToLog('Action: Checked system error logs');
        addToLog(`Log findings: ${currentScenario.logs}`);
        completedActions.push('logs');
        checkProgress();
    });
    
    document.getElementById('visual-inspect-btn').addEventListener('click', function() {
        if (!currentScenario || completedActions.includes('visual')) return;
        
        updateSimulatorDisplay(currentScenario.visual);
        addToLog('Action: Performed visual inspection');
        addToLog(`Visual findings: ${currentScenario.visual}`);
        completedActions.push('visual');
        checkProgress();
    });
    
    document.getElementById('test-components-btn').addEventListener('click', function() {
        if (!currentScenario || completedActions.includes('tests')) return;
        
        updateSimulatorDisplay(currentScenario.tests);
        addToLog('Action: Tested components');
        addToLog(`Test results: ${currentScenario.tests}`);
        completedActions.push('tests');
        checkProgress();
    });
    
    document.getElementById('apply-solution-btn').addEventListener('click', function() {
        if (!currentScenario || completedActions.includes('solution')) return;
        
        updateSimulatorDisplay(currentScenario.solution);
        addToLog('Action: Applied solution');
        addToLog(`Solution: ${currentScenario.solution}`);
        completedActions.push('solution');
        
        // Show success message
        setTimeout(() => {
            updateSimulatorDisplay(`
                <div class="success-message">
                    <h3>Troubleshooting Successful!</h3>
                    <p><strong>Root Cause:</strong> ${currentScenario.root_cause}</p>
                    <p><strong>Solution:</strong> ${currentScenario.solution}</p>
                    <p>Select another scenario to continue practicing.</p>
                </div>
            `);
            addToLog('Troubleshooting completed successfully!');
        }, 2000);
    });
    
    /**
     * Update the simulator display with new content
     * @param {string} content - The content to display
     */
    function updateSimulatorDisplay(content) {
        simulatorDisplay.innerHTML = `<div class="scenario-content">${content}</div>`;
    }
    
    /**
     * Add a message to the log
     * @param {string} message - The message to add
     */
    function addToLog(message) {
        const timestamp = new Date().toLocaleTimeString();
        const logEntry = document.createElement('p');
        logEntry.innerHTML = `<span class="log-time">[${timestamp}]</span> ${message}`;
        logContent.appendChild(logEntry);
        
        // Scroll to bottom
        logContent.scrollTop = logContent.scrollHeight;
    }
    
    /**
     * Check progress and provide hints if needed
     */
    function checkProgress() {
        if (completedActions.length >= 4 && !completedActions.includes('solution')) {
            addToLog('Hint: You have gathered enough information to apply a solution.');
        }
    }
    
    /**
     * Reset the simulator to initial state
     */
    function resetSimulator() {
        completedActions = [];
    }
}
