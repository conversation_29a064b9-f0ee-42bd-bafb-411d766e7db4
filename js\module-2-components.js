/**
 * VirtualX Pro - Module 2: System Components & Architecture
 * Interactive 3D component exploration and assembly simulation
 */

class ComponentsModule {
    constructor() {
        this.currentModel = 'ge-discovery';
        this.isExplodedView = false;
        this.isXrayView = false;
        this.currentVendor = 'ge';
        this.assemblyProgress = 0;
        this.components = {
            tube: {
                title: 'X-ray Tube Assembly',
                description: 'Heart of the X-ray system - generates X-ray photons',
                specifications: {
                    'Anode Material': 'Tungsten with Rhenium',
                    'Focal Spot Size': '0.6mm / 1.2mm',
                    'Anode Angle': '12-16 degrees',
                    'Heat Capacity': '300-400 kHU',
                    'Cooling': 'Oil circulation + fan'
                },
                components: [
                    'Rotating anode disc',
                    'Cathode filament',
                    'Glass envelope',
                    'Housing assembly',
                    'Cooling system'
                ]
            },
            generator: {
                title: 'High-Frequency Generator',
                description: 'Converts AC power to high-voltage DC for X-ray production',
                specifications: {
                    'Type': 'IGBT High-Frequency',
                    'Frequency': '50-100 kHz',
                    'Power Rating': '32-100 kW',
                    'Voltage Ripple': '<1%',
                    'Efficiency': '>95%'
                },
                components: [
                    'IGBT switching circuits',
                    'High-voltage transformer',
                    'Rectifier circuits',
                    'Control electronics',
                    'Feedback systems'
                ]
            },
            detector: {
                title: 'Digital Detector System',
                description: 'Converts X-ray photons to digital image data',
                specifications: {
                    'Technology': 'a-Si/CsI Flat Panel',
                    'Active Area': '43 x 43 cm',
                    'Pixel Matrix': '3072 x 3072',
                    'Pixel Size': '140 μm',
                    'DQE': '>60% @ 1 lp/mm'
                },
                components: [
                    'Scintillator layer (CsI)',
                    'Photodiode array (a-Si)',
                    'TFT switching matrix',
                    'Readout electronics',
                    'Image processing unit'
                ]
            },
            collimator: {
                title: 'Beam Collimation System',
                description: 'Controls X-ray beam size and shape',
                specifications: {
                    'Material': 'Lead shutters',
                    'Field Size': '5x5 to 43x43 cm',
                    'Light Field': 'LED illumination',
                    'Accuracy': '±1% of SID',
                    'Filtration': 'Al equivalent'
                },
                components: [
                    'Lead shutters (4-blade)',
                    'Light field lamp',
                    'Mirror assembly',
                    'Filtration wheels',
                    'Position sensors'
                ]
            },
            control: {
                title: 'Control System',
                description: 'Manages system operation and user interface',
                specifications: {
                    'Processor': 'Multi-core ARM/x86',
                    'Memory': '8-16 GB RAM',
                    'Storage': '500GB-2TB SSD',
                    'Interface': 'Touch screen GUI',
                    'Network': 'DICOM/HL7 compliant'
                },
                components: [
                    'Main control computer',
                    'Touch screen interface',
                    'Safety interlocks',
                    'Network interface',
                    'Backup systems'
                ]
            },
            table: {
                title: 'Patient Positioning Table',
                description: 'Supports and positions patient for imaging',
                specifications: {
                    'Weight Capacity': '300-500 kg',
                    'Height Range': '60-120 cm',
                    'Tabletop Material': 'Carbon fiber',
                    'Movement': '4-way float',
                    'Accuracy': '±1 mm'
                },
                components: [
                    'Carbon fiber tabletop',
                    'Motorized base',
                    'Position encoders',
                    'Safety systems',
                    'Accessories mount'
                ]
            }
        };
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.initializeModel();
        this.startConnectionAnimations();
    }

    setupEventListeners() {
        // Component node interactions
        document.querySelectorAll('.component-node').forEach(node => {
            node.addEventListener('mouseenter', this.highlightComponent.bind(this));
            node.addEventListener('mouseleave', this.resetHighlight.bind(this));
        });

        // Viewer button interactions
        document.querySelectorAll('.viewer-btn').forEach(btn => {
            btn.addEventListener('click', this.updateActiveButton.bind(this));
        });
    }

    highlightComponent(event) {
        const node = event.target.closest('.component-node');
        const connections = document.querySelectorAll('.connection-line');
        
        // Highlight all connections
        connections.forEach(line => {
            line.style.background = 'var(--napkin-accent)';
            line.style.height = '4px';
            line.style.opacity = '1';
            line.style.boxShadow = '0 0 15px var(--napkin-accent)';
        });
        
        // Add glow effect to node
        node.style.boxShadow = '0 0 30px var(--napkin-accent)';
    }

    resetHighlight() {
        const connections = document.querySelectorAll('.connection-line');
        const nodes = document.querySelectorAll('.component-node');
        
        connections.forEach(line => {
            line.style.background = 'var(--napkin-primary)';
            line.style.height = '3px';
            line.style.opacity = '0.6';
            line.style.boxShadow = 'none';
        });
        
        nodes.forEach(node => {
            node.style.boxShadow = 'var(--napkin-shadow)';
        });
    }

    updateActiveButton(event) {
        const clickedBtn = event.target.closest('.viewer-btn');
        if (!clickedBtn) return;
        
        // Update active state for model buttons only
        if (clickedBtn.textContent.includes('GE') || 
            clickedBtn.textContent.includes('Philips') || 
            clickedBtn.textContent.includes('Siemens')) {
            document.querySelectorAll('.viewer-btn').forEach(btn => {
                if (btn.textContent.includes('GE') || 
                    btn.textContent.includes('Philips') || 
                    btn.textContent.includes('Siemens')) {
                    btn.classList.remove('active');
                }
            });
            clickedBtn.classList.add('active');
        }
    }

    initializeModel() {
        // Simulate 3D model loading
        setTimeout(() => {
            const viewport = document.getElementById('modelViewport');
            viewport.innerHTML = `
                <div class="viewport-overlay">
                    <div>Model: GE Discovery XR656</div>
                    <div>Components: 6 visible</div>
                    <div>View: Standard</div>
                    <div>Zoom: 100%</div>
                </div>
                <div style="display: flex; align-items: center; justify-content: center; height: 100%; color: white;">
                    <div style="text-align: center;">
                        <div style="width: 300px; height: 200px; background: linear-gradient(45deg, #374151, #4b5563); border-radius: 10px; margin: 0 auto 1rem; display: flex; align-items: center; justify-content: center; font-size: 2rem;">
                            <i class="fas fa-hospital"></i>
                        </div>
                        <p>3D Model Loaded Successfully</p>
                        <p style="font-size: 0.9rem; opacity: 0.7;">Interactive controls available</p>
                    </div>
                </div>
            `;
        }, 2000);
    }

    startConnectionAnimations() {
        const lines = document.querySelectorAll('.connection-line');
        lines.forEach((line, index) => {
            setTimeout(() => {
                line.style.animation = 'pulse 3s ease-in-out infinite';
                line.style.animationDelay = `${index * 0.5}s`;
            }, 1000);
        });
    }
}

// Global functions for HTML onclick handlers
function showSystemOverview() {
    showModal({
        title: 'X-ray System Architecture Overview',
        content: `
            <div style="padding: 2rem;">
                <div style="text-align: center; margin-bottom: 2rem;">
                    <div style="font-size: 4rem; color: var(--napkin-primary); margin-bottom: 1rem;">
                        <i class="fas fa-microchip"></i>
                    </div>
                    <h3 style="margin-bottom: 1rem;">Complete System Integration</h3>
                    <p style="color: #6b7280; margin-bottom: 2rem;">
                        Understanding how all components work together to create high-quality medical images
                    </p>
                </div>
                
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1.5rem;">
                    <div style="background: #f8fafc; padding: 1.5rem; border-radius: 10px; border-left: 4px solid var(--napkin-primary);">
                        <h4 style="color: var(--napkin-primary); margin-bottom: 1rem;">
                            <i class="fas fa-bolt"></i> Power & Generation
                        </h4>
                        <p style="font-size: 0.9rem; color: #374151;">
                            High-frequency generator converts AC power to precise high-voltage DC for X-ray tube operation
                        </p>
                    </div>
                    
                    <div style="background: #f8fafc; padding: 1.5rem; border-radius: 10px; border-left: 4px solid var(--napkin-accent);">
                        <h4 style="color: var(--napkin-accent); margin-bottom: 1rem;">
                            <i class="fas fa-radiation"></i> X-ray Production
                        </h4>
                        <p style="font-size: 0.9rem; color: #374151;">
                            Rotating anode X-ray tube generates controlled X-ray beam through electron-target interaction
                        </p>
                    </div>
                    
                    <div style="background: #f8fafc; padding: 1.5rem; border-radius: 10px; border-left: 4px solid var(--napkin-success);">
                        <h4 style="color: var(--napkin-success); margin-bottom: 1rem;">
                            <i class="fas fa-tv"></i> Image Detection
                        </h4>
                        <p style="font-size: 0.9rem; color: #374151;">
                            Digital flat panel detector converts X-ray photons to electrical signals for image formation
                        </p>
                    </div>
                    
                    <div style="background: #f8fafc; padding: 1.5rem; border-radius: 10px; border-left: 4px solid var(--napkin-warning);">
                        <h4 style="color: var(--napkin-warning); margin-bottom: 1rem;">
                            <i class="fas fa-cogs"></i> System Control
                        </h4>
                        <p style="font-size: 0.9rem; color: #374151;">
                            Integrated control system manages all components and provides user interface for operation
                        </p>
                    </div>
                </div>
                
                <div style="text-align: center; margin-top: 2rem;">
                    <button onclick="start3DTour()" style="background: var(--napkin-primary); color: white; border: none; padding: 1rem 2rem; border-radius: 25px; cursor: pointer; font-weight: 500; margin-right: 1rem;">
                        <i class="fas fa-play"></i> Start 3D Tour
                    </button>
                    <button onclick="openAssemblyGuide()" style="background: var(--napkin-accent); color: white; border: none; padding: 1rem 2rem; border-radius: 25px; cursor: pointer; font-weight: 500;">
                        <i class="fas fa-tools"></i> Assembly Guide
                    </button>
                </div>
            </div>
        `
    });
}

function showComponent(componentId) {
    const component = window.componentsModule.components[componentId];
    if (!component) return;
    
    showModal({
        title: component.title,
        content: `
            <div style="padding: 2rem;">
                <p style="font-size: 1.1rem; margin-bottom: 2rem; color: #374151;">
                    ${component.description}
                </p>
                
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem; margin-bottom: 2rem;">
                    <div>
                        <h4 style="color: var(--napkin-primary); margin-bottom: 1rem;">Technical Specifications</h4>
                        <div style="background: #f8fafc; padding: 1rem; border-radius: 8px;">
                            ${Object.entries(component.specifications).map(([key, value]) => `
                                <div style="display: flex; justify-content: space-between; padding: 0.5rem 0; border-bottom: 1px solid #e5e7eb;">
                                    <span style="font-weight: 500;">${key}:</span>
                                    <span style="color: var(--napkin-primary);">${value}</span>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                    
                    <div>
                        <h4 style="color: var(--napkin-primary); margin-bottom: 1rem;">Key Components</h4>
                        <div style="background: #f8fafc; padding: 1rem; border-radius: 8px;">
                            ${component.components.map(comp => `
                                <div style="display: flex; align-items: center; gap: 0.75rem; padding: 0.5rem 0;">
                                    <div style="width: 8px; height: 8px; background: var(--napkin-primary); border-radius: 50%;"></div>
                                    <span>${comp}</span>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                </div>
                
                <div style="text-align: center;">
                    <button onclick="view3DComponent('${componentId}')" style="background: var(--napkin-primary); color: white; border: none; padding: 0.75rem 2rem; border-radius: 25px; cursor: pointer; font-weight: 500; margin-right: 1rem;">
                        <i class="fas fa-cube"></i> View in 3D
                    </button>
                    <button onclick="startComponentQuiz('${componentId}')" style="background: var(--napkin-accent); color: white; border: none; padding: 0.75rem 2rem; border-radius: 25px; cursor: pointer; font-weight: 500;">
                        <i class="fas fa-question-circle"></i> Component Quiz
                    </button>
                </div>
            </div>
        `
    });
}

function loadModel(modelId) {
    const models = {
        'ge-discovery': 'GE Discovery XR656',
        'philips-digitaldiagnost': 'Philips DigitalDiagnost C90',
        'siemens-ysio': 'Siemens Ysio Max'
    };
    
    const modelName = models[modelId];
    const viewport = document.getElementById('modelViewport');
    
    // Show loading state
    viewport.innerHTML = `
        <div class="viewport-overlay">
            <div>Loading: ${modelName}</div>
            <div>Components: Loading...</div>
            <div>View: Standard</div>
            <div>Zoom: 100%</div>
        </div>
        <div style="display: flex; align-items: center; justify-content: center; height: 100%; color: #6b7280;">
            <div style="text-align: center;">
                <div style="width: 60px; height: 60px; border: 3px solid var(--napkin-primary); border-top: 3px solid transparent; border-radius: 50%; margin: 0 auto 1rem; animation: spin 1s linear infinite;"></div>
                <p>Loading ${modelName}...</p>
            </div>
        </div>
    `;
    
    // Simulate model loading
    setTimeout(() => {
        viewport.innerHTML = `
            <div class="viewport-overlay">
                <div>Model: ${modelName}</div>
                <div>Components: 6 visible</div>
                <div>View: ${window.componentsModule.isExplodedView ? 'Exploded' : 'Standard'}</div>
                <div>Zoom: 100%</div>
            </div>
            <div style="display: flex; align-items: center; justify-content: center; height: 100%; color: white;">
                <div style="text-align: center;">
                    <div style="width: 350px; height: 250px; background: linear-gradient(45deg, #374151, #4b5563); border-radius: 10px; margin: 0 auto 1rem; display: flex; align-items: center; justify-content: center; font-size: 3rem; position: relative; overflow: hidden;">
                        <i class="fas fa-hospital"></i>
                        <div style="position: absolute; bottom: 10px; right: 10px; font-size: 0.8rem; opacity: 0.7;">
                            ${modelName}
                        </div>
                    </div>
                    <p>${modelName} Loaded</p>
                    <p style="font-size: 0.9rem; opacity: 0.7;">Click and drag to interact</p>
                </div>
            </div>
        `;
        
        window.componentsModule.currentModel = modelId;
    }, 1500);
}

function toggleExplodedView() {
    window.componentsModule.isExplodedView = !window.componentsModule.isExplodedView;
    const btn = event.target.closest('.viewer-btn');
    
    if (window.componentsModule.isExplodedView) {
        btn.classList.add('active');
        btn.innerHTML = '<i class="fas fa-compress-arrows-alt"></i> Normal View';
    } else {
        btn.classList.remove('active');
        btn.innerHTML = '<i class="fas fa-expand-arrows-alt"></i> Exploded View';
    }
    
    // Reload current model with new view
    loadModel(window.componentsModule.currentModel);
}

function toggleXrayView() {
    window.componentsModule.isXrayView = !window.componentsModule.isXrayView;
    const btn = event.target.closest('.viewer-btn');
    
    if (window.componentsModule.isXrayView) {
        btn.classList.add('active');
        btn.innerHTML = '<i class="fas fa-eye-slash"></i> Normal View';
    } else {
        btn.classList.remove('active');
        btn.innerHTML = '<i class="fas fa-eye"></i> X-ray View';
    }
    
    // Apply X-ray filter effect
    const viewport = document.getElementById('modelViewport');
    if (window.componentsModule.isXrayView) {
        viewport.style.filter = 'invert(1) contrast(2) brightness(0.8)';
    } else {
        viewport.style.filter = 'none';
    }
}

function showVendor(vendorId) {
    // Update active tab
    document.querySelectorAll('.vendor-tab').forEach(tab => tab.classList.remove('active'));
    event.target.classList.add('active');
    
    // Show corresponding content
    document.querySelectorAll('.vendor-content').forEach(content => {
        content.classList.remove('active');
    });
    document.getElementById(`${vendorId}-content`).classList.add('active');
    
    window.componentsModule.currentVendor = vendorId;
}

function startAssemblyStep(stepNumber) {
    const steps = [
        'Foundation Setup',
        'Generator Installation', 
        'X-ray Tube Assembly',
        'Detector Mounting',
        'Control System',
        'Final Testing'
    ];
    
    showModal({
        title: `Assembly Step ${stepNumber}: ${steps[stepNumber - 1]}`,
        content: `
            <div style="padding: 2rem; text-align: center;">
                <div style="font-size: 3rem; color: var(--napkin-primary); margin-bottom: 1rem;">
                    <i class="fas fa-tools"></i>
                </div>
                <h3 style="margin-bottom: 1rem;">${steps[stepNumber - 1]}</h3>
                <p style="color: #6b7280; margin-bottom: 2rem;">
                    Interactive assembly simulation for step ${stepNumber} of the installation process
                </p>
                
                <div style="background: #f8fafc; padding: 2rem; border-radius: 10px; margin-bottom: 2rem;">
                    <h4 style="color: var(--napkin-primary); margin-bottom: 1rem;">Step Features:</h4>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 1rem;">
                        <div style="background: white; padding: 1rem; border-radius: 8px;">
                            <i class="fas fa-hand-pointer" style="color: var(--napkin-primary); margin-bottom: 0.5rem;"></i>
                            <div>Interactive 3D</div>
                        </div>
                        <div style="background: white; padding: 1rem; border-radius: 8px;">
                            <i class="fas fa-list-check" style="color: var(--napkin-primary); margin-bottom: 0.5rem;"></i>
                            <div>Step Checklist</div>
                        </div>
                        <div style="background: white; padding: 1rem; border-radius: 8px;">
                            <i class="fas fa-exclamation-triangle" style="color: var(--napkin-warning); margin-bottom: 0.5rem;"></i>
                            <div>Safety Alerts</div>
                        </div>
                        <div style="background: white; padding: 1rem; border-radius: 8px;">
                            <i class="fas fa-video" style="color: var(--napkin-accent); margin-bottom: 0.5rem;"></i>
                            <div>Video Guide</div>
                        </div>
                    </div>
                </div>
                
                <button onclick="launchAssemblySimulator(${stepNumber})" style="background: var(--napkin-primary); color: white; border: none; padding: 1rem 2rem; border-radius: 25px; cursor: pointer; font-weight: 500; font-size: 1.1rem;">
                    <i class="fas fa-play"></i> Start Assembly Step
                </button>
            </div>
        `
    });
}

function start3DTour() {
    alert('3D System Tour will launch in the full platform. This demonstrates the interactive interface design.');
    closeModal();
}

function openAssemblyGuide() {
    alert('Assembly Guide will open in the full platform with step-by-step interactive instructions.');
    closeModal();
}

function view3DComponent(componentId) {
    alert(`3D Component Viewer for ${componentId} will launch in the full platform.`);
    closeModal();
}

function startComponentQuiz(componentId) {
    alert(`Interactive quiz for ${componentId} component will start in the full platform.`);
    closeModal();
}

function launchAssemblySimulator(stepNumber) {
    alert(`Assembly Simulator for Step ${stepNumber} will launch in the full platform.`);
    closeModal();
}

function showModal({ title, content }) {
    const modal = document.createElement('div');
    modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.8);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10000;
        animation: fadeIn 0.3s ease;
    `;
    
    modal.innerHTML = `
        <div style="background: white; border-radius: 15px; max-width: 800px; width: 90%; max-height: 80%; overflow-y: auto; position: relative;">
            <div style="background: var(--napkin-gradient); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;">
                <h3 style="margin: 0; font-size: 1.3rem;">${title}</h3>
                <button onclick="closeModal()" style="position: absolute; top: 1rem; right: 1rem; background: none; border: none; color: white; font-size: 1.5rem; cursor: pointer;">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div>${content}</div>
        </div>
    `;
    
    document.body.appendChild(modal);
    window.currentModal = modal;
    
    // Close on outside click
    modal.addEventListener('click', (e) => {
        if (e.target === modal) closeModal();
    });
}

function closeModal() {
    if (window.currentModal) {
        window.currentModal.remove();
        window.currentModal = null;
    }
}

// Add CSS for animations
const style = document.createElement('style');
style.textContent = `
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
    
    @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
    }
    
    @keyframes pulse {
        0%, 100% { opacity: 0.6; }
        50% { opacity: 1; }
    }
`;
document.head.appendChild(style);

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.componentsModule = new ComponentsModule();
});
