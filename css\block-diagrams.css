/* CSS for X-ray System Block Diagrams Module */

/* Overview Section */
.overview {
    padding: 3rem 0;
    background-color: #f8f9fa;
}

.info-box {
    display: flex;
    background-color: white;
    border-radius: 8px;
    padding: 1.5rem;
    margin-top: 2rem;
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1);
    gap: 1.5rem;
}

.info-icon {
    font-size: 2.5rem;
    color: #3498db;
    flex-shrink: 0;
}

.info-content h3 {
    margin-top: 0;
    color: #2c3e50;
    margin-bottom: 1rem;
}

.info-content ul {
    margin-left: 1.5rem;
}

.info-content li {
    margin-bottom: 0.5rem;
}

/* Diagram Sections */
.diagram-section {
    padding: 4rem 0;
}

.diagram-section:nth-child(odd) {
    background-color: white;
}

.diagram-section:nth-child(even) {
    background-color: #f8f9fa;
}

.diagram-container {
    display: flex;
    flex-wrap: wrap;
    gap: 2rem;
    margin-top: 2rem;
}

.interactive-diagram {
    flex: 3 1 500px;
    background-color: white;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: auto;
}

.interactive-diagram img {
    max-width: 100%;
    height: auto;
}

.diagram-info {
    flex: 2 1 300px;
    background-color: white;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1);
}

.diagram-info h3 {
    margin-top: 0;
    color: #2c3e50;
    margin-bottom: 1rem;
    border-bottom: 2px solid #3498db;
    padding-bottom: 0.5rem;
}

.diagram-info ul {
    margin-left: 1.5rem;
    margin-bottom: 1.5rem;
}

.diagram-info li {
    margin-bottom: 0.5rem;
}

.common-issues {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 1rem;
    border-left: 4px solid #e74c3c;
}

.common-issues h4 {
    margin-top: 0;
    color: #e74c3c;
    margin-bottom: 0.5rem;
}

.common-issues ul {
    margin-bottom: 0;
}

/* SVG Block Diagram Styling */
.block-diagram {
    max-width: 100%;
    height: auto;
}

.block {
    fill: white;
    stroke: #2c3e50;
    stroke-width: 2;
    rx: 5;
    ry: 5;
    cursor: pointer;
    transition: fill 0.3s ease;
}

.power-block:hover {
    fill: #f39c12;
}

.control-block:hover {
    fill: #3498db;
}

.generator-block:hover {
    fill: #e74c3c;
}

.tube-block:hover {
    fill: #9b59b6;
}

.collimator-block:hover {
    fill: #2ecc71;
}

.detector-block:hover {
    fill: #1abc9c;
}

.processing-block:hover {
    fill: #f1c40f;
}

.display-block:hover {
    fill: #34495e;
}

.connection {
    stroke: #7f8c8d;
    stroke-width: 2;
    stroke-dasharray: 5, 5;
}

.block-title {
    text-anchor: middle;
    font-size: 14px;
    font-family: Arial, sans-serif;
    pointer-events: none;
}

/* Component Details */
.component-details {
    margin-top: 1rem;
    min-height: 200px;
}

.component-info {
    display: none;
}

.component-info.active {
    display: block;
}

.default-info {
    display: block;
}

/* Navigation Buttons */
.navigation-buttons {
    padding: 2rem 0;
    background-color: #f8f9fa;
    border-top: 1px solid #e9ecef;
}

.nav-buttons {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 1rem;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .info-box {
        flex-direction: column;
    }
    
    .diagram-container {
        flex-direction: column;
    }
    
    .interactive-diagram, .diagram-info {
        flex: none;
        width: 100%;
    }
    
    .nav-buttons {
        justify-content: center;
    }
    
    .nav-buttons .btn {
        width: 100%;
        text-align: center;
        margin-bottom: 0.5rem;
    }
}
