<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - X-ray System Training Module</title>
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/language-toggle.css">
    <link rel="stylesheet" href="css/auth.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/crypto-js/4.1.1/crypto-js.min.js" 
            integrity="sha512-E8QSvWZ0eCLGk4km3hxSsNmGWbLtSCSUcewDQPQWZF6pEU8GlT8a5fF32wOl1i8ftdMhssTrF/OhyGWwonTcXA==" 
            crossorigin="anonymous" 
            referrerpolicy="no-referrer"></script>
</head>
<body class="auth-page">
    <div class="auth-container">
        <div class="auth-box">
            <div class="auth-header">
                <img src="images/xray-logo.png" alt="X-ray System Training Logo" class="auth-logo" onerror="this.src='https://via.placeholder.com/150x50?text=X-ray+Training'">
                <h1 data-lang="login-title">Login to X-ray Training</h1>
            </div>
            
            <div class="auth-tabs">
                <button class="auth-tab active" data-tab="login" data-lang="login-tab">Login</button>
                <button class="auth-tab" data-tab="register" data-lang="register-tab">Register</button>
            </div>
            
            <div class="auth-content">
                <!-- Login Form -->
                <form id="login-form" class="auth-form active">
                    <div class="form-group">
                        <label for="login-username" data-lang="username">Username</label>
                        <div class="input-with-icon">
                            <i class="fas fa-user"></i>
                            <input type="text" id="login-username" name="username" required>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="login-password" data-lang="password">Password</label>
                        <div class="input-with-icon">
                            <i class="fas fa-lock"></i>
                            <input type="password" id="login-password" name="password" required>
                            <button type="button" class="toggle-password" tabindex="-1">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="form-group remember-me">
                        <label class="checkbox-container">
                            <input type="checkbox" id="remember-me" name="remember-me">
                            <span class="checkmark"></span>
                            <span data-lang="remember-me">Remember me</span>
                        </label>
                        <a href="#" class="forgot-password" data-lang="forgot-password">Forgot Password?</a>
                    </div>
                    
                    <div class="form-group">
                        <button type="submit" class="btn btn-primary btn-block" data-lang="login-button">Login</button>
                    </div>
                    
                    <div class="auth-message" id="login-message"></div>
                </form>
                
                <!-- Register Form -->
                <form id="register-form" class="auth-form">
                    <div class="form-group">
                        <label for="register-username" data-lang="username">Username</label>
                        <div class="input-with-icon">
                            <i class="fas fa-user"></i>
                            <input type="text" id="register-username" name="username" required>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="register-email" data-lang="email">Email</label>
                        <div class="input-with-icon">
                            <i class="fas fa-envelope"></i>
                            <input type="email" id="register-email" name="email" required>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="register-password" data-lang="password">Password</label>
                        <div class="input-with-icon">
                            <i class="fas fa-lock"></i>
                            <input type="password" id="register-password" name="password" required>
                            <button type="button" class="toggle-password" tabindex="-1">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                        <div class="password-strength">
                            <div class="strength-meter">
                                <div class="strength-bar"></div>
                            </div>
                            <div class="strength-text" data-lang="password-strength">Password Strength: <span>Weak</span></div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="register-confirm-password" data-lang="confirm-password">Confirm Password</label>
                        <div class="input-with-icon">
                            <i class="fas fa-lock"></i>
                            <input type="password" id="register-confirm-password" name="confirm-password" required>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <button type="submit" class="btn btn-primary btn-block" data-lang="register-button">Register</button>
                    </div>
                    
                    <div class="auth-message" id="register-message"></div>
                </form>
            </div>
            
            <div class="auth-footer">
                <p data-lang="auth-footer">© 2025 X-ray System Interactive Training Module</p>
                <div class="language-selector">
                    <button id="language-toggle-btn" class="language-toggle-btn" data-lang="language-toggle">العربية</button>
                </div>
            </div>
        </div>
    </div>
    
    <script src="js/language-toggle.js"></script>
    <script src="js/auth.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Tab switching
            const tabs = document.querySelectorAll('.auth-tab');
            const forms = document.querySelectorAll('.auth-form');
            
            tabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    const tabName = this.getAttribute('data-tab');
                    
                    // Update active tab
                    tabs.forEach(t => t.classList.remove('active'));
                    this.classList.add('active');
                    
                    // Show corresponding form
                    forms.forEach(form => {
                        form.classList.remove('active');
                        if (form.id === `${tabName}-form`) {
                            form.classList.add('active');
                        }
                    });
                });
            });
            
            // Password visibility toggle
            const toggleButtons = document.querySelectorAll('.toggle-password');
            toggleButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const input = this.parentElement.querySelector('input');
                    const icon = this.querySelector('i');
                    
                    if (input.type === 'password') {
                        input.type = 'text';
                        icon.classList.remove('fa-eye');
                        icon.classList.add('fa-eye-slash');
                    } else {
                        input.type = 'password';
                        icon.classList.remove('fa-eye-slash');
                        icon.classList.add('fa-eye');
                    }
                });
            });
            
            // Password strength meter
            const passwordInput = document.getElementById('register-password');
            const strengthBar = document.querySelector('.strength-bar');
            const strengthText = document.querySelector('.strength-text span');
            
            passwordInput.addEventListener('input', function() {
                const password = this.value;
                const strength = calculatePasswordStrength(password);
                
                // Update strength bar
                strengthBar.style.width = `${strength}%`;
                
                // Update color
                if (strength < 30) {
                    strengthBar.style.backgroundColor = '#ff4d4d'; // Red
                    strengthText.textContent = 'Weak';
                } else if (strength < 60) {
                    strengthBar.style.backgroundColor = '#ffa64d'; // Orange
                    strengthText.textContent = 'Medium';
                } else if (strength < 80) {
                    strengthBar.style.backgroundColor = '#4da6ff'; // Blue
                    strengthText.textContent = 'Strong';
                } else {
                    strengthBar.style.backgroundColor = '#4dff4d'; // Green
                    strengthText.textContent = 'Very Strong';
                }
            });
            
            // Login form submission
            const loginForm = document.getElementById('login-form');
            const loginMessage = document.getElementById('login-message');
            
            loginForm.addEventListener('submit', function(e) {
                e.preventDefault();
                
                const username = document.getElementById('login-username').value;
                const password = document.getElementById('login-password').value;
                
                // Attempt login
                if (window.XrayTrainingAuth.login(username, password)) {
                    loginMessage.textContent = 'Login successful! Redirecting...';
                    loginMessage.className = 'auth-message success';
                    
                    // Redirect to dashboard
                    setTimeout(() => {
                        window.location.href = 'index.html';
                    }, 1500);
                } else {
                    loginMessage.textContent = 'Invalid username or password';
                    loginMessage.className = 'auth-message error';
                }
            });
            
            // Register form submission
            const registerForm = document.getElementById('register-form');
            const registerMessage = document.getElementById('register-message');
            
            registerForm.addEventListener('submit', function(e) {
                e.preventDefault();
                
                const username = document.getElementById('register-username').value;
                const email = document.getElementById('register-email').value;
                const password = document.getElementById('register-password').value;
                const confirmPassword = document.getElementById('register-confirm-password').value;
                
                // Validate passwords match
                if (password !== confirmPassword) {
                    registerMessage.textContent = 'Passwords do not match';
                    registerMessage.className = 'auth-message error';
                    return;
                }
                
                // Validate password strength
                const strength = calculatePasswordStrength(password);
                if (strength < 50) {
                    registerMessage.textContent = 'Password is too weak. Please choose a stronger password.';
                    registerMessage.className = 'auth-message error';
                    return;
                }
                
                // Attempt registration
                if (window.XrayTrainingAuth.register(username, email, password)) {
                    registerMessage.textContent = 'Registration successful! You can now log in.';
                    registerMessage.className = 'auth-message success';
                    
                    // Clear form
                    registerForm.reset();
                    
                    // Switch to login tab
                    document.querySelector('[data-tab="login"]').click();
                } else {
                    registerMessage.textContent = 'Registration failed. Username may already exist.';
                    registerMessage.className = 'auth-message error';
                }
            });
            
            // Calculate password strength (0-100)
            function calculatePasswordStrength(password) {
                let strength = 0;
                
                // Length contribution (up to 30 points)
                strength += Math.min(30, password.length * 3);
                
                // Character variety contribution
                if (/[a-z]/.test(password)) strength += 10; // lowercase
                if (/[A-Z]/.test(password)) strength += 10; // uppercase
                if (/[0-9]/.test(password)) strength += 10; // numbers
                if (/[^a-zA-Z0-9]/.test(password)) strength += 15; // special chars
                
                // Complexity patterns
                if (/[a-z].*[A-Z]|[A-Z].*[a-z]/.test(password)) strength += 10; // mix of upper and lower
                if (/[a-zA-Z].*[0-9]|[0-9].*[a-zA-Z]/.test(password)) strength += 10; // letters and numbers
                if (/[a-zA-Z0-9].*[^a-zA-Z0-9]|[^a-zA-Z0-9].*[a-zA-Z0-9]/.test(password)) strength += 15; // alphanumeric and special
                
                return Math.min(100, strength);
            }
            
            // Check if user is already logged in
            if (window.XrayTrainingAuth.isAuthenticated) {
                window.location.href = 'index.html';
            }
        });
    </script>
</body>
</html>
