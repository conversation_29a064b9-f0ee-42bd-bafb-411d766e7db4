/**
 * Content Protection Module for X-ray System Training
 * Implements content encryption, access control, and secure content delivery
 */

// Configuration
const CONTENT_PROTECTION_CONFIG = {
    // Encryption key for content (in a real app, this would be stored securely)
    encryptionKey: 'X-ray-Content-Encryption-Key-2025',
    // Local storage keys
    storageKeys: {
        encryptedContent: 'xray_training_encrypted_content',
        contentAccess: 'xray_training_content_access'
    }
};

/**
 * Content Protection class to handle secure content delivery
 */
class ContentProtection {
    constructor() {
        this.encryptionKey = CONTENT_PROTECTION_CONFIG.encryptionKey;
        this.auth = window.XrayTrainingAuth;
        
        // Initialize content protection
        this.init();
    }
    
    /**
     * Initialize content protection
     */
    init() {
        // Check if user is authenticated
        if (!this.auth || !this.auth.isAuthenticated) {
            console.warn('User not authenticated. Content protection requires authentication.');
            return;
        }
        
        // Find protected content elements
        this.findAndProtectContent();
        
        // Listen for authentication events
        document.addEventListener('auth', (event) => {
            const { type } = event.detail;
            
            if (type === 'login' || type === 'session-restored') {
                // User logged in or session restored, protect content
                this.findAndProtectContent();
            } else if (type === 'logout') {
                // User logged out, hide protected content
                this.hideProtectedContent();
            }
        });
    }
    
    /**
     * Find and protect content elements
     */
    findAndProtectContent() {
        // Find all elements with data-protected attribute
        const protectedElements = document.querySelectorAll('[data-protected]');
        
        protectedElements.forEach(element => {
            const moduleId = element.getAttribute('data-protected');
            
            // Check if user has access to this module
            if (this.auth.hasAccessToModule(moduleId)) {
                // User has access, decrypt and show content
                this.decryptAndShowContent(element, moduleId);
            } else {
                // User doesn't have access, hide content
                this.hideContent(element, moduleId);
            }
        });
    }
    
    /**
     * Decrypt and show content
     * @param {HTMLElement} element - Protected element
     * @param {string} moduleId - Module ID
     */
    decryptAndShowContent(element, moduleId) {
        // Check if content is already decrypted
        if (element.getAttribute('data-decrypted') === 'true') {
            return;
        }
        
        // Get encrypted content
        let encryptedContent = element.getAttribute('data-encrypted-content');
        
        // If no encrypted content in attribute, check if it's in the element
        if (!encryptedContent) {
            encryptedContent = element.textContent.trim();
        }
        
        // If still no encrypted content, check if it needs to be loaded
        if (!encryptedContent) {
            const contentUrl = element.getAttribute('data-content-url');
            if (contentUrl) {
                // Load content from URL
                this.loadEncryptedContent(contentUrl)
                    .then(content => {
                        element.setAttribute('data-encrypted-content', content);
                        this.decryptAndShowContent(element, moduleId);
                    })
                    .catch(error => {
                        console.error('Error loading encrypted content:', error);
                        this.showAccessDeniedMessage(element, 'Error loading content');
                    });
                return;
            }
        }
        
        // If we have encrypted content, decrypt it
        if (encryptedContent) {
            try {
                const decryptedContent = this.decryptContent(encryptedContent);
                
                // Update element with decrypted content
                element.innerHTML = decryptedContent;
                element.setAttribute('data-decrypted', 'true');
                element.classList.remove('protected-content');
                element.classList.add('decrypted-content');
            } catch (error) {
                console.error('Error decrypting content:', error);
                this.showAccessDeniedMessage(element, 'Error decrypting content');
            }
        }
    }
    
    /**
     * Hide content that user doesn't have access to
     * @param {HTMLElement} element - Protected element
     * @param {string} moduleId - Module ID
     */
    hideContent(element, moduleId) {
        // Store original content if not already stored
        if (!element.getAttribute('data-original-content')) {
            element.setAttribute('data-original-content', element.innerHTML);
        }
        
        // Show access denied message
        this.showAccessDeniedMessage(element, 'Access Denied');
    }
    
    /**
     * Hide all protected content (e.g., on logout)
     */
    hideProtectedContent() {
        const protectedElements = document.querySelectorAll('[data-protected]');
        
        protectedElements.forEach(element => {
            const moduleId = element.getAttribute('data-protected');
            this.hideContent(element, moduleId);
        });
    }
    
    /**
     * Show access denied message
     * @param {HTMLElement} element - Protected element
     * @param {string} message - Message to show
     */
    showAccessDeniedMessage(element, message) {
        element.innerHTML = `
            <div class="access-denied">
                <i class="fas fa-lock"></i>
                <h3>${message}</h3>
                <p>You don't have access to this content. Please contact your instructor or administrator.</p>
            </div>
        `;
        element.classList.add('protected-content');
        element.classList.remove('decrypted-content');
        element.removeAttribute('data-decrypted');
    }
    
    /**
     * Load encrypted content from URL
     * @param {string} url - URL to load content from
     * @returns {Promise<string>} - Encrypted content
     */
    loadEncryptedContent(url) {
        return fetch(url)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`Failed to load content: ${response.status} ${response.statusText}`);
                }
                return response.text();
            });
    }
    
    /**
     * Encrypt content
     * @param {string} content - Content to encrypt
     * @returns {string} - Encrypted content
     */
    encryptContent(content) {
        return CryptoJS.AES.encrypt(content, this.encryptionKey).toString();
    }
    
    /**
     * Decrypt content
     * @param {string} encryptedContent - Encrypted content
     * @returns {string} - Decrypted content
     */
    decryptContent(encryptedContent) {
        const bytes = CryptoJS.AES.decrypt(encryptedContent, this.encryptionKey);
        return bytes.toString(CryptoJS.enc.Utf8);
    }
    
    /**
     * Protect a specific element
     * @param {HTMLElement} element - Element to protect
     * @param {string} moduleId - Module ID
     */
    protectElement(element, moduleId) {
        // Set protection attributes
        element.setAttribute('data-protected', moduleId);
        
        // Store original content
        const originalContent = element.innerHTML;
        element.setAttribute('data-original-content', originalContent);
        
        // Encrypt content
        const encryptedContent = this.encryptContent(originalContent);
        element.setAttribute('data-encrypted-content', encryptedContent);
        
        // Apply protection
        if (this.auth.hasAccessToModule(moduleId)) {
            // User has access, keep content visible
            element.setAttribute('data-decrypted', 'true');
            element.classList.add('decrypted-content');
        } else {
            // User doesn't have access, hide content
            this.hideContent(element, moduleId);
        }
    }
    
    /**
     * Create and encrypt a content file
     * @param {string} content - Content to encrypt
     * @param {string} filename - Filename to save as
     */
    createEncryptedFile(content, filename) {
        // Encrypt content
        const encryptedContent = this.encryptContent(content);
        
        // Create blob
        const blob = new Blob([encryptedContent], { type: 'text/plain' });
        
        // Create download link
        const a = document.createElement('a');
        a.href = URL.createObjectURL(blob);
        a.download = filename;
        
        // Trigger download
        document.body.appendChild(a);
        a.click();
        
        // Clean up
        document.body.removeChild(a);
        URL.revokeObjectURL(a.href);
    }
}

// Initialize content protection when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Wait for auth to be initialized
    if (window.XrayTrainingAuth) {
        window.XrayTrainingContentProtection = new ContentProtection();
    } else {
        // Wait for auth to be initialized
        const authCheckInterval = setInterval(() => {
            if (window.XrayTrainingAuth) {
                window.XrayTrainingContentProtection = new ContentProtection();
                clearInterval(authCheckInterval);
            }
        }, 100);
        
        // Clear interval after 5 seconds if auth is not initialized
        setTimeout(() => {
            clearInterval(authCheckInterval);
            console.error('Auth not initialized after 5 seconds');
        }, 5000);
    }
});
