<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Module 4: Generator Systems & Power Electronics - VirtualX Pro</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --napkin-primary: #1d4ed8;
            --napkin-secondary: #1e40af;
            --napkin-accent: #3b82f6;
            --napkin-success: #10b981;
            --napkin-warning: #f59e0b;
            --napkin-danger: #ef4444;
            --napkin-dark: #1f2937;
            --napkin-light: #eff6ff;
            --napkin-gradient: linear-gradient(135deg, #1d4ed8 0%, #3b82f6 100%);
            --napkin-shadow: 0 10px 25px rgba(29, 78, 216, 0.1);
            --napkin-shadow-lg: 0 20px 40px rgba(29, 78, 216, 0.15);
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
            min-height: 100vh;
            line-height: 1.6;
        }

        .training-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }

        .module-header {
            text-align: center;
            margin-bottom: 3rem;
            background: var(--napkin-gradient);
            color: white;
            padding: 3rem 2rem;
            border-radius: 20px;
            box-shadow: var(--napkin-shadow-lg);
            position: relative;
            overflow: hidden;
        }

        .module-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><path d="M20,20 L30,30 L20,40 Z" fill="rgba(255,255,255,0.05)"/><path d="M70,30 L80,40 L70,50 Z" fill="rgba(255,255,255,0.05)"/></svg>');
            animation: float 12s ease-in-out infinite;
        }

        .module-title {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 1rem;
            position: relative;
            z-index: 1;
        }

        .module-subtitle {
            font-size: 1.3rem;
            opacity: 0.9;
            margin-bottom: 2rem;
            position: relative;
            z-index: 1;
        }

        /* Power Electronics Mind Map */
        .power-mindmap {
            position: relative;
            background: white;
            border-radius: 20px;
            padding: 4rem;
            margin: 2rem 0;
            box-shadow: var(--napkin-shadow);
            overflow: hidden;
            min-height: 700px;
        }

        .generator-core {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 240px;
            height: 240px;
            background: var(--napkin-gradient);
            border-radius: 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 1.1rem;
            text-align: center;
            box-shadow: var(--napkin-shadow-lg);
            cursor: pointer;
            transition: all 0.3s ease;
            z-index: 10;
        }

        .generator-core:hover {
            transform: translate(-50%, -50%) scale(1.05);
            box-shadow: 0 25px 50px rgba(29, 78, 216, 0.3);
        }

        .generator-icon {
            font-size: 4rem;
            margin-bottom: 0.5rem;
        }

        .circuit-node {
            position: absolute;
            width: 180px;
            height: 120px;
            background: white;
            border: 3px solid var(--napkin-primary);
            border-radius: 15px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: var(--napkin-shadow);
        }

        .circuit-node:hover {
            transform: scale(1.05);
            background: var(--napkin-primary);
            color: white;
            box-shadow: var(--napkin-shadow-lg);
        }

        .circuit-icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
            color: var(--napkin-primary);
        }

        .circuit-node:hover .circuit-icon {
            color: white;
        }

        /* Circuit positioning */
        .input-node { top: 10%; left: 10%; }
        .igbt-node { top: 10%; right: 10%; }
        .transformer-node { bottom: 10%; left: 10%; }
        .rectifier-node { bottom: 10%; right: 10%; }
        .control-node { top: 50%; left: 5%; transform: translateY(-50%); }
        .feedback-node { top: 50%; right: 5%; transform: translateY(-50%); }

        /* Connection lines */
        .circuit-line {
            position: absolute;
            background: var(--napkin-primary);
            height: 3px;
            transform-origin: left center;
            opacity: 0.6;
            z-index: 1;
            border-radius: 2px;
        }

        .line-input { top: 25%; left: 25%; width: 200px; transform: rotate(15deg); }
        .line-igbt { top: 25%; right: 25%; width: 200px; transform: rotate(-15deg); transform-origin: right center; }
        .line-transformer { bottom: 25%; left: 25%; width: 200px; transform: rotate(-15deg); }
        .line-rectifier { bottom: 25%; right: 25%; width: 200px; transform: rotate(15deg); transform-origin: right center; }
        .line-control { top: 50%; left: 20%; width: 150px; }
        .line-feedback { top: 50%; right: 20%; width: 150px; transform-origin: right center; }

        /* Virtual Oscilloscope */
        .oscilloscope-section {
            background: var(--napkin-dark);
            border-radius: 20px;
            padding: 2rem;
            margin: 3rem 0;
            color: white;
            position: relative;
            overflow: hidden;
        }

        .scope-header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .scope-controls {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin-bottom: 2rem;
            flex-wrap: wrap;
        }

        .scope-btn {
            background: var(--napkin-primary);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 25px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .scope-btn:hover {
            background: var(--napkin-accent);
            transform: translateY(-2px);
        }

        .scope-btn.active {
            background: var(--napkin-accent);
            box-shadow: 0 0 20px rgba(59, 130, 246, 0.5);
        }

        .scope-display {
            width: 100%;
            height: 400px;
            background: #000;
            border-radius: 15px;
            position: relative;
            overflow: hidden;
            border: 3px solid #333;
        }

        .scope-grid {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: 
                linear-gradient(rgba(0, 255, 0, 0.2) 1px, transparent 1px),
                linear-gradient(90deg, rgba(0, 255, 0, 0.2) 1px, transparent 1px);
            background-size: 40px 40px;
        }

        .waveform {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }

        .scope-info {
            position: absolute;
            top: 1rem;
            left: 1rem;
            background: rgba(0, 0, 0, 0.8);
            color: #00ff00;
            padding: 1rem;
            border-radius: 8px;
            font-family: monospace;
            font-size: 0.9rem;
        }

        /* IGBT Circuit Simulator */
        .igbt-simulator {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            margin: 3rem 0;
            box-shadow: var(--napkin-shadow);
        }

        .simulator-controls {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .control-group {
            background: var(--napkin-light);
            padding: 1.5rem;
            border-radius: 12px;
        }

        .control-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: var(--napkin-dark);
        }

        .control-slider {
            width: 100%;
            height: 8px;
            border-radius: 4px;
            background: #e5e7eb;
            outline: none;
            -webkit-appearance: none;
        }

        .control-slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: var(--napkin-primary);
            cursor: pointer;
        }

        .control-value {
            display: block;
            margin-top: 0.5rem;
            font-weight: 500;
            color: var(--napkin-primary);
        }

        .circuit-diagram {
            width: 100%;
            height: 300px;
            background: #f8fafc;
            border-radius: 15px;
            position: relative;
            overflow: hidden;
            border: 2px solid #e5e7eb;
        }

        /* Performance Metrics */
        .performance-metrics {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            margin: 3rem 0;
            box-shadow: var(--napkin-shadow);
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-top: 2rem;
        }

        .metric-card {
            background: var(--napkin-light);
            padding: 1.5rem;
            border-radius: 12px;
            text-align: center;
            border-left: 4px solid var(--napkin-primary);
        }

        .metric-icon {
            font-size: 2.5rem;
            color: var(--napkin-primary);
            margin-bottom: 1rem;
        }

        .metric-value {
            font-size: 2rem;
            font-weight: bold;
            color: var(--napkin-dark);
            margin-bottom: 0.5rem;
        }

        .metric-label {
            color: #6b7280;
            font-weight: 500;
        }

        .metric-trend {
            margin-top: 0.5rem;
            font-size: 0.9rem;
        }

        .trend-up {
            color: var(--napkin-success);
        }

        .trend-down {
            color: var(--napkin-danger);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .module-title {
                font-size: 2rem;
            }
            
            .power-mindmap {
                padding: 2rem;
                min-height: 500px;
            }
            
            .generator-core {
                width: 180px;
                height: 180px;
                font-size: 0.9rem;
            }
            
            .circuit-node {
                width: 120px;
                height: 80px;
                font-size: 0.8rem;
            }
            
            .simulator-controls {
                grid-template-columns: 1fr;
            }
        }

        /* Animations */
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.05); opacity: 0.9; }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .animate-waveform {
            animation: pulse 2s ease-in-out infinite;
        }
    </style>
</head>
<body>
    <div class="training-container">
        <!-- Module Header -->
        <div class="module-header animate__animated animate__fadeInDown">
            <h1 class="module-title">
                <i class="fas fa-bolt"></i>
                Generator Systems & Power Electronics
            </h1>
            <p class="module-subtitle">High-Frequency Generator Technology & Control</p>
            <p>Master IGBT switching circuits, power electronics, and control systems through hands-on simulation</p>
            
            <div class="module-stats" style="display: flex; justify-content: center; gap: 3rem; margin-top: 2rem;">
                <div class="stat-item">
                    <span style="font-size: 2.5rem; font-weight: bold; display: block;">12</span>
                    <span style="font-size: 0.9rem; opacity: 0.8;">Hours</span>
                </div>
                <div class="stat-item">
                    <span style="font-size: 2.5rem; font-weight: bold; display: block;">IGBT</span>
                    <span style="font-size: 0.9rem; opacity: 0.8;">Circuits</span>
                </div>
                <div class="stat-item">
                    <span style="font-size: 2.5rem; font-weight: bold; display: block;">100kHz</span>
                    <span style="font-size: 0.9rem; opacity: 0.8;">Switching</span>
                </div>
                <div class="stat-item">
                    <span style="font-size: 2.5rem; font-weight: bold; display: block;">95%</span>
                    <span style="font-size: 0.9rem; opacity: 0.8;">Efficiency</span>
                </div>
            </div>
        </div>

        <!-- Power Electronics Mind Map -->
        <div class="power-mindmap">
            <h2 style="text-align: center; margin-bottom: 3rem; color: var(--napkin-dark); font-size: 1.8rem;">
                High-Frequency Generator Architecture
            </h2>
            
            <!-- Central Generator Core -->
            <div class="generator-core" onclick="showGeneratorOverview()">
                <div class="generator-icon">
                    <i class="fas fa-microchip"></i>
                </div>
                <div>High-Frequency<br>Generator</div>
            </div>
            
            <!-- Circuit Nodes -->
            <div class="circuit-node input-node" onclick="showCircuit('input')">
                <div class="circuit-icon">
                    <i class="fas fa-plug"></i>
                </div>
                <div>AC Input<br>& Filtering</div>
            </div>
            
            <div class="circuit-node igbt-node" onclick="showCircuit('igbt')">
                <div class="circuit-icon">
                    <i class="fas fa-microchip"></i>
                </div>
                <div>IGBT Switching<br>Circuit</div>
            </div>
            
            <div class="circuit-node transformer-node" onclick="showCircuit('transformer')">
                <div class="circuit-icon">
                    <i class="fas fa-coils"></i>
                </div>
                <div>High-Voltage<br>Transformer</div>
            </div>
            
            <div class="circuit-node rectifier-node" onclick="showCircuit('rectifier')">
                <div class="circuit-icon">
                    <i class="fas fa-wave-square"></i>
                </div>
                <div>Rectifier &<br>Smoothing</div>
            </div>
            
            <div class="circuit-node control-node" onclick="showCircuit('control')">
                <div class="circuit-icon">
                    <i class="fas fa-cogs"></i>
                </div>
                <div>Control<br>System</div>
            </div>
            
            <div class="circuit-node feedback-node" onclick="showCircuit('feedback')">
                <div class="circuit-icon">
                    <i class="fas fa-sync"></i>
                </div>
                <div>Feedback<br>Loop</div>
            </div>
            
            <!-- Connection Lines -->
            <div class="circuit-line line-input"></div>
            <div class="circuit-line line-igbt"></div>
            <div class="circuit-line line-transformer"></div>
            <div class="circuit-line line-rectifier"></div>
            <div class="circuit-line line-control"></div>
            <div class="circuit-line line-feedback"></div>
        </div>

        <!-- Virtual Oscilloscope -->
        <div class="oscilloscope-section">
            <div class="scope-header">
                <h3 style="font-size: 1.8rem; margin-bottom: 1rem;">
                    <i class="fas fa-wave-square"></i>
                    Virtual Oscilloscope & Signal Analysis
                </h3>
                <p style="opacity: 0.8;">Analyze waveforms and switching characteristics in real-time</p>
            </div>
            
            <div class="scope-controls">
                <button type="button" class="scope-btn active" onclick="showWaveform('input-voltage')">
                    <i class="fas fa-sine-wave"></i> Input Voltage
                </button>
                <button type="button" class="scope-btn" onclick="showWaveform('igbt-switching')">
                    <i class="fas fa-square-wave"></i> IGBT Switching
                </button>
                <button type="button" class="scope-btn" onclick="showWaveform('transformer-primary')">
                    <i class="fas fa-wave-square"></i> Transformer Primary
                </button>
                <button type="button" class="scope-btn" onclick="showWaveform('output-voltage')">
                    <i class="fas fa-chart-line"></i> Output Voltage
                </button>
                <button type="button" class="scope-btn" onclick="showWaveform('current-feedback')">
                    <i class="fas fa-bolt"></i> Current Feedback
                </button>
            </div>
            
            <div class="scope-display" id="scopeDisplay">
                <div class="scope-grid"></div>
                <canvas class="waveform" id="waveformCanvas" width="800" height="400"></canvas>
                
                <div class="scope-info">
                    <div>CH1: Input Voltage</div>
                    <div>Frequency: 50 Hz</div>
                    <div>Amplitude: 230V RMS</div>
                    <div>Time/Div: 5ms</div>
                    <div>Volt/Div: 50V</div>
                </div>
            </div>
        </div>

        <!-- IGBT Circuit Simulator -->
        <div class="igbt-simulator">
            <h3 style="text-align: center; margin-bottom: 2rem; color: var(--napkin-dark); font-size: 1.8rem;">
                <i class="fas fa-microchip"></i>
                IGBT Circuit Simulator
            </h3>
            
            <div class="simulator-controls">
                <div class="control-group">
                    <label class="control-label">Switching Frequency</label>
                    <input type="range" class="control-slider" id="frequency" min="10" max="100" value="50" oninput="updateSimulation()">
                    <span class="control-value" id="frequencyValue">50 kHz</span>
                </div>
                
                <div class="control-group">
                    <label class="control-label">Duty Cycle</label>
                    <input type="range" class="control-slider" id="dutyCycle" min="10" max="90" value="50" oninput="updateSimulation()">
                    <span class="control-value" id="dutyCycleValue">50%</span>
                </div>
                
                <div class="control-group">
                    <label class="control-label">Load Current</label>
                    <input type="range" class="control-slider" id="loadCurrent" min="10" max="500" value="100" oninput="updateSimulation()">
                    <span class="control-value" id="loadCurrentValue">100 mA</span>
                </div>
                
                <div class="control-group">
                    <label class="control-label">Gate Voltage</label>
                    <input type="range" class="control-slider" id="gateVoltage" min="10" max="20" value="15" oninput="updateSimulation()">
                    <span class="control-value" id="gateVoltageValue">15V</span>
                </div>
            </div>
            
            <div class="circuit-diagram" id="circuitDiagram">
                <div style="display: flex; align-items: center; justify-content: center; height: 100%; color: #6b7280;">
                    <div style="text-align: center;">
                        <i class="fas fa-microchip" style="font-size: 4rem; margin-bottom: 1rem; color: var(--napkin-primary);"></i>
                        <p>IGBT Circuit Simulation</p>
                        <p style="font-size: 0.9rem; opacity: 0.7;">Adjust parameters to see real-time changes</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Performance Metrics -->
        <div class="performance-metrics">
            <h3 style="text-align: center; margin-bottom: 2rem; color: var(--napkin-dark); font-size: 1.8rem;">
                <i class="fas fa-chart-line"></i>
                Real-time Performance Metrics
            </h3>
            
            <div class="metrics-grid">
                <div class="metric-card">
                    <div class="metric-icon">
                        <i class="fas fa-bolt"></i>
                    </div>
                    <div class="metric-value" id="powerOutput">85.2</div>
                    <div class="metric-label">Power Output (kW)</div>
                    <div class="metric-trend trend-up">
                        <i class="fas fa-arrow-up"></i> +2.3%
                    </div>
                </div>
                
                <div class="metric-card">
                    <div class="metric-icon">
                        <i class="fas fa-percentage"></i>
                    </div>
                    <div class="metric-value" id="efficiency">95.8</div>
                    <div class="metric-label">Efficiency (%)</div>
                    <div class="metric-trend trend-up">
                        <i class="fas fa-arrow-up"></i> +0.5%
                    </div>
                </div>
                
                <div class="metric-card">
                    <div class="metric-icon">
                        <i class="fas fa-wave-square"></i>
                    </div>
                    <div class="metric-value" id="rippleFactor">0.8</div>
                    <div class="metric-label">Voltage Ripple (%)</div>
                    <div class="metric-trend trend-down">
                        <i class="fas fa-arrow-down"></i> -0.2%
                    </div>
                </div>
                
                <div class="metric-card">
                    <div class="metric-icon">
                        <i class="fas fa-thermometer-half"></i>
                    </div>
                    <div class="metric-value" id="temperature">68</div>
                    <div class="metric-label">Temperature (°C)</div>
                    <div class="metric-trend trend-up">
                        <i class="fas fa-arrow-up"></i> +1.2°C
                    </div>
                </div>
                
                <div class="metric-card">
                    <div class="metric-icon">
                        <i class="fas fa-tachometer-alt"></i>
                    </div>
                    <div class="metric-value" id="frequency">50.0</div>
                    <div class="metric-label">Switching Freq (kHz)</div>
                    <div class="metric-trend">
                        <i class="fas fa-minus"></i> Stable
                    </div>
                </div>
                
                <div class="metric-card">
                    <div class="metric-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <div class="metric-value" id="thd">2.1</div>
                    <div class="metric-label">THD (%)</div>
                    <div class="metric-trend trend-down">
                        <i class="fas fa-arrow-down"></i> -0.3%
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="js/module-4-generator.js"></script>
</body>
</html>
