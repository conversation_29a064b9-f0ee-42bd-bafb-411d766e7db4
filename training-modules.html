<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Training Modules - VirtualX Pro</title>
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/enterprise.css">
    <link rel="stylesheet" href="css/competition-features.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
    <style>
        .training-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .training-header {
            text-align: center;
            margin-bottom: 3rem;
            background: var(--enterprise-gradient);
            color: white;
            padding: 3rem 2rem;
            border-radius: var(--border-radius);
        }
        
        .learning-path-selector {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin-bottom: 3rem;
            flex-wrap: wrap;
        }
        
        .path-card {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: var(--border-radius);
            padding: 2rem;
            cursor: pointer;
            transition: var(--transition);
            text-align: center;
            min-width: 250px;
            position: relative;
        }
        
        .path-card.active {
            border-color: var(--enterprise-primary);
            background: var(--enterprise-light);
        }
        
        .path-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-enterprise);
        }
        
        .path-icon {
            font-size: 3rem;
            color: var(--enterprise-primary);
            margin-bottom: 1rem;
        }
        
        .path-title {
            font-size: 1.3rem;
            font-weight: 700;
            color: var(--enterprise-dark);
            margin-bottom: 0.5rem;
        }
        
        .path-description {
            color: #666;
            font-size: 0.9rem;
            margin-bottom: 1rem;
        }
        
        .path-stats {
            display: flex;
            justify-content: space-around;
            margin-top: 1rem;
            padding-top: 1rem;
            border-top: 1px solid #eee;
        }
        
        .path-stat {
            text-align: center;
        }
        
        .stat-number {
            font-size: 1.5rem;
            font-weight: bold;
            color: var(--enterprise-primary);
        }
        
        .stat-label {
            font-size: 0.8rem;
            color: #666;
        }
        
        .modules-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }
        
        .module-card {
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-enterprise);
            overflow: hidden;
            transition: var(--transition);
            position: relative;
        }
        
        .module-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 30px rgba(0, 102, 204, 0.2);
        }
        
        .module-header {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 1.5rem;
            position: relative;
        }
        
        .module-difficulty {
            position: absolute;
            top: 1rem;
            right: 1rem;
            padding: 0.25rem 0.75rem;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .difficulty-beginner {
            background: var(--enterprise-success);
            color: white;
        }
        
        .difficulty-intermediate {
            background: var(--enterprise-warning);
            color: white;
        }
        
        .difficulty-advanced {
            background: var(--enterprise-danger);
            color: white;
        }
        
        .module-icon {
            font-size: 3rem;
            color: var(--enterprise-primary);
            margin-bottom: 1rem;
        }
        
        .module-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--enterprise-dark);
            margin-bottom: 0.5rem;
        }
        
        .module-subtitle {
            color: #666;
            margin-bottom: 1rem;
        }
        
        .module-content {
            padding: 2rem;
        }
        
        .module-objectives {
            margin-bottom: 1.5rem;
        }
        
        .objective-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
        }
        
        .objective-item i {
            color: var(--enterprise-success);
            width: 16px;
        }
        
        .module-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin: 1.5rem 0;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: var(--border-radius);
        }
        
        .detail-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.9rem;
        }
        
        .detail-item i {
            color: var(--enterprise-primary);
            width: 16px;
        }
        
        .module-progress {
            margin: 1.5rem 0;
        }
        
        .progress-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
        }
        
        .progress-text {
            font-size: 0.9rem;
            color: #666;
        }
        
        .progress-percentage {
            font-weight: bold;
            color: var(--enterprise-primary);
        }
        
        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            background: var(--enterprise-success);
            transition: width 0.3s ease;
        }
        
        .module-actions {
            display: flex;
            gap: 1rem;
            margin-top: 1.5rem;
        }
        
        .btn-start {
            flex: 1;
            background: var(--enterprise-primary);
            color: white;
            border: none;
            padding: 1rem;
            border-radius: var(--border-radius);
            cursor: pointer;
            font-weight: 600;
            transition: var(--transition);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }
        
        .btn-start:hover {
            background: var(--enterprise-secondary);
            transform: translateY(-2px);
        }
        
        .btn-preview {
            background: transparent;
            color: var(--enterprise-primary);
            border: 2px solid var(--enterprise-primary);
            padding: 1rem;
            border-radius: var(--border-radius);
            cursor: pointer;
            font-weight: 600;
            transition: var(--transition);
        }
        
        .btn-preview:hover {
            background: var(--enterprise-primary);
            color: white;
        }
        
        .learning-analytics {
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-enterprise);
            padding: 2rem;
            margin: 3rem 0;
        }
        
        .analytics-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .analytics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 2rem;
        }
        
        .analytics-card {
            text-align: center;
            padding: 1.5rem;
            background: #f8f9fa;
            border-radius: var(--border-radius);
        }
        
        .analytics-number {
            font-size: 2.5rem;
            font-weight: bold;
            color: var(--enterprise-primary);
            margin-bottom: 0.5rem;
        }
        
        .analytics-label {
            color: #666;
            font-size: 0.9rem;
        }
        
        .recommended-path {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border: 2px solid var(--enterprise-primary);
            border-radius: var(--border-radius);
            padding: 2rem;
            margin: 3rem 0;
            text-align: center;
        }
        
        .recommendation-icon {
            font-size: 3rem;
            color: var(--enterprise-primary);
            margin-bottom: 1rem;
        }
        
        .achievement-badges {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin: 2rem 0;
            flex-wrap: wrap;
        }
        
        .badge {
            background: var(--enterprise-success);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .badge.locked {
            background: #ccc;
            color: #666;
        }
        
        .module-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.9);
            z-index: 10000;
            display: none;
            align-items: center;
            justify-content: center;
        }
        
        .modal-content {
            background: white;
            border-radius: var(--border-radius);
            width: 90%;
            max-width: 800px;
            max-height: 80%;
            overflow-y: auto;
        }
        
        .modal-header {
            background: var(--enterprise-gradient);
            color: white;
            padding: 1.5rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .modal-close {
            background: none;
            border: none;
            color: white;
            font-size: 1.5rem;
            cursor: pointer;
        }
        
        .modal-body {
            padding: 2rem;
        }

        .technical-content {
            margin: 1.5rem 0;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: var(--border-radius);
            border-left: 4px solid var(--enterprise-primary);
        }

        .visual-aids-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 0.75rem;
            margin-top: 1rem;
        }

        .visual-aid-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem;
            background: white;
            border-radius: var(--border-radius);
            font-size: 0.85rem;
            font-weight: 500;
            color: var(--enterprise-dark);
            transition: var(--transition);
        }

        .visual-aid-item:hover {
            background: var(--enterprise-light);
            transform: translateY(-2px);
        }

        .visual-aid-item i {
            color: var(--enterprise-primary);
            width: 16px;
        }

        .real-environment-badge {
            background: var(--enterprise-success);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 12px;
            font-size: 0.7rem;
            font-weight: 600;
            text-transform: uppercase;
            margin-left: 0.5rem;
        }

        .certification-level {
            background: var(--enterprise-warning);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 12px;
            font-size: 0.7rem;
            font-weight: 600;
            margin-left: 0.5rem;
        }

        .technical-specs {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: var(--border-radius);
            padding: 1rem;
            margin: 1rem 0;
        }

        .spec-row {
            display: flex;
            justify-content: space-between;
            padding: 0.5rem 0;
            border-bottom: 1px solid #f0f0f0;
            font-size: 0.9rem;
        }

        .spec-label {
            font-weight: 600;
            color: #666;
        }

        .spec-value {
            color: var(--enterprise-primary);
            font-weight: 500;
        }

        .objectives-header {
            margin-bottom: 1rem;
            color: var(--enterprise-primary);
        }

        .technical-header {
            color: var(--enterprise-primary);
            margin: 1rem 0 0.5rem 0;
        }

        .progress-0 {
            width: 0%;
        }

        .btn-locked {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .btn-locked:hover {
            background: #6c757d;
            transform: none;
        }

        .difficulty-expert {
            background: #8e24aa;
            color: white;
        }

        .module-card[data-path="specialist"] {
            border: 2px solid #8e24aa;
        }

        .module-card[data-path="specialist"]:hover {
            border-color: #7b1fa2;
            box-shadow: 0 8px 30px rgba(142, 36, 170, 0.2);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="enterprise-nav">
        <div class="container">
            <div class="nav-content">
                <div class="nav-brand">
                    <a href="index.html">
                        <img src="images/virtualx-pro-logo.png" alt="VirtualX Pro" class="nav-logo" onerror="this.style.display='none'">
                        <span class="brand-text">VirtualX Pro</span>
                    </a>
                </div>
                <ul class="main-nav">
                    <li><a href="index.html"><i class="fas fa-home"></i> Dashboard</a></li>
                    <li><a href="system-models.html"><i class="fas fa-cogs"></i> System Models</a></li>
                    <li><a href="training-modules.html" class="active"><i class="fas fa-graduation-cap"></i> Training Modules</a></li>
                    <li><a href="simulation.html"><i class="fas fa-cube"></i> 3D Simulation</a></li>
                    <li><a href="ai-diagnostics.html"><i class="fas fa-stethoscope"></i> AI Diagnostics</a></li>
                    <li><a href="collaboration.html"><i class="fas fa-users"></i> Remote Support</a></li>
                    <li><a href="analytics.html"><i class="fas fa-chart-line"></i> Analytics</a></li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="training-container">
        <!-- Header Section -->
        <div class="training-header animate__animated animate__fadeInDown">
            <h1>Advanced Training Modules</h1>
            <p>Comprehensive learning paths designed for biomedical engineers and technical support professionals</p>
            <div style="margin-top: 2rem;">
                <div class="stat-item" style="display: inline-block; margin: 0 2rem;">
                    <div class="stat-number" style="font-size: 2rem; font-weight: bold;">25+</div>
                    <div class="stat-label">Training Modules</div>
                </div>
                <div class="stat-item" style="display: inline-block; margin: 0 2rem;">
                    <div class="stat-number" style="font-size: 2rem; font-weight: bold;">4</div>
                    <div class="stat-label">Learning Paths</div>
                </div>
                <div class="stat-item" style="display: inline-block; margin: 0 2rem;">
                    <div class="stat-number" style="font-size: 2rem; font-weight: bold;">100+</div>
                    <div class="stat-label">Hours Content</div>
                </div>
            </div>
        </div>

        <!-- Learning Path Selector -->
        <div class="learning-path-selector">
            <div class="path-card active" data-path="foundation">
                <div class="path-icon">
                    <i class="fas fa-seedling"></i>
                </div>
                <div class="path-title">Foundation Level</div>
                <div class="path-description">Essential knowledge for new technicians</div>
                <div class="path-stats">
                    <div class="path-stat">
                        <div class="stat-number">8</div>
                        <div class="stat-label">Modules</div>
                    </div>
                    <div class="path-stat">
                        <div class="stat-number">24h</div>
                        <div class="stat-label">Duration</div>
                    </div>
                </div>
            </div>
            
            <div class="path-card" data-path="intermediate">
                <div class="path-icon">
                    <i class="fas fa-tools"></i>
                </div>
                <div class="path-title">Intermediate Level</div>
                <div class="path-description">Advanced technical skills and troubleshooting</div>
                <div class="path-stats">
                    <div class="path-stat">
                        <div class="stat-number">10</div>
                        <div class="stat-label">Modules</div>
                    </div>
                    <div class="path-stat">
                        <div class="stat-number">35h</div>
                        <div class="stat-label">Duration</div>
                    </div>
                </div>
            </div>
            
            <div class="path-card" data-path="advanced">
                <div class="path-icon">
                    <i class="fas fa-rocket"></i>
                </div>
                <div class="path-title">Advanced Level</div>
                <div class="path-description">Expert-level system optimization and innovation</div>
                <div class="path-stats">
                    <div class="path-stat">
                        <div class="stat-number">7</div>
                        <div class="stat-label">Modules</div>
                    </div>
                    <div class="path-stat">
                        <div class="stat-number">28h</div>
                        <div class="stat-label">Duration</div>
                    </div>
                </div>
            </div>
            
            <div class="path-card" data-path="specialist">
                <div class="path-icon">
                    <i class="fas fa-crown"></i>
                </div>
                <div class="path-title">Specialist Level</div>
                <div class="path-description">Leadership and training expertise</div>
                <div class="path-stats">
                    <div class="path-stat">
                        <div class="stat-number">5</div>
                        <div class="stat-label">Modules</div>
                    </div>
                    <div class="path-stat">
                        <div class="stat-number">20h</div>
                        <div class="stat-label">Duration</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recommended Learning Path -->
        <div class="recommended-path">
            <div class="recommendation-icon">
                <i class="fas fa-lightbulb"></i>
            </div>
            <h3 style="color: var(--enterprise-primary); margin-bottom: 1rem;">Recommended for You</h3>
            <p style="margin-bottom: 1.5rem;">Based on your profile and progress, we recommend starting with the <strong>Intermediate Level</strong> path to build upon your existing knowledge.</p>
            <button type="button" class="btn-start" onclick="selectPath('intermediate')" style="max-width: 300px; margin: 0 auto;">
                <i class="fas fa-play"></i>
                Start Recommended Path
            </button>
        </div>

        <!-- Achievement Badges -->
        <div class="achievement-badges">
            <div class="badge">
                <i class="fas fa-medal"></i>
                Component Expert
            </div>
            <div class="badge">
                <i class="fas fa-trophy"></i>
                Safety Champion
            </div>
            <div class="badge locked">
                <i class="fas fa-lock"></i>
                Troubleshooting Master
            </div>
            <div class="badge locked">
                <i class="fas fa-star"></i>
                System Specialist
            </div>
        </div>

        <!-- Training Modules Grid -->
        <div class="modules-grid" id="modules-grid">
            <!-- Foundation Level Modules -->

            <!-- Module 1: X-ray Physics Fundamentals -->
            <div class="module-card" data-path="foundation">
                <div class="module-header">
                    <div class="module-difficulty difficulty-beginner">Foundation</div>
                    <div class="module-icon">
                        <i class="fas fa-atom"></i>
                    </div>
                    <div class="module-title">X-ray Physics Fundamentals</div>
                    <div class="module-subtitle">Interactive Physics Simulation & Theory</div>
                </div>
                <div class="module-content">
                    <div class="module-objectives">
                        <h4 style="margin-bottom: 1rem; color: var(--enterprise-primary);">Technical Learning Objectives</h4>
                        <div class="objective-item">
                            <i class="fas fa-check"></i>
                            <span>Master X-ray production mechanisms (Bremsstrahlung & Characteristic)</span>
                        </div>
                        <div class="objective-item">
                            <i class="fas fa-check"></i>
                            <span>Calculate beam attenuation using Beer-Lambert law</span>
                        </div>
                        <div class="objective-item">
                            <i class="fas fa-check"></i>
                            <span>Understand photoelectric effect and Compton scattering</span>
                        </div>
                        <div class="objective-item">
                            <i class="fas fa-check"></i>
                            <span>Apply radiation safety principles (ALARA, shielding calculations)</span>
                        </div>
                    </div>

                    <div class="technical-content">
                        <h5 style="color: var(--enterprise-primary); margin: 1rem 0 0.5rem 0;">Interactive Visual Aids</h5>
                        <div class="visual-aids-grid">
                            <div class="visual-aid-item">
                                <i class="fas fa-play-circle"></i>
                                <span>3D Electron-Target Interaction Simulator</span>
                            </div>
                            <div class="visual-aid-item">
                                <i class="fas fa-chart-line"></i>
                                <span>Real-time Spectrum Analysis Tool</span>
                            </div>
                            <div class="visual-aid-item">
                                <i class="fas fa-calculator"></i>
                                <span>HVL & Filtration Calculator</span>
                            </div>
                            <div class="visual-aid-item">
                                <i class="fas fa-shield-alt"></i>
                                <span>Radiation Shielding Designer</span>
                            </div>
                        </div>
                    </div>

                    <div class="module-details">
                        <div class="detail-item">
                            <i class="fas fa-clock"></i>
                            <span>Duration: 6 hours</span>
                        </div>
                        <div class="detail-item">
                            <i class="fas fa-flask"></i>
                            <span>Virtual Physics Lab</span>
                        </div>
                        <div class="detail-item">
                            <i class="fas fa-certificate"></i>
                            <span>IEC 60601 Compliant</span>
                        </div>
                        <div class="detail-item">
                            <i class="fas fa-users"></i>
                            <span>Entry Level</span>
                        </div>
                    </div>

                    <div class="module-progress">
                        <div class="progress-header">
                            <span class="progress-text">Progress</span>
                            <span class="progress-percentage">0%</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 0%"></div>
                        </div>
                    </div>

                    <div class="module-actions">
                        <button type="button" class="btn-start" onclick="startModule('physics-fundamentals')">
                            <i class="fas fa-play"></i>
                            Start Module
                        </button>
                        <button type="button" class="btn-preview" onclick="previewModule('physics-fundamentals')">
                            <i class="fas fa-eye"></i>
                            Preview
                        </button>
                    </div>
                </div>
            </div>

            <!-- Module 2: System Components & Architecture -->
            <div class="module-card" data-path="foundation">
                <div class="module-header">
                    <div class="module-difficulty difficulty-beginner">Foundation</div>
                    <div class="module-icon">
                        <i class="fas fa-sitemap"></i>
                    </div>
                    <div class="module-title">System Components & Architecture</div>
                    <div class="module-subtitle">Interactive 3D Component Exploration</div>
                </div>
                <div class="module-content">
                    <div class="module-objectives">
                        <h4 style="margin-bottom: 1rem; color: var(--enterprise-primary);">Technical Learning Objectives</h4>
                        <div class="objective-item">
                            <i class="fas fa-check"></i>
                            <span>Identify all major X-ray system components and subsystems</span>
                        </div>
                        <div class="objective-item">
                            <i class="fas fa-check"></i>
                            <span>Understand signal flow and control system architecture</span>
                        </div>
                        <div class="objective-item">
                            <i class="fas fa-check"></i>
                            <span>Practice virtual assembly/disassembly procedures</span>
                        </div>
                        <div class="objective-item">
                            <i class="fas fa-check"></i>
                            <span>Compare multi-vendor system designs and specifications</span>
                        </div>
                    </div>

                    <div class="technical-content">
                        <h5 style="color: var(--enterprise-primary); margin: 1rem 0 0.5rem 0;">Real Environment Simulation</h5>
                        <div class="visual-aids-grid">
                            <div class="visual-aid-item">
                                <i class="fas fa-cube"></i>
                                <span>Photorealistic 3D System Models</span>
                            </div>
                            <div class="visual-aid-item">
                                <i class="fas fa-tools"></i>
                                <span>Virtual Assembly Workbench</span>
                            </div>
                            <div class="visual-aid-item">
                                <i class="fas fa-project-diagram"></i>
                                <span>Interactive Block Diagrams</span>
                            </div>
                            <div class="visual-aid-item">
                                <i class="fas fa-search"></i>
                                <span>Component Identification Quiz</span>
                            </div>
                        </div>
                    </div>

                    <div class="module-details">
                        <div class="detail-item">
                            <i class="fas fa-clock"></i>
                            <span>Duration: 8 hours</span>
                        </div>
                        <div class="detail-item">
                            <i class="fas fa-cube"></i>
                            <span>3D Interactive</span>
                        </div>
                        <div class="detail-item">
                            <i class="fas fa-certificate"></i>
                            <span>Component Specialist</span>
                        </div>
                        <div class="detail-item">
                            <i class="fas fa-users"></i>
                            <span>Prerequisites: Physics</span>
                        </div>
                    </div>

                    <div class="module-progress">
                        <div class="progress-header">
                            <span class="progress-text">Progress</span>
                            <span class="progress-percentage">75%</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 75%"></div>
                        </div>
                    </div>

                    <div class="module-actions">
                        <button type="button" class="btn-start" onclick="startModule('components')">
                            <i class="fas fa-play"></i>
                            Continue Module
                        </button>
                        <button type="button" class="btn-preview" onclick="previewModule('components')">
                            <i class="fas fa-eye"></i>
                            Preview
                        </button>
                    </div>
                </div>
            </div>

            <!-- Module 3: Safety & Radiation Protection -->
            <div class="module-card" data-path="foundation">
                <div class="module-header">
                    <div class="module-difficulty difficulty-beginner">Foundation</div>
                    <div class="module-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <div class="module-title">Safety & Radiation Protection</div>
                    <div class="module-subtitle">Comprehensive Safety Protocol Training</div>
                </div>
                <div class="module-content">
                    <div class="module-objectives">
                        <h4 style="margin-bottom: 1rem; color: var(--enterprise-primary);">Safety Certification Objectives</h4>
                        <div class="objective-item">
                            <i class="fas fa-check"></i>
                            <span>Master ALARA principles and dose optimization techniques</span>
                        </div>
                        <div class="objective-item">
                            <i class="fas fa-check"></i>
                            <span>Calculate shielding requirements per IEC 60601-2-43</span>
                        </div>
                        <div class="objective-item">
                            <i class="fas fa-check"></i>
                            <span>Implement emergency procedures and incident response</span>
                        </div>
                        <div class="objective-item">
                            <i class="fas fa-check"></i>
                            <span>Perform radiation surveys and compliance testing</span>
                        </div>
                    </div>

                    <div class="technical-content">
                        <h5 style="color: var(--enterprise-primary); margin: 1rem 0 0.5rem 0;">Interactive Safety Tools</h5>
                        <div class="visual-aids-grid">
                            <div class="visual-aid-item">
                                <i class="fas fa-radiation"></i>
                                <span>Radiation Field Visualizer</span>
                            </div>
                            <div class="visual-aid-item">
                                <i class="fas fa-calculator"></i>
                                <span>Dose Rate Calculator</span>
                            </div>
                            <div class="visual-aid-item">
                                <i class="fas fa-exclamation-triangle"></i>
                                <span>Emergency Response Simulator</span>
                            </div>
                            <div class="visual-aid-item">
                                <i class="fas fa-clipboard-check"></i>
                                <span>Compliance Checklist Tool</span>
                            </div>
                        </div>
                    </div>

                    <div class="module-details">
                        <div class="detail-item">
                            <i class="fas fa-clock"></i>
                            <span>Duration: 5 hours</span>
                        </div>
                        <div class="detail-item">
                            <i class="fas fa-shield-alt"></i>
                            <span>Safety Simulation</span>
                        </div>
                        <div class="detail-item">
                            <i class="fas fa-certificate"></i>
                            <span>Safety Officer Cert</span>
                        </div>
                        <div class="detail-item">
                            <i class="fas fa-users"></i>
                            <span>Mandatory for All</span>
                        </div>
                    </div>

                    <div class="module-progress">
                        <div class="progress-header">
                            <span class="progress-text">Progress</span>
                            <span class="progress-percentage">45%</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 45%"></div>
                        </div>
                    </div>

                    <div class="module-actions">
                        <button type="button" class="btn-start" onclick="startModule('safety-radiation')">
                            <i class="fas fa-play"></i>
                            Continue Module
                        </button>
                        <button type="button" class="btn-preview" onclick="previewModule('safety-radiation')">
                            <i class="fas fa-eye"></i>
                            Preview
                        </button>
                    </div>
                </div>
            </div>

            <!-- Intermediate Level Modules -->

            <!-- Module 4: Generator Systems & Power Electronics -->
            <div class="module-card" data-path="intermediate">
                <div class="module-header">
                    <div class="module-difficulty difficulty-intermediate">Intermediate</div>
                    <div class="module-icon">
                        <i class="fas fa-bolt"></i>
                    </div>
                    <div class="module-title">Generator Systems & Power Electronics</div>
                    <div class="module-subtitle">High-Frequency Generator Technology & Control</div>
                </div>
                <div class="module-content">
                    <div class="module-objectives">
                        <h4 class="objectives-header">Technical Mastery Objectives</h4>
                        <div class="objective-item">
                            <i class="fas fa-check"></i>
                            <span>Analyze IGBT switching circuits and high-frequency inverters</span>
                        </div>
                        <div class="objective-item">
                            <i class="fas fa-check"></i>
                            <span>Troubleshoot generator control systems and feedback loops</span>
                        </div>
                        <div class="objective-item">
                            <i class="fas fa-check"></i>
                            <span>Perform precision calibration using oscilloscope measurements</span>
                        </div>
                        <div class="objective-item">
                            <i class="fas fa-check"></i>
                            <span>Implement power factor correction and harmonic analysis</span>
                        </div>
                    </div>

                    <div class="technical-content">
                        <h5 class="technical-header">Real Environment Simulation Tools</h5>
                        <div class="visual-aids-grid">
                            <div class="visual-aid-item">
                                <i class="fas fa-wave-square"></i>
                                <span>Virtual Oscilloscope & Signal Analysis</span>
                            </div>
                            <div class="visual-aid-item">
                                <i class="fas fa-microchip"></i>
                                <span>IGBT Circuit Simulator</span>
                            </div>
                            <div class="visual-aid-item">
                                <i class="fas fa-chart-line"></i>
                                <span>Power Quality Analyzer</span>
                            </div>
                            <div class="visual-aid-item">
                                <i class="fas fa-cogs"></i>
                                <span>Control System Debugger</span>
                            </div>
                        </div>
                    </div>

                    <div class="technical-specs">
                        <h6>Technical Specifications Covered:</h6>
                        <div class="spec-row">
                            <span class="spec-label">Generator Types:</span>
                            <span class="spec-value">Single-phase, 3-phase, High-frequency</span>
                        </div>
                        <div class="spec-row">
                            <span class="spec-label">Power Range:</span>
                            <span class="spec-value">32kW - 100kW systems</span>
                        </div>
                        <div class="spec-row">
                            <span class="spec-label">Frequency:</span>
                            <span class="spec-value">50Hz - 100kHz switching</span>
                        </div>
                        <div class="spec-row">
                            <span class="spec-label">Standards:</span>
                            <span class="spec-value">IEC 60601-2-43, FDA 21 CFR 1020</span>
                        </div>
                    </div>

                    <div class="module-details">
                        <div class="detail-item">
                            <i class="fas fa-clock"></i>
                            <span>Duration: 12 hours</span>
                        </div>
                        <div class="detail-item">
                            <i class="fas fa-microchip"></i>
                            <span>Circuit Simulation</span>
                        </div>
                        <div class="detail-item">
                            <i class="fas fa-certificate"></i>
                            <span>Power Systems Cert</span>
                        </div>
                        <div class="detail-item">
                            <i class="fas fa-users"></i>
                            <span>Requires: Physics + Components</span>
                        </div>
                    </div>

                    <div class="module-progress">
                        <div class="progress-header">
                            <span class="progress-text">Progress</span>
                            <span class="progress-percentage">0%</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill progress-0"></div>
                        </div>
                    </div>

                    <div class="module-actions">
                        <button type="button" class="btn-start" onclick="startModule('generator-systems')">
                            <i class="fas fa-play"></i>
                            Start Module
                        </button>
                        <button type="button" class="btn-preview" onclick="previewModule('generator-systems')">
                            <i class="fas fa-eye"></i>
                            Preview
                        </button>
                    </div>
                </div>
            </div>

            <!-- Module 5: AI-Powered Diagnostics -->
            <div class="module-card" data-path="intermediate">
                <div class="module-header">
                    <div class="module-difficulty difficulty-intermediate">Intermediate</div>
                    <div class="module-icon">
                        <i class="fas fa-brain"></i>
                    </div>
                    <div class="module-title">AI-Powered Diagnostics</div>
                    <div class="module-subtitle">Machine Learning for Intelligent Fault Detection</div>
                </div>
                <div class="module-content">
                    <div class="module-objectives">
                        <h4 class="objectives-header">AI Competency Objectives</h4>
                        <div class="objective-item">
                            <i class="fas fa-check"></i>
                            <span>Master AI diagnostic algorithms and pattern recognition</span>
                        </div>
                        <div class="objective-item">
                            <i class="fas fa-check"></i>
                            <span>Interpret confidence levels and statistical analysis</span>
                        </div>
                        <div class="objective-item">
                            <i class="fas fa-check"></i>
                            <span>Validate AI recommendations with manual verification</span>
                        </div>
                        <div class="objective-item">
                            <i class="fas fa-check"></i>
                            <span>Train custom models for specific equipment types</span>
                        </div>
                    </div>

                    <div class="technical-content">
                        <h5 class="technical-header">AI Training Environment</h5>
                        <div class="visual-aids-grid">
                            <div class="visual-aid-item">
                                <i class="fas fa-robot"></i>
                                <span>Neural Network Visualizer</span>
                            </div>
                            <div class="visual-aid-item">
                                <i class="fas fa-database"></i>
                                <span>10,000+ Case Database</span>
                            </div>
                            <div class="visual-aid-item">
                                <i class="fas fa-chart-area"></i>
                                <span>Statistical Analysis Tools</span>
                            </div>
                            <div class="visual-aid-item">
                                <i class="fas fa-flask"></i>
                                <span>Model Training Laboratory</span>
                            </div>
                        </div>
                    </div>

                    <div class="technical-specs">
                        <h6>AI System Specifications:</h6>
                        <div class="spec-row">
                            <span class="spec-label">Accuracy Rate:</span>
                            <span class="spec-value">95.2% validated accuracy</span>
                        </div>
                        <div class="spec-row">
                            <span class="spec-label">Training Data:</span>
                            <span class="spec-value">10,000+ real service cases</span>
                        </div>
                        <div class="spec-row">
                            <span class="spec-label">Response Time:</span>
                            <span class="spec-value">&lt;2 seconds analysis</span>
                        </div>
                        <div class="spec-row">
                            <span class="spec-label">Confidence Threshold:</span>
                            <span class="spec-value">85% minimum for recommendations</span>
                        </div>
                    </div>

                    <div class="module-details">
                        <div class="detail-item">
                            <i class="fas fa-clock"></i>
                            <span>Duration: 10 hours</span>
                        </div>
                        <div class="detail-item">
                            <i class="fas fa-robot"></i>
                            <span>AI Simulation</span>
                        </div>
                        <div class="detail-item">
                            <i class="fas fa-certificate"></i>
                            <span>AI Diagnostics Cert</span>
                        </div>
                        <div class="detail-item">
                            <i class="fas fa-users"></i>
                            <span>Requires: Generator Systems</span>
                        </div>
                    </div>

                    <div class="module-progress">
                        <div class="progress-header">
                            <span class="progress-text">Progress</span>
                            <span class="progress-percentage">0%</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill progress-0"></div>
                        </div>
                    </div>

                    <div class="module-actions">
                        <button type="button" class="btn-start" onclick="startModule('ai-diagnostics')">
                            <i class="fas fa-play"></i>
                            Start Module
                        </button>
                        <button type="button" class="btn-preview" onclick="previewModule('ai-diagnostics')">
                            <i class="fas fa-eye"></i>
                            Preview
                        </button>
                    </div>
                </div>
            </div>

            <!-- Advanced Level Modules -->

            <!-- Module 6: Advanced Image Processing & Quality Assurance -->
            <div class="module-card" data-path="advanced">
                <div class="module-header">
                    <div class="module-difficulty difficulty-advanced">Advanced</div>
                    <div class="module-icon">
                        <i class="fas fa-image"></i>
                    </div>
                    <div class="module-title">Advanced Image Processing & QA</div>
                    <div class="module-subtitle">Digital Image Enhancement & Quality Control</div>
                </div>
                <div class="module-content">
                    <div class="module-objectives">
                        <h4 class="objectives-header">Expert Technical Objectives</h4>
                        <div class="objective-item">
                            <i class="fas fa-check"></i>
                            <span>Implement advanced image processing algorithms (FFT, convolution)</span>
                        </div>
                        <div class="objective-item">
                            <i class="fas fa-check"></i>
                            <span>Perform comprehensive QA testing per IEC 61223-2-6</span>
                        </div>
                        <div class="objective-item">
                            <i class="fas fa-check"></i>
                            <span>Optimize detector calibration and flat-field correction</span>
                        </div>
                        <div class="objective-item">
                            <i class="fas fa-check"></i>
                            <span>Analyze MTF, DQE, and noise power spectrum measurements</span>
                        </div>
                    </div>

                    <div class="technical-content">
                        <h5 class="technical-header">Professional QA Laboratory</h5>
                        <div class="visual-aids-grid">
                            <div class="visual-aid-item">
                                <i class="fas fa-microscope"></i>
                                <span>MTF Analysis Workstation</span>
                            </div>
                            <div class="visual-aid-item">
                                <i class="fas fa-chart-area"></i>
                                <span>DQE Measurement Suite</span>
                            </div>
                            <div class="visual-aid-item">
                                <i class="fas fa-adjust"></i>
                                <span>Flat-Field Correction Tool</span>
                            </div>
                            <div class="visual-aid-item">
                                <i class="fas fa-wave-square"></i>
                                <span>Noise Analysis Platform</span>
                            </div>
                        </div>
                    </div>

                    <div class="technical-specs">
                        <h6>QA Standards & Measurements:</h6>
                        <div class="spec-row">
                            <span class="spec-label">MTF @ 2 lp/mm:</span>
                            <span class="spec-value">&gt;40% (IEC requirement)</span>
                        </div>
                        <div class="spec-row">
                            <span class="spec-label">DQE @ 1 lp/mm:</span>
                            <span class="spec-value">&gt;60% target performance</span>
                        </div>
                        <div class="spec-row">
                            <span class="spec-label">Uniformity:</span>
                            <span class="spec-value">±5% across detector area</span>
                        </div>
                        <div class="spec-row">
                            <span class="spec-label">Linearity:</span>
                            <span class="spec-value">R² &gt; 0.99 correlation</span>
                        </div>
                    </div>

                    <div class="module-details">
                        <div class="detail-item">
                            <i class="fas fa-clock"></i>
                            <span>Duration: 15 hours</span>
                        </div>
                        <div class="detail-item">
                            <i class="fas fa-microscope"></i>
                            <span>QA Laboratory</span>
                        </div>
                        <div class="detail-item">
                            <i class="fas fa-certificate"></i>
                            <span>QA Specialist Cert</span>
                        </div>
                        <div class="detail-item">
                            <i class="fas fa-users"></i>
                            <span>Requires: AI Diagnostics</span>
                        </div>
                    </div>

                    <div class="module-progress">
                        <div class="progress-header">
                            <span class="progress-text">Progress</span>
                            <span class="progress-percentage">0%</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill progress-0"></div>
                        </div>
                    </div>

                    <div class="module-actions">
                        <button type="button" class="btn-start" onclick="startModule('image-processing-qa')">
                            <i class="fas fa-play"></i>
                            Start Module
                        </button>
                        <button type="button" class="btn-preview" onclick="previewModule('image-processing-qa')">
                            <i class="fas fa-eye"></i>
                            Preview
                        </button>
                    </div>
                </div>
            </div>

            <!-- Module 7: System Integration & Network Architecture -->
            <div class="module-card" data-path="advanced">
                <div class="module-header">
                    <div class="module-difficulty difficulty-advanced">Advanced</div>
                    <div class="module-icon">
                        <i class="fas fa-network-wired"></i>
                    </div>
                    <div class="module-title">System Integration & Network Architecture</div>
                    <div class="module-subtitle">DICOM, HL7, and Enterprise Integration</div>
                </div>
                <div class="module-content">
                    <div class="module-objectives">
                        <h4 class="objectives-header">Integration Mastery Objectives</h4>
                        <div class="objective-item">
                            <i class="fas fa-check"></i>
                            <span>Configure DICOM services and workflow optimization</span>
                        </div>
                        <div class="objective-item">
                            <i class="fas fa-check"></i>
                            <span>Implement HL7 interfaces and data exchange protocols</span>
                        </div>
                        <div class="objective-item">
                            <i class="fas fa-check"></i>
                            <span>Design secure network architectures with VPN/firewall</span>
                        </div>
                        <div class="objective-item">
                            <i class="fas fa-check"></i>
                            <span>Troubleshoot enterprise-level connectivity issues</span>
                        </div>
                    </div>

                    <div class="technical-content">
                        <h5 class="technical-header">Enterprise Integration Lab</h5>
                        <div class="visual-aids-grid">
                            <div class="visual-aid-item">
                                <i class="fas fa-server"></i>
                                <span>DICOM Server Simulator</span>
                            </div>
                            <div class="visual-aid-item">
                                <i class="fas fa-exchange-alt"></i>
                                <span>HL7 Message Builder</span>
                            </div>
                            <div class="visual-aid-item">
                                <i class="fas fa-shield-alt"></i>
                                <span>Network Security Tester</span>
                            </div>
                            <div class="visual-aid-item">
                                <i class="fas fa-project-diagram"></i>
                                <span>Architecture Design Tool</span>
                            </div>
                        </div>
                    </div>

                    <div class="technical-specs">
                        <h6>Integration Standards:</h6>
                        <div class="spec-row">
                            <span class="spec-label">DICOM Version:</span>
                            <span class="spec-value">3.0 PS3.1-PS3.20 compliant</span>
                        </div>
                        <div class="spec-row">
                            <span class="spec-label">HL7 Version:</span>
                            <span class="spec-value">v2.5.1 and FHIR R4</span>
                        </div>
                        <div class="spec-row">
                            <span class="spec-label">Security:</span>
                            <span class="spec-value">TLS 1.3, AES-256 encryption</span>
                        </div>
                        <div class="spec-row">
                            <span class="spec-label">Performance:</span>
                            <span class="spec-value">&lt;100ms response time</span>
                        </div>
                    </div>

                    <div class="module-details">
                        <div class="detail-item">
                            <i class="fas fa-clock"></i>
                            <span>Duration: 18 hours</span>
                        </div>
                        <div class="detail-item">
                            <i class="fas fa-network-wired"></i>
                            <span>Integration Lab</span>
                        </div>
                        <div class="detail-item">
                            <i class="fas fa-certificate"></i>
                            <span>Integration Expert</span>
                        </div>
                        <div class="detail-item">
                            <i class="fas fa-users"></i>
                            <span>Requires: Image Processing</span>
                        </div>
                    </div>

                    <div class="module-progress">
                        <div class="progress-header">
                            <span class="progress-text">Progress</span>
                            <span class="progress-percentage">0%</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill progress-0"></div>
                        </div>
                    </div>

                    <div class="module-actions">
                        <button type="button" class="btn-start" onclick="startModule('system-integration')" disabled class="btn-locked">
                            <i class="fas fa-lock"></i>
                            Locked
                        </button>
                        <button type="button" class="btn-preview" onclick="previewModule('system-integration')">
                            <i class="fas fa-eye"></i>
                            Preview
                        </button>
                    </div>
                </div>
            </div>

            <!-- Expert/Specialist Level Modules -->

            <!-- Module 8: Certified Technical Engineer Program -->
            <div class="module-card" data-path="specialist">
                <div class="module-header">
                    <div class="module-difficulty difficulty-advanced" style="background: #8e24aa;">Expert</div>
                    <div class="module-icon">
                        <i class="fas fa-crown"></i>
                    </div>
                    <div class="module-title">Certified Technical Engineer Program</div>
                    <div class="module-subtitle">Professional Certification & Leadership Training</div>
                </div>
                <div class="module-content">
                    <div class="module-objectives">
                        <h4 class="objectives-header">Professional Certification Objectives</h4>
                        <div class="objective-item">
                            <i class="fas fa-check"></i>
                            <span>Master all IEC 60601 series standards and compliance testing</span>
                        </div>
                        <div class="objective-item">
                            <i class="fas fa-check"></i>
                            <span>Lead complex multi-system installations and commissioning</span>
                        </div>
                        <div class="objective-item">
                            <i class="fas fa-check"></i>
                            <span>Develop custom training programs and technical documentation</span>
                        </div>
                        <div class="objective-item">
                            <i class="fas fa-check"></i>
                            <span>Achieve professional engineer certification (PE/CEng equivalent)</span>
                        </div>
                    </div>

                    <div class="technical-content">
                        <h5 class="technical-header">Professional Engineering Suite</h5>
                        <div class="visual-aids-grid">
                            <div class="visual-aid-item">
                                <i class="fas fa-graduation-cap"></i>
                                <span>Certification Exam Simulator</span>
                            </div>
                            <div class="visual-aid-item">
                                <i class="fas fa-clipboard-list"></i>
                                <span>Compliance Testing Suite</span>
                            </div>
                            <div class="visual-aid-item">
                                <i class="fas fa-chalkboard-teacher"></i>
                                <span>Training Development Kit</span>
                            </div>
                            <div class="visual-aid-item">
                                <i class="fas fa-award"></i>
                                <span>Professional Portfolio Builder</span>
                            </div>
                        </div>
                    </div>

                    <div class="technical-specs">
                        <h6>Certification Requirements:</h6>
                        <div class="spec-row">
                            <span class="spec-label">Exam Score:</span>
                            <span class="spec-value">&gt;90% on comprehensive assessment</span>
                        </div>
                        <div class="spec-row">
                            <span class="spec-label">Practical Projects:</span>
                            <span class="spec-value">5 real-world case studies</span>
                        </div>
                        <div class="spec-row">
                            <span class="spec-label">Peer Review:</span>
                            <span class="spec-value">Professional engineer validation</span>
                        </div>
                        <div class="spec-row">
                            <span class="spec-label">Continuing Education:</span>
                            <span class="spec-value">40 hours annually</span>
                        </div>
                    </div>

                    <div class="module-details">
                        <div class="detail-item">
                            <i class="fas fa-clock"></i>
                            <span>Duration: 25 hours</span>
                        </div>
                        <div class="detail-item">
                            <i class="fas fa-crown"></i>
                            <span>Certification Program</span>
                        </div>
                        <div class="detail-item">
                            <i class="fas fa-certificate"></i>
                            <span>Professional Engineer</span>
                        </div>
                        <div class="detail-item">
                            <i class="fas fa-users"></i>
                            <span>Requires: All Previous</span>
                        </div>
                    </div>

                    <div class="module-progress">
                        <div class="progress-header">
                            <span class="progress-text">Progress</span>
                            <span class="progress-percentage">0%</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill progress-0"></div>
                        </div>
                    </div>

                    <div class="module-actions">
                        <button type="button" class="btn-start" onclick="startModule('certified-engineer')" disabled class="btn-locked">
                            <i class="fas fa-lock"></i>
                            Locked
                        </button>
                        <button type="button" class="btn-preview" onclick="previewModule('certified-engineer')">
                            <i class="fas fa-eye"></i>
                            Preview
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Learning Analytics -->
        <div class="learning-analytics">
            <div class="analytics-header">
                <h3 style="color: var(--enterprise-primary); margin-bottom: 1rem;">Your Learning Analytics</h3>
                <p style="color: #666;">Track your progress and identify areas for improvement</p>
            </div>
            
            <div class="analytics-grid">
                <div class="analytics-card">
                    <div class="analytics-number">12</div>
                    <div class="analytics-label">Modules Completed</div>
                </div>
                <div class="analytics-card">
                    <div class="analytics-number">87%</div>
                    <div class="analytics-label">Average Score</div>
                </div>
                <div class="analytics-card">
                    <div class="analytics-number">45h</div>
                    <div class="analytics-label">Total Study Time</div>
                </div>
                <div class="analytics-card">
                    <div class="analytics-number">3</div>
                    <div class="analytics-label">Certificates Earned</div>
                </div>
                <div class="analytics-card">
                    <div class="analytics-number">95%</div>
                    <div class="analytics-label">Retention Rate</div>
                </div>
                <div class="analytics-card">
                    <div class="analytics-number">7</div>
                    <div class="analytics-label">Days Streak</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Module Preview Modal -->
    <div class="module-modal" id="module-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modal-title">Module Preview</h3>
                <button type="button" class="modal-close" onclick="closeModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body" id="modal-body">
                <!-- Dynamic content will be loaded here -->
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/training-modules.js"></script>
</body>
</html>
