<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Training Modules - VirtualX Pro</title>
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/enterprise.css">
    <link rel="stylesheet" href="css/competition-features.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
    <style>
        .training-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .training-header {
            text-align: center;
            margin-bottom: 3rem;
            background: var(--enterprise-gradient);
            color: white;
            padding: 3rem 2rem;
            border-radius: var(--border-radius);
        }
        
        .learning-path-selector {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin-bottom: 3rem;
            flex-wrap: wrap;
        }
        
        .path-card {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: var(--border-radius);
            padding: 2rem;
            cursor: pointer;
            transition: var(--transition);
            text-align: center;
            min-width: 250px;
            position: relative;
        }
        
        .path-card.active {
            border-color: var(--enterprise-primary);
            background: var(--enterprise-light);
        }
        
        .path-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-enterprise);
        }
        
        .path-icon {
            font-size: 3rem;
            color: var(--enterprise-primary);
            margin-bottom: 1rem;
        }
        
        .path-title {
            font-size: 1.3rem;
            font-weight: 700;
            color: var(--enterprise-dark);
            margin-bottom: 0.5rem;
        }
        
        .path-description {
            color: #666;
            font-size: 0.9rem;
            margin-bottom: 1rem;
        }
        
        .path-stats {
            display: flex;
            justify-content: space-around;
            margin-top: 1rem;
            padding-top: 1rem;
            border-top: 1px solid #eee;
        }
        
        .path-stat {
            text-align: center;
        }
        
        .stat-number {
            font-size: 1.5rem;
            font-weight: bold;
            color: var(--enterprise-primary);
        }
        
        .stat-label {
            font-size: 0.8rem;
            color: #666;
        }
        
        .modules-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }
        
        .module-card {
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-enterprise);
            overflow: hidden;
            transition: var(--transition);
            position: relative;
        }
        
        .module-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 30px rgba(0, 102, 204, 0.2);
        }
        
        .module-header {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 1.5rem;
            position: relative;
        }
        
        .module-difficulty {
            position: absolute;
            top: 1rem;
            right: 1rem;
            padding: 0.25rem 0.75rem;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .difficulty-beginner {
            background: var(--enterprise-success);
            color: white;
        }
        
        .difficulty-intermediate {
            background: var(--enterprise-warning);
            color: white;
        }
        
        .difficulty-advanced {
            background: var(--enterprise-danger);
            color: white;
        }
        
        .module-icon {
            font-size: 3rem;
            color: var(--enterprise-primary);
            margin-bottom: 1rem;
        }
        
        .module-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--enterprise-dark);
            margin-bottom: 0.5rem;
        }
        
        .module-subtitle {
            color: #666;
            margin-bottom: 1rem;
        }
        
        .module-content {
            padding: 2rem;
        }
        
        .module-objectives {
            margin-bottom: 1.5rem;
        }
        
        .objective-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
        }
        
        .objective-item i {
            color: var(--enterprise-success);
            width: 16px;
        }
        
        .module-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin: 1.5rem 0;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: var(--border-radius);
        }
        
        .detail-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.9rem;
        }
        
        .detail-item i {
            color: var(--enterprise-primary);
            width: 16px;
        }
        
        .module-progress {
            margin: 1.5rem 0;
        }
        
        .progress-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
        }
        
        .progress-text {
            font-size: 0.9rem;
            color: #666;
        }
        
        .progress-percentage {
            font-weight: bold;
            color: var(--enterprise-primary);
        }
        
        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            background: var(--enterprise-success);
            transition: width 0.3s ease;
        }
        
        .module-actions {
            display: flex;
            gap: 1rem;
            margin-top: 1.5rem;
        }
        
        .btn-start {
            flex: 1;
            background: var(--enterprise-primary);
            color: white;
            border: none;
            padding: 1rem;
            border-radius: var(--border-radius);
            cursor: pointer;
            font-weight: 600;
            transition: var(--transition);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }
        
        .btn-start:hover {
            background: var(--enterprise-secondary);
            transform: translateY(-2px);
        }
        
        .btn-preview {
            background: transparent;
            color: var(--enterprise-primary);
            border: 2px solid var(--enterprise-primary);
            padding: 1rem;
            border-radius: var(--border-radius);
            cursor: pointer;
            font-weight: 600;
            transition: var(--transition);
        }
        
        .btn-preview:hover {
            background: var(--enterprise-primary);
            color: white;
        }
        
        .learning-analytics {
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-enterprise);
            padding: 2rem;
            margin: 3rem 0;
        }
        
        .analytics-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .analytics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 2rem;
        }
        
        .analytics-card {
            text-align: center;
            padding: 1.5rem;
            background: #f8f9fa;
            border-radius: var(--border-radius);
        }
        
        .analytics-number {
            font-size: 2.5rem;
            font-weight: bold;
            color: var(--enterprise-primary);
            margin-bottom: 0.5rem;
        }
        
        .analytics-label {
            color: #666;
            font-size: 0.9rem;
        }
        
        .recommended-path {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border: 2px solid var(--enterprise-primary);
            border-radius: var(--border-radius);
            padding: 2rem;
            margin: 3rem 0;
            text-align: center;
        }
        
        .recommendation-icon {
            font-size: 3rem;
            color: var(--enterprise-primary);
            margin-bottom: 1rem;
        }
        
        .achievement-badges {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin: 2rem 0;
            flex-wrap: wrap;
        }
        
        .badge {
            background: var(--enterprise-success);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .badge.locked {
            background: #ccc;
            color: #666;
        }
        
        .module-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.9);
            z-index: 10000;
            display: none;
            align-items: center;
            justify-content: center;
        }
        
        .modal-content {
            background: white;
            border-radius: var(--border-radius);
            width: 90%;
            max-width: 800px;
            max-height: 80%;
            overflow-y: auto;
        }
        
        .modal-header {
            background: var(--enterprise-gradient);
            color: white;
            padding: 1.5rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .modal-close {
            background: none;
            border: none;
            color: white;
            font-size: 1.5rem;
            cursor: pointer;
        }
        
        .modal-body {
            padding: 2rem;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="enterprise-nav">
        <div class="container">
            <div class="nav-content">
                <div class="nav-brand">
                    <a href="index.html">
                        <img src="images/virtualx-pro-logo.png" alt="VirtualX Pro" class="nav-logo" onerror="this.style.display='none'">
                        <span class="brand-text">VirtualX Pro</span>
                    </a>
                </div>
                <ul class="main-nav">
                    <li><a href="index.html"><i class="fas fa-home"></i> Dashboard</a></li>
                    <li><a href="system-models.html"><i class="fas fa-cogs"></i> System Models</a></li>
                    <li><a href="training-modules.html" class="active"><i class="fas fa-graduation-cap"></i> Training Modules</a></li>
                    <li><a href="simulation.html"><i class="fas fa-cube"></i> 3D Simulation</a></li>
                    <li><a href="ai-diagnostics.html"><i class="fas fa-stethoscope"></i> AI Diagnostics</a></li>
                    <li><a href="collaboration.html"><i class="fas fa-users"></i> Remote Support</a></li>
                    <li><a href="analytics.html"><i class="fas fa-chart-line"></i> Analytics</a></li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="training-container">
        <!-- Header Section -->
        <div class="training-header animate__animated animate__fadeInDown">
            <h1>Advanced Training Modules</h1>
            <p>Comprehensive learning paths designed for biomedical engineers and technical support professionals</p>
            <div style="margin-top: 2rem;">
                <div class="stat-item" style="display: inline-block; margin: 0 2rem;">
                    <div class="stat-number" style="font-size: 2rem; font-weight: bold;">25+</div>
                    <div class="stat-label">Training Modules</div>
                </div>
                <div class="stat-item" style="display: inline-block; margin: 0 2rem;">
                    <div class="stat-number" style="font-size: 2rem; font-weight: bold;">4</div>
                    <div class="stat-label">Learning Paths</div>
                </div>
                <div class="stat-item" style="display: inline-block; margin: 0 2rem;">
                    <div class="stat-number" style="font-size: 2rem; font-weight: bold;">100+</div>
                    <div class="stat-label">Hours Content</div>
                </div>
            </div>
        </div>

        <!-- Learning Path Selector -->
        <div class="learning-path-selector">
            <div class="path-card active" data-path="foundation">
                <div class="path-icon">
                    <i class="fas fa-seedling"></i>
                </div>
                <div class="path-title">Foundation Level</div>
                <div class="path-description">Essential knowledge for new technicians</div>
                <div class="path-stats">
                    <div class="path-stat">
                        <div class="stat-number">8</div>
                        <div class="stat-label">Modules</div>
                    </div>
                    <div class="path-stat">
                        <div class="stat-number">24h</div>
                        <div class="stat-label">Duration</div>
                    </div>
                </div>
            </div>
            
            <div class="path-card" data-path="intermediate">
                <div class="path-icon">
                    <i class="fas fa-tools"></i>
                </div>
                <div class="path-title">Intermediate Level</div>
                <div class="path-description">Advanced technical skills and troubleshooting</div>
                <div class="path-stats">
                    <div class="path-stat">
                        <div class="stat-number">10</div>
                        <div class="stat-label">Modules</div>
                    </div>
                    <div class="path-stat">
                        <div class="stat-number">35h</div>
                        <div class="stat-label">Duration</div>
                    </div>
                </div>
            </div>
            
            <div class="path-card" data-path="advanced">
                <div class="path-icon">
                    <i class="fas fa-rocket"></i>
                </div>
                <div class="path-title">Advanced Level</div>
                <div class="path-description">Expert-level system optimization and innovation</div>
                <div class="path-stats">
                    <div class="path-stat">
                        <div class="stat-number">7</div>
                        <div class="stat-label">Modules</div>
                    </div>
                    <div class="path-stat">
                        <div class="stat-number">28h</div>
                        <div class="stat-label">Duration</div>
                    </div>
                </div>
            </div>
            
            <div class="path-card" data-path="specialist">
                <div class="path-icon">
                    <i class="fas fa-crown"></i>
                </div>
                <div class="path-title">Specialist Level</div>
                <div class="path-description">Leadership and training expertise</div>
                <div class="path-stats">
                    <div class="path-stat">
                        <div class="stat-number">5</div>
                        <div class="stat-label">Modules</div>
                    </div>
                    <div class="path-stat">
                        <div class="stat-number">20h</div>
                        <div class="stat-label">Duration</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recommended Learning Path -->
        <div class="recommended-path">
            <div class="recommendation-icon">
                <i class="fas fa-lightbulb"></i>
            </div>
            <h3 style="color: var(--enterprise-primary); margin-bottom: 1rem;">Recommended for You</h3>
            <p style="margin-bottom: 1.5rem;">Based on your profile and progress, we recommend starting with the <strong>Intermediate Level</strong> path to build upon your existing knowledge.</p>
            <button type="button" class="btn-start" onclick="selectPath('intermediate')" style="max-width: 300px; margin: 0 auto;">
                <i class="fas fa-play"></i>
                Start Recommended Path
            </button>
        </div>

        <!-- Achievement Badges -->
        <div class="achievement-badges">
            <div class="badge">
                <i class="fas fa-medal"></i>
                Component Expert
            </div>
            <div class="badge">
                <i class="fas fa-trophy"></i>
                Safety Champion
            </div>
            <div class="badge locked">
                <i class="fas fa-lock"></i>
                Troubleshooting Master
            </div>
            <div class="badge locked">
                <i class="fas fa-star"></i>
                System Specialist
            </div>
        </div>

        <!-- Training Modules Grid -->
        <div class="modules-grid" id="modules-grid">
            <!-- Foundation Level Modules -->
            <div class="module-card" data-path="foundation">
                <div class="module-header">
                    <div class="module-difficulty difficulty-beginner">Beginner</div>
                    <div class="module-icon">
                        <i class="fas fa-sitemap"></i>
                    </div>
                    <div class="module-title">X-ray System Components</div>
                    <div class="module-subtitle">Interactive 3D Component Exploration</div>
                </div>
                <div class="module-content">
                    <div class="module-objectives">
                        <h4 style="margin-bottom: 1rem; color: var(--enterprise-primary);">Learning Objectives</h4>
                        <div class="objective-item">
                            <i class="fas fa-check"></i>
                            <span>Identify all major X-ray system components</span>
                        </div>
                        <div class="objective-item">
                            <i class="fas fa-check"></i>
                            <span>Understand component functions and interactions</span>
                        </div>
                        <div class="objective-item">
                            <i class="fas fa-check"></i>
                            <span>Practice virtual assembly procedures</span>
                        </div>
                        <div class="objective-item">
                            <i class="fas fa-check"></i>
                            <span>Compare multi-vendor system designs</span>
                        </div>
                    </div>
                    
                    <div class="module-details">
                        <div class="detail-item">
                            <i class="fas fa-clock"></i>
                            <span>Duration: 4 hours</span>
                        </div>
                        <div class="detail-item">
                            <i class="fas fa-cube"></i>
                            <span>3D Interactive</span>
                        </div>
                        <div class="detail-item">
                            <i class="fas fa-certificate"></i>
                            <span>Certificate: Yes</span>
                        </div>
                        <div class="detail-item">
                            <i class="fas fa-users"></i>
                            <span>Prerequisites: None</span>
                        </div>
                    </div>
                    
                    <div class="module-progress">
                        <div class="progress-header">
                            <span class="progress-text">Progress</span>
                            <span class="progress-percentage">75%</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 75%"></div>
                        </div>
                    </div>
                    
                    <div class="module-actions">
                        <button type="button" class="btn-start" onclick="startModule('components')">
                            <i class="fas fa-play"></i>
                            Continue Module
                        </button>
                        <button type="button" class="btn-preview" onclick="previewModule('components')">
                            <i class="fas fa-eye"></i>
                            Preview
                        </button>
                    </div>
                </div>
            </div>

            <!-- Intermediate Level Module -->
            <div class="module-card" data-path="intermediate">
                <div class="module-header">
                    <div class="module-difficulty difficulty-intermediate">Intermediate</div>
                    <div class="module-icon">
                        <i class="fas fa-brain"></i>
                    </div>
                    <div class="module-title">AI-Powered Diagnostics</div>
                    <div class="module-subtitle">Intelligent Fault Detection and Analysis</div>
                </div>
                <div class="module-content">
                    <div class="module-objectives">
                        <h4 style="margin-bottom: 1rem; color: var(--enterprise-primary);">Learning Objectives</h4>
                        <div class="objective-item">
                            <i class="fas fa-check"></i>
                            <span>Master AI diagnostic tools and interfaces</span>
                        </div>
                        <div class="objective-item">
                            <i class="fas fa-check"></i>
                            <span>Interpret AI analysis results accurately</span>
                        </div>
                        <div class="objective-item">
                            <i class="fas fa-check"></i>
                            <span>Combine AI insights with manual inspection</span>
                        </div>
                        <div class="objective-item">
                            <i class="fas fa-check"></i>
                            <span>Validate AI recommendations in practice</span>
                        </div>
                    </div>
                    
                    <div class="module-details">
                        <div class="detail-item">
                            <i class="fas fa-clock"></i>
                            <span>Duration: 6 hours</span>
                        </div>
                        <div class="detail-item">
                            <i class="fas fa-robot"></i>
                            <span>AI Simulation</span>
                        </div>
                        <div class="detail-item">
                            <i class="fas fa-certificate"></i>
                            <span>Certificate: Yes</span>
                        </div>
                        <div class="detail-item">
                            <i class="fas fa-users"></i>
                            <span>Prerequisites: Components</span>
                        </div>
                    </div>
                    
                    <div class="module-progress">
                        <div class="progress-header">
                            <span class="progress-text">Progress</span>
                            <span class="progress-percentage">0%</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 0%"></div>
                        </div>
                    </div>
                    
                    <div class="module-actions">
                        <button type="button" class="btn-start" onclick="startModule('ai-diagnostics')">
                            <i class="fas fa-play"></i>
                            Start Module
                        </button>
                        <button type="button" class="btn-preview" onclick="previewModule('ai-diagnostics')">
                            <i class="fas fa-eye"></i>
                            Preview
                        </button>
                    </div>
                </div>
            </div>

            <!-- Advanced Level Module -->
            <div class="module-card" data-path="advanced">
                <div class="module-header">
                    <div class="module-difficulty difficulty-advanced">Advanced</div>
                    <div class="module-icon">
                        <i class="fas fa-cogs"></i>
                    </div>
                    <div class="module-title">System Optimization</div>
                    <div class="module-subtitle">Performance Tuning and Advanced Configuration</div>
                </div>
                <div class="module-content">
                    <div class="module-objectives">
                        <h4 style="margin-bottom: 1rem; color: var(--enterprise-primary);">Learning Objectives</h4>
                        <div class="objective-item">
                            <i class="fas fa-check"></i>
                            <span>Optimize system performance parameters</span>
                        </div>
                        <div class="objective-item">
                            <i class="fas fa-check"></i>
                            <span>Implement advanced calibration procedures</span>
                        </div>
                        <div class="objective-item">
                            <i class="fas fa-check"></i>
                            <span>Configure custom imaging protocols</span>
                        </div>
                        <div class="objective-item">
                            <i class="fas fa-check"></i>
                            <span>Analyze system performance metrics</span>
                        </div>
                    </div>
                    
                    <div class="module-details">
                        <div class="detail-item">
                            <i class="fas fa-clock"></i>
                            <span>Duration: 8 hours</span>
                        </div>
                        <div class="detail-item">
                            <i class="fas fa-chart-line"></i>
                            <span>Performance Lab</span>
                        </div>
                        <div class="detail-item">
                            <i class="fas fa-certificate"></i>
                            <span>Certificate: Yes</span>
                        </div>
                        <div class="detail-item">
                            <i class="fas fa-users"></i>
                            <span>Prerequisites: Diagnostics</span>
                        </div>
                    </div>
                    
                    <div class="module-progress">
                        <div class="progress-header">
                            <span class="progress-text">Progress</span>
                            <span class="progress-percentage">0%</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 0%"></div>
                        </div>
                    </div>
                    
                    <div class="module-actions">
                        <button type="button" class="btn-start" onclick="startModule('optimization')" disabled style="opacity: 0.5;">
                            <i class="fas fa-lock"></i>
                            Locked
                        </button>
                        <button type="button" class="btn-preview" onclick="previewModule('optimization')">
                            <i class="fas fa-eye"></i>
                            Preview
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Learning Analytics -->
        <div class="learning-analytics">
            <div class="analytics-header">
                <h3 style="color: var(--enterprise-primary); margin-bottom: 1rem;">Your Learning Analytics</h3>
                <p style="color: #666;">Track your progress and identify areas for improvement</p>
            </div>
            
            <div class="analytics-grid">
                <div class="analytics-card">
                    <div class="analytics-number">12</div>
                    <div class="analytics-label">Modules Completed</div>
                </div>
                <div class="analytics-card">
                    <div class="analytics-number">87%</div>
                    <div class="analytics-label">Average Score</div>
                </div>
                <div class="analytics-card">
                    <div class="analytics-number">45h</div>
                    <div class="analytics-label">Total Study Time</div>
                </div>
                <div class="analytics-card">
                    <div class="analytics-number">3</div>
                    <div class="analytics-label">Certificates Earned</div>
                </div>
                <div class="analytics-card">
                    <div class="analytics-number">95%</div>
                    <div class="analytics-label">Retention Rate</div>
                </div>
                <div class="analytics-card">
                    <div class="analytics-number">7</div>
                    <div class="analytics-label">Days Streak</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Module Preview Modal -->
    <div class="module-modal" id="module-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modal-title">Module Preview</h3>
                <button type="button" class="modal-close" onclick="closeModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body" id="modal-body">
                <!-- Dynamic content will be loaded here -->
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/training-modules.js"></script>
</body>
</html>
