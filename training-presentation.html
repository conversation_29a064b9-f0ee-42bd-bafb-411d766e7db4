<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VirtualX Pro - Training Standards Presentation</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
    <link rel="stylesheet" href="css/presentation-animations.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-color: #0066cc;
            --secondary-color: #004499;
            --accent-color: #00aaff;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --dark-color: #2c3e50;
            --light-color: #ecf0f1;
            --gradient-primary: linear-gradient(135deg, #0066cc 0%, #004499 100%);
            --gradient-accent: linear-gradient(135deg, #00aaff 0%, #0066cc 100%);
            --shadow-light: 0 2px 10px rgba(0, 102, 204, 0.1);
            --shadow-medium: 0 4px 20px rgba(0, 102, 204, 0.2);
            --shadow-heavy: 0 8px 30px rgba(0, 102, 204, 0.3);
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .presentation-container {
            position: relative;
            width: 100%;
            height: 100vh;
            overflow: hidden;
        }

        .slide {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 2rem;
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .slide.active {
            opacity: 1;
            transform: translateX(0);
        }

        .slide.prev {
            transform: translateX(-100%);
        }

        /* Slide Backgrounds */
        .slide-1 { background: var(--gradient-primary); }
        .slide-2 { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .slide-3 { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
        .slide-4 { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
        .slide-5 { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }
        .slide-6 { background: linear-gradient(135deg, #fa709a 0%, #fee140 100%); }
        .slide-7 { background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%); }
        .slide-8 { background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%); }
        .slide-9 { background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%); }

        .slide-content {
            max-width: 1200px;
            width: 100%;
            text-align: center;
            color: white;
        }

        .slide-title {
            font-size: 3.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
            animation: slideInDown 1s ease-out;
        }

        .slide-subtitle {
            font-size: 1.5rem;
            margin-bottom: 2rem;
            opacity: 0.9;
            animation: slideInUp 1s ease-out 0.3s both;
        }

        .slide-description {
            font-size: 1.2rem;
            line-height: 1.6;
            margin-bottom: 3rem;
            opacity: 0.8;
            animation: fadeIn 1s ease-out 0.6s both;
        }

        /* Interactive Elements */
        .interactive-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin: 2rem 0;
        }

        .interactive-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 2rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
            cursor: pointer;
            transition: all 0.3s ease;
            animation: zoomIn 0.8s ease-out;
        }

        .interactive-card:hover {
            transform: translateY(-10px) scale(1.05);
            background: rgba(255, 255, 255, 0.2);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
        }

        .card-icon {
            font-size: 4rem;
            margin-bottom: 1rem;
            color: #fff;
            animation: bounce 2s infinite;
        }

        .card-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
        }

        .card-description {
            font-size: 1rem;
            opacity: 0.9;
            line-height: 1.5;
        }

        /* Progress Indicators */
        .progress-container {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin: 2rem 0;
        }

        .progress-step {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .progress-step.active {
            background: rgba(255, 255, 255, 0.9);
            color: var(--primary-color);
            transform: scale(1.2);
        }

        .progress-step.completed {
            background: var(--success-color);
            color: white;
        }

        /* Animated Icons */
        .animated-icon {
            display: inline-block;
            animation: pulse 2s infinite;
        }

        .rotating-icon {
            animation: rotate 3s linear infinite;
        }

        .floating-icon {
            animation: float 3s ease-in-out infinite;
        }

        /* Statistics Display */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 2rem;
            margin: 2rem 0;
        }

        .stat-item {
            text-align: center;
            animation: countUp 2s ease-out;
        }

        .stat-number {
            font-size: 3rem;
            font-weight: bold;
            color: #fff;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .stat-label {
            font-size: 1.1rem;
            opacity: 0.9;
            margin-top: 0.5rem;
        }

        /* Navigation Controls */
        .nav-controls {
            position: fixed;
            bottom: 2rem;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 1rem;
            z-index: 1000;
        }

        .nav-btn {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            font-size: 1.2rem;
        }

        .nav-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
        }

        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        /* Slide Indicators */
        .slide-indicators {
            position: fixed;
            right: 2rem;
            top: 50%;
            transform: translateY(-50%);
            display: flex;
            flex-direction: column;
            gap: 1rem;
            z-index: 1000;
        }

        .indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.3);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .indicator.active {
            background: white;
            transform: scale(1.5);
        }

        /* Module Showcase */
        .module-showcase {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin: 2rem 0;
        }

        .module-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 1.5rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .module-card:hover {
            transform: translateY(-5px);
            background: rgba(255, 255, 255, 0.2);
        }

        .module-icon {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            color: #fff;
        }

        .module-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .module-duration {
            font-size: 0.9rem;
            opacity: 0.8;
            margin-bottom: 1rem;
        }

        .module-features {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
        }

        .feature-tag {
            background: rgba(255, 255, 255, 0.2);
            padding: 0.25rem 0.75rem;
            border-radius: 12px;
            font-size: 0.8rem;
        }

        /* Animations */
        @keyframes slideInDown {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes zoomIn {
            from {
                opacity: 0;
                transform: scale(0.5);
            }
            to {
                opacity: 1;
                transform: scale(1);
            }
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-10px);
            }
            60% {
                transform: translateY(-5px);
            }
        }

        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        @keyframes countUp {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .slide-title {
                font-size: 2.5rem;
            }
            
            .slide-subtitle {
                font-size: 1.2rem;
            }
            
            .slide-description {
                font-size: 1rem;
            }
            
            .interactive-grid {
                grid-template-columns: 1fr;
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="presentation-container">
        <!-- Slide 1: Title Slide -->
        <div class="slide slide-1 active">
            <div class="slide-content">
                <h1 class="slide-title">
                    <i class="fas fa-atom animated-icon"></i>
                    VirtualX Pro
                </h1>
                <p class="slide-subtitle">Advanced X-ray Training Standards & Certification Program</p>
                <p class="slide-description">
                    Comprehensive professional development from entry-level technicians to certified expert engineers
                </p>
                
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-number" data-count="8">0</div>
                        <div class="stat-label">Training Modules</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" data-count="107">0</div>
                        <div class="stat-label">Training Hours</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" data-count="4">0</div>
                        <div class="stat-label">Certification Levels</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" data-count="95">0</div>
                        <div class="stat-label">Success Rate %</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 2: Program Overview -->
        <div class="slide slide-2">
            <div class="slide-content">
                <h1 class="slide-title">
                    <i class="fas fa-graduation-cap floating-icon"></i>
                    Training Program Overview
                </h1>
                <p class="slide-subtitle">Progressive Professional Development Pathway</p>
                
                <div class="progress-container">
                    <div class="progress-step completed">
                        <i class="fas fa-seedling"></i>
                    </div>
                    <div class="progress-step completed">
                        <i class="fas fa-tools"></i>
                    </div>
                    <div class="progress-step active">
                        <i class="fas fa-rocket"></i>
                    </div>
                    <div class="progress-step">
                        <i class="fas fa-crown"></i>
                    </div>
                </div>
                
                <div class="interactive-grid">
                    <div class="interactive-card" onclick="highlightLevel('foundation')">
                        <div class="card-icon">
                            <i class="fas fa-seedling"></i>
                        </div>
                        <div class="card-title">Foundation Level</div>
                        <div class="card-description">
                            Entry-level technicians<br>
                            Physics • Components • Safety<br>
                            <strong>19 hours total</strong>
                        </div>
                    </div>
                    
                    <div class="interactive-card" onclick="highlightLevel('intermediate')">
                        <div class="card-icon">
                            <i class="fas fa-tools"></i>
                        </div>
                        <div class="card-title">Intermediate Level</div>
                        <div class="card-description">
                            Experienced technicians<br>
                            Generators • AI Diagnostics<br>
                            <strong>22 hours total</strong>
                        </div>
                    </div>
                    
                    <div class="interactive-card" onclick="highlightLevel('advanced')">
                        <div class="card-icon">
                            <i class="fas fa-rocket"></i>
                        </div>
                        <div class="card-title">Advanced Level</div>
                        <div class="card-description">
                            Senior engineers<br>
                            Image Processing • Integration<br>
                            <strong>33 hours total</strong>
                        </div>
                    </div>
                    
                    <div class="interactive-card" onclick="highlightLevel('expert')">
                        <div class="card-icon">
                            <i class="fas fa-crown"></i>
                        </div>
                        <div class="card-title">Expert Level</div>
                        <div class="card-description">
                            Certified engineers<br>
                            Professional Certification<br>
                            <strong>25 hours total</strong>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 3: Foundation Level -->
        <div class="slide slide-3">
            <div class="slide-content">
                <h1 class="slide-title">
                    <i class="fas fa-seedling rotating-icon"></i>
                    Foundation Level Training
                </h1>
                <p class="slide-subtitle">Essential Knowledge for Entry-Level Technicians</p>

                <div class="module-showcase">
                    <div class="module-card" onclick="showModuleDetails('physics')">
                        <div class="module-icon">
                            <i class="fas fa-atom"></i>
                        </div>
                        <div class="module-title">X-ray Physics Fundamentals</div>
                        <div class="module-duration">6 hours • IEC 60601 Compliant</div>
                        <div class="module-features">
                            <span class="feature-tag">Physics Simulation</span>
                            <span class="feature-tag">Interactive Tools</span>
                            <span class="feature-tag">Safety Training</span>
                        </div>
                    </div>

                    <div class="module-card" onclick="showModuleDetails('components')">
                        <div class="module-icon">
                            <i class="fas fa-sitemap"></i>
                        </div>
                        <div class="module-title">System Components</div>
                        <div class="module-duration">8 hours • Component Specialist</div>
                        <div class="module-features">
                            <span class="feature-tag">3D Models</span>
                            <span class="feature-tag">Virtual Assembly</span>
                            <span class="feature-tag">Multi-vendor</span>
                        </div>
                    </div>

                    <div class="module-card" onclick="showModuleDetails('safety')">
                        <div class="module-icon">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <div class="module-title">Safety & Radiation Protection</div>
                        <div class="module-duration">5 hours • Safety Officer Cert</div>
                        <div class="module-features">
                            <span class="feature-tag">ALARA Principles</span>
                            <span class="feature-tag">Emergency Response</span>
                            <span class="feature-tag">Compliance</span>
                        </div>
                    </div>
                </div>

                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-number">100%</div>
                        <div class="stat-label">Physics Mastery</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">3D</div>
                        <div class="stat-label">Interactive Models</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">IEC</div>
                        <div class="stat-label">Standards Compliant</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 4: Intermediate Level -->
        <div class="slide slide-4">
            <div class="slide-content">
                <h1 class="slide-title">
                    <i class="fas fa-tools floating-icon"></i>
                    Intermediate Level Training
                </h1>
                <p class="slide-subtitle">Advanced Technical Skills for Experienced Technicians</p>

                <div class="module-showcase">
                    <div class="module-card" onclick="showModuleDetails('generators')">
                        <div class="module-icon">
                            <i class="fas fa-bolt"></i>
                        </div>
                        <div class="module-title">Generator Systems & Power Electronics</div>
                        <div class="module-duration">12 hours • Power Systems Cert</div>
                        <div class="module-features">
                            <span class="feature-tag">IGBT Circuits</span>
                            <span class="feature-tag">Oscilloscope</span>
                            <span class="feature-tag">Control Systems</span>
                        </div>
                    </div>

                    <div class="module-card" onclick="showModuleDetails('ai-diagnostics')">
                        <div class="module-icon">
                            <i class="fas fa-brain"></i>
                        </div>
                        <div class="module-title">AI-Powered Diagnostics</div>
                        <div class="module-duration">10 hours • AI Diagnostics Cert</div>
                        <div class="module-features">
                            <span class="feature-tag">Neural Networks</span>
                            <span class="feature-tag">10,000+ Cases</span>
                            <span class="feature-tag">95.2% Accuracy</span>
                        </div>
                    </div>
                </div>

                <div class="interactive-grid">
                    <div class="interactive-card">
                        <div class="card-icon">
                            <i class="fas fa-microchip animated-icon"></i>
                        </div>
                        <div class="card-title">Circuit Analysis</div>
                        <div class="card-description">
                            Virtual oscilloscope and SPICE simulation for power electronics mastery
                        </div>
                    </div>

                    <div class="interactive-card">
                        <div class="card-icon">
                            <i class="fas fa-robot animated-icon"></i>
                        </div>
                        <div class="card-title">AI Training Lab</div>
                        <div class="card-description">
                            Machine learning model training with real service case database
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 5: Advanced Level -->
        <div class="slide slide-5">
            <div class="slide-content">
                <h1 class="slide-title">
                    <i class="fas fa-rocket rotating-icon"></i>
                    Advanced Level Training
                </h1>
                <p class="slide-subtitle">Expert-Level Skills for Senior Engineers</p>

                <div class="module-showcase">
                    <div class="module-card" onclick="showModuleDetails('image-processing')">
                        <div class="module-icon">
                            <i class="fas fa-image"></i>
                        </div>
                        <div class="module-title">Advanced Image Processing & QA</div>
                        <div class="module-duration">15 hours • QA Specialist Cert</div>
                        <div class="module-features">
                            <span class="feature-tag">MTF Analysis</span>
                            <span class="feature-tag">DQE Testing</span>
                            <span class="feature-tag">IEC 61223-2-6</span>
                        </div>
                    </div>

                    <div class="module-card" onclick="showModuleDetails('integration')">
                        <div class="module-icon">
                            <i class="fas fa-network-wired"></i>
                        </div>
                        <div class="module-title">System Integration & Networks</div>
                        <div class="module-duration">18 hours • Integration Expert</div>
                        <div class="module-features">
                            <span class="feature-tag">DICOM/HL7</span>
                            <span class="feature-tag">Enterprise</span>
                            <span class="feature-tag">Security</span>
                        </div>
                    </div>
                </div>

                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-number">MTF</div>
                        <div class="stat-label">Analysis Tools</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">DICOM</div>
                        <div class="stat-label">Integration</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">TLS 1.3</div>
                        <div class="stat-label">Security</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">99.9%</div>
                        <div class="stat-label">Uptime SLA</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 6: Expert Level -->
        <div class="slide slide-6">
            <div class="slide-content">
                <h1 class="slide-title">
                    <i class="fas fa-crown floating-icon"></i>
                    Expert Level Certification
                </h1>
                <p class="slide-subtitle">Professional Engineer Certification Program</p>

                <div class="module-showcase">
                    <div class="module-card" onclick="showModuleDetails('certified-engineer')">
                        <div class="module-icon">
                            <i class="fas fa-graduation-cap"></i>
                        </div>
                        <div class="module-title">Certified Technical Engineer Program</div>
                        <div class="module-duration">25 hours • Professional Engineer</div>
                        <div class="module-features">
                            <span class="feature-tag">PE Certification</span>
                            <span class="feature-tag">Leadership</span>
                            <span class="feature-tag">Standards Mastery</span>
                        </div>
                    </div>
                </div>

                <div class="interactive-grid">
                    <div class="interactive-card">
                        <div class="card-icon">
                            <i class="fas fa-clipboard-check animated-icon"></i>
                        </div>
                        <div class="card-title">IEC 60601 Mastery</div>
                        <div class="card-description">
                            Complete mastery of all IEC 60601 series standards and compliance testing
                        </div>
                    </div>

                    <div class="interactive-card">
                        <div class="card-icon">
                            <i class="fas fa-users-cog animated-icon"></i>
                        </div>
                        <div class="card-title">Project Leadership</div>
                        <div class="card-description">
                            Lead complex multi-system installations and commissioning projects
                        </div>
                    </div>

                    <div class="interactive-card">
                        <div class="card-icon">
                            <i class="fas fa-chalkboard-teacher animated-icon"></i>
                        </div>
                        <div class="card-title">Training Development</div>
                        <div class="card-description">
                            Develop custom training programs and technical documentation
                        </div>
                    </div>
                </div>

                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-number">90%</div>
                        <div class="stat-label">Exam Requirement</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">5</div>
                        <div class="stat-label">Case Studies</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">PE</div>
                        <div class="stat-label">Certification</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 7: Interactive Tools & Simulation -->
        <div class="slide slide-7">
            <div class="slide-content">
                <h1 class="slide-title">
                    <i class="fas fa-cube rotating-icon"></i>
                    Interactive Tools & Real Environment Simulation
                </h1>
                <p class="slide-subtitle">Cutting-Edge Technology for Immersive Learning</p>

                <div class="interactive-grid">
                    <div class="interactive-card">
                        <div class="card-icon">
                            <i class="fas fa-vr-cardboard animated-icon"></i>
                        </div>
                        <div class="card-title">3D Virtual Reality</div>
                        <div class="card-description">
                            Immersive VR training with haptic feedback and real-time physics simulation
                        </div>
                    </div>

                    <div class="interactive-card">
                        <div class="card-icon">
                            <i class="fas fa-microscope animated-icon"></i>
                        </div>
                        <div class="card-title">Professional Instruments</div>
                        <div class="card-description">
                            Virtual oscilloscopes, multimeters, and spectrum analyzers with real interfaces
                        </div>
                    </div>

                    <div class="interactive-card">
                        <div class="card-icon">
                            <i class="fas fa-brain animated-icon"></i>
                        </div>
                        <div class="card-title">AI-Powered Learning</div>
                        <div class="card-description">
                            Adaptive learning paths with personalized recommendations and progress tracking
                        </div>
                    </div>

                    <div class="interactive-card">
                        <div class="card-icon">
                            <i class="fas fa-globe animated-icon"></i>
                        </div>
                        <div class="card-title">Global Accessibility</div>
                        <div class="card-description">
                            12 languages, WCAG 2.1 compliance, and cross-platform compatibility
                        </div>
                    </div>
                </div>

                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-number">60</div>
                        <div class="stat-label">FPS Real-time</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">WebGL</div>
                        <div class="stat-label">Acceleration</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">VR/AR</div>
                        <div class="stat-label">Compatible</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 8: Performance Metrics -->
        <div class="slide slide-8">
            <div class="slide-content">
                <h1 class="slide-title">
                    <i class="fas fa-chart-line floating-icon"></i>
                    Training Effectiveness & ROI
                </h1>
                <p class="slide-subtitle">Proven Results & Business Impact</p>

                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-number" data-count="85">0</div>
                        <div class="stat-label">% Knowledge Retention Improvement</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" data-count="50">0</div>
                        <div class="stat-label">% Faster Skill Acquisition</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" data-count="60">0</div>
                        <div class="stat-label">% Reduction in Field Errors</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" data-count="40">0</div>
                        <div class="stat-label">% Lower Training Costs</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" data-count="95">0</div>
                        <div class="stat-label">% First-time Certification Pass</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" data-count="300">0</div>
                        <div class="stat-label">% ROI Achievement</div>
                    </div>
                </div>

                <div class="interactive-grid">
                    <div class="interactive-card">
                        <div class="card-icon">
                            <i class="fas fa-trophy animated-icon"></i>
                        </div>
                        <div class="card-title">Industry Leadership</div>
                        <div class="card-description">
                            World-class training platform recognized by leading medical equipment manufacturers
                        </div>
                    </div>

                    <div class="interactive-card">
                        <div class="card-icon">
                            <i class="fas fa-users animated-icon"></i>
                        </div>
                        <div class="card-title">Global Deployment</div>
                        <div class="card-description">
                            Successfully deployed in 120+ countries with 24/7 availability and support
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 9: Call to Action -->
        <div class="slide slide-9">
            <div class="slide-content">
                <h1 class="slide-title">
                    <i class="fas fa-rocket floating-icon"></i>
                    Transform Your Training Program
                </h1>
                <p class="slide-subtitle">Join the Future of X-ray Technical Education</p>
                <p class="slide-description">
                    Ready to revolutionize your technical training with VirtualX Pro's comprehensive certification program?
                </p>

                <div class="interactive-grid">
                    <div class="interactive-card" onclick="startDemo()">
                        <div class="card-icon">
                            <i class="fas fa-play-circle animated-icon"></i>
                        </div>
                        <div class="card-title">Start Free Demo</div>
                        <div class="card-description">
                            Experience our training modules with a comprehensive 30-day trial
                        </div>
                    </div>

                    <div class="interactive-card" onclick="scheduleConsultation()">
                        <div class="card-icon">
                            <i class="fas fa-calendar-alt animated-icon"></i>
                        </div>
                        <div class="card-title">Schedule Consultation</div>
                        <div class="card-description">
                            Discuss your training needs with our technical education specialists
                        </div>
                    </div>

                    <div class="interactive-card" onclick="downloadBrochure()">
                        <div class="card-icon">
                            <i class="fas fa-download animated-icon"></i>
                        </div>
                        <div class="card-title">Download Brochure</div>
                        <div class="card-description">
                            Get detailed information about our certification programs and pricing
                        </div>
                    </div>
                </div>

                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-number">24/7</div>
                        <div class="stat-label">Global Support</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">30</div>
                        <div class="stat-label">Day Free Trial</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">∞</div>
                        <div class="stat-label">Scalable Users</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Navigation Controls -->
    <div class="nav-controls">
        <button type="button" class="nav-btn" id="prevBtn" onclick="changeSlide(-1)" title="Previous Slide">
            <i class="fas fa-chevron-left"></i>
        </button>
        <button type="button" class="nav-btn" id="playBtn" onclick="toggleAutoplay()" title="Play/Pause">
            <i class="fas fa-play"></i>
        </button>
        <button type="button" class="nav-btn" id="nextBtn" onclick="changeSlide(1)" title="Next Slide">
            <i class="fas fa-chevron-right"></i>
        </button>
    </div>

    <!-- Slide Indicators -->
    <div class="slide-indicators">
        <div class="indicator active" onclick="goToSlide(0)"></div>
        <div class="indicator" onclick="goToSlide(1)"></div>
        <div class="indicator" onclick="goToSlide(2)"></div>
        <div class="indicator" onclick="goToSlide(3)"></div>
        <div class="indicator" onclick="goToSlide(4)"></div>
        <div class="indicator" onclick="goToSlide(5)"></div>
        <div class="indicator" onclick="goToSlide(6)"></div>
        <div class="indicator" onclick="goToSlide(7)"></div>
        <div class="indicator" onclick="goToSlide(8)"></div>
    </div>

    <script src="js/training-presentation.js"></script>
</body>
</html>
