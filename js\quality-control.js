/**
 * JavaScript for X-ray System Quality Control Module
 */

document.addEventListener('DOMContentLoaded', function() {
    // Navigation active state management
    const navLinks = document.querySelectorAll('nav ul li a');
    const sections = document.querySelectorAll('section[id]');
    
    // Smooth scrolling for navigation links
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            // Only prevent default if it's an anchor link
            if (this.getAttribute('href').startsWith('#')) {
                e.preventDefault();
                
                const targetId = this.getAttribute('href');
                const targetSection = document.querySelector(targetId);
                
                if (targetSection) {
                    window.scrollTo({
                        top: targetSection.offsetTop - 60, // Adjust for nav height
                        behavior: 'smooth'
                    });
                    
                    // Update active link
                    navLinks.forEach(link => link.classList.remove('active'));
                    this.classList.add('active');
                }
            }
        });
    });
    
    // Update active navigation link on scroll
    window.addEventListener('scroll', function() {
        let current = '';
        
        sections.forEach(section => {
            const sectionTop = section.offsetTop;
            const sectionHeight = section.clientHeight;
            
            if (pageYOffset >= sectionTop - 100) {
                current = section.getAttribute('id');
            }
        });
        
        navLinks.forEach(link => {
            link.classList.remove('active');
            const href = link.getAttribute('href');
            if (href && href === `#${current}`) {
                link.classList.add('active');
            }
        });
    });
    
    // Initialize test procedure buttons
    initializeTestProcedureButtons();
});

/**
 * Initialize test procedure buttons to show detailed procedures
 */
function initializeTestProcedureButtons() {
    const procedureButtons = document.querySelectorAll('.test-procedure-btn');
    
    procedureButtons.forEach(button => {
        button.addEventListener('click', function() {
            const testType = this.getAttribute('data-test');
            showTestProcedureModal(testType);
        });
    });
}

/**
 * Show a modal with detailed test procedure
 * @param {string} testType - The type of test to show procedure for
 */
function showTestProcedureModal(testType) {
    // Get procedure content based on test type
    const procedureContent = getTestProcedureContent(testType);
    
    // Create modal container
    const modalContainer = document.createElement('div');
    modalContainer.className = 'procedure-modal-container';
    
    // Create modal
    const modal = document.createElement('div');
    modal.className = 'procedure-modal';
    
    // Create modal header
    const modalHeader = document.createElement('div');
    modalHeader.className = 'modal-header';
    
    const modalTitle = document.createElement('h3');
    modalTitle.textContent = procedureContent.title;
    
    const closeButton = document.createElement('button');
    closeButton.innerHTML = '&times;';
    closeButton.className = 'close-btn';
    
    modalHeader.appendChild(modalTitle);
    modalHeader.appendChild(closeButton);
    
    // Create modal body
    const modalBody = document.createElement('div');
    modalBody.className = 'modal-body';
    modalBody.innerHTML = procedureContent.content;
    
    // Assemble modal
    modal.appendChild(modalHeader);
    modal.appendChild(modalBody);
    modalContainer.appendChild(modal);
    
    // Add close functionality
    closeButton.addEventListener('click', function() {
        document.body.removeChild(modalContainer);
    });
    
    // Add click outside to close
    modalContainer.addEventListener('click', function(e) {
        if (e.target === modalContainer) {
            document.body.removeChild(modalContainer);
        }
    });
    
    // Add to document
    document.body.appendChild(modalContainer);
}

/**
 * Get detailed procedure content for a specific test
 * @param {string} testType - The type of test
 * @returns {object} - Object containing title and content for the procedure
 */
function getTestProcedureContent(testType) {
    const procedures = {
        'kvp': {
            title: 'Detailed kVp Accuracy Test Procedure',
            content: `
                <p>The kVp accuracy test verifies that the actual peak kilovoltage (kVp) matches the selected kVp within acceptable tolerance. This is critical for ensuring proper image contrast and patient dose.</p>
                
                <h4>Required Equipment</h4>
                <ul>
                    <li>Calibrated kVp meter or multi-parameter meter</li>
                    <li>Solid state detector or ionization chamber</li>
                    <li>Appropriate stand or positioning device</li>
                    <li>Data recording form or software</li>
                </ul>
                
                <h4>Safety Precautions</h4>
                <ul>
                    <li>Wear appropriate radiation protection (lead apron, thyroid shield)</li>
                    <li>Maintain safe distance during exposures</li>
                    <li>Use lowest practical technique factors</li>
                    <li>Follow ALARA principles (As Low As Reasonably Achievable)</li>
                </ul>
                
                <h4>Detailed Procedure</h4>
                <ol>
                    <li>Ensure the X-ray system has been warmed up according to manufacturer recommendations</li>
                    <li>Position the kVp meter at the appropriate distance from the X-ray tube (typically 100 cm)</li>
                    <li>Center the detector to the X-ray beam using the light field</li>
                    <li>Collimate to the active area of the detector</li>
                    <li>Select a series of kVp settings to test across the operational range:
                        <ul>
                            <li>Low range: 50-60 kVp</li>
                            <li>Mid range: 70-90 kVp</li>
                            <li>High range: 100-120 kVp</li>
                        </ul>
                    </li>
                    <li>For each kVp setting:
                        <ol>
                            <li>Select appropriate mA and exposure time (typically 10-20 mAs)</li>
                            <li>Make at least three exposures</li>
                            <li>Record the measured kVp for each exposure</li>
                            <li>Calculate the average measured kVp</li>
                            <li>Calculate the percentage difference: % Difference = ((Measured kVp - Set kVp) / Set kVp) × 100%</li>
                        </ol>
                    </li>
                    <li>Document all results in the QC record</li>
                </ol>
                
                <h4>Acceptance Criteria</h4>
                <p>The measured kVp should be within ±5% of the selected value across the operational range.</p>
                
                <h4>Troubleshooting</h4>
                <p>If the kVp accuracy is outside acceptable limits:</p>
                <ul>
                    <li>Verify the kVp meter is properly calibrated</li>
                    <li>Check for proper positioning of the meter</li>
                    <li>Ensure adequate warm-up of the X-ray system</li>
                    <li>Check for line voltage fluctuations</li>
                    <li>Consult service documentation for calibration procedures</li>
                    <li>Contact service engineer if calibration is required</li>
                </ul>
                
                <div class="note">
                    <p><strong>Note:</strong> Always follow manufacturer's specific recommendations for testing procedures and acceptance criteria, as they may vary by equipment model.</p>
                </div>
            `
        },
        'ma': {
            title: 'Detailed mA/mAs Linearity Test Procedure',
            content: `
                <p>The mA/mAs linearity test verifies that radiation output is proportional to the selected mA or mAs (tube current or current-time product). This ensures consistent image quality across different technique settings.</p>
                
                <h4>Required Equipment</h4>
                <ul>
                    <li>Calibrated dosimeter or radiation meter</li>
                    <li>Solid state detector or ionization chamber</li>
                    <li>Appropriate stand or positioning device</li>
                    <li>Data recording form or software</li>
                    <li>Calculator or spreadsheet for analysis</li>
                </ul>
                
                <h4>Safety Precautions</h4>
                <ul>
                    <li>Wear appropriate radiation protection (lead apron, thyroid shield)</li>
                    <li>Maintain safe distance during exposures</li>
                    <li>Use lowest practical technique factors</li>
                    <li>Follow ALARA principles (As Low As Reasonably Achievable)</li>
                </ul>
                
                <h4>Detailed Procedure</h4>
                <ol>
                    <li>Ensure the X-ray system has been warmed up according to manufacturer recommendations</li>
                    <li>Position the dosimeter at a fixed distance from the X-ray tube (typically 100 cm)</li>
                    <li>Center the detector to the X-ray beam using the light field</li>
                    <li>Collimate to the active area of the detector</li>
                    <li>Select a fixed kVp (typically 80 kVp) for all exposures</li>
                    <li>For mA linearity:
                        <ol>
                            <li>Select a fixed exposure time (e.g., 100 ms)</li>
                            <li>Make exposures at various mA settings (e.g., 100, 200, 300, 400 mA)</li>
                            <li>Record radiation output (mGy) for each setting</li>
                            <li>Calculate mAs for each exposure (mA × time in seconds)</li>
                            <li>Calculate output per mAs (mGy/mAs) for each measurement</li>
                        </ol>
                    </li>
                    <li>For mAs linearity (if using mAs settings):
                        <ol>
                            <li>Make exposures at various mAs settings (e.g., 10, 20, 40, 80 mAs)</li>
                            <li>Record radiation output (mGy) for each setting</li>
                            <li>Calculate output per mAs (mGy/mAs) for each measurement</li>
                        </ol>
                    </li>
                    <li>Calculate the average output per mAs across all measurements</li>
                    <li>Calculate the maximum deviation from the average: % Deviation = ((Max Output/mAs - Min Output/mAs) / Average Output/mAs) × 100%</li>
                    <li>Document all results in the QC record</li>
                </ol>
                
                <h4>Acceptance Criteria</h4>
                <p>The output per mAs should be consistent within ±10% across the operational range.</p>
                
                <h4>Troubleshooting</h4>
                <p>If the mA/mAs linearity is outside acceptable limits:</p>
                <ul>
                    <li>Verify the dosimeter is properly calibrated</li>
                    <li>Check for proper positioning of the detector</li>
                    <li>Ensure adequate warm-up of the X-ray system</li>
                    <li>Check for line voltage fluctuations</li>
                    <li>Verify filament circuit operation</li>
                    <li>Check for issues with the high voltage generator</li>
                    <li>Contact service engineer if calibration is required</li>
                </ul>
                
                <div class="note">
                    <p><strong>Note:</strong> Always follow manufacturer's specific recommendations for testing procedures and acceptance criteria, as they may vary by equipment model.</p>
                </div>
            `
        },
        'reproducibility': {
            title: 'Detailed Exposure Reproducibility Test Procedure',
            content: `
                <p>The exposure reproducibility test verifies that the X-ray system produces consistent radiation output when the same exposure factors are used repeatedly. This ensures reliable and predictable image quality.</p>
                
                <h4>Required Equipment</h4>
                <ul>
                    <li>Calibrated dosimeter or radiation meter</li>
                    <li>Solid state detector or ionization chamber</li>
                    <li>Appropriate stand or positioning device</li>
                    <li>Data recording form or software</li>
                    <li>Calculator or spreadsheet for statistical analysis</li>
                </ul>
                
                <h4>Safety Precautions</h4>
                <ul>
                    <li>Wear appropriate radiation protection (lead apron, thyroid shield)</li>
                    <li>Maintain safe distance during exposures</li>
                    <li>Use lowest practical technique factors</li>
                    <li>Follow ALARA principles (As Low As Reasonably Achievable)</li>
                </ul>
                
                <h4>Detailed Procedure</h4>
                <ol>
                    <li>Ensure the X-ray system has been warmed up according to manufacturer recommendations</li>
                    <li>Position the dosimeter at a fixed distance from the X-ray tube (typically 100 cm)</li>
                    <li>Center the detector to the X-ray beam using the light field</li>
                    <li>Collimate to the active area of the detector</li>
                    <li>Select fixed technique factors:
                        <ul>
                            <li>kVp: 80 kVp (or clinically relevant value)</li>
                            <li>mA: 100-200 mA (or clinically relevant value)</li>
                            <li>Time: 100 ms (or clinically relevant value)</li>
                        </ul>
                    </li>
                    <li>Make at least 5 consecutive exposures with the same settings</li>
                    <li>Record radiation output (mGy) for each exposure</li>
                    <li>Calculate the mean (average) output</li>
                    <li>Calculate the standard deviation (SD) of the measurements</li>
                    <li>Calculate the coefficient of variation (CV): CV = (SD / Mean) × 100%</li>
                    <li>Document all results in the QC record</li>
                </ol>
                
                <h4>Acceptance Criteria</h4>
                <p>The coefficient of variation (CV) should be less than 5% for consecutive exposures.</p>
                
                <h4>Troubleshooting</h4>
                <p>If the reproducibility is outside acceptable limits:</p>
                <ul>
                    <li>Verify the dosimeter is properly calibrated</li>
                    <li>Check for proper positioning of the detector</li>
                    <li>Ensure adequate warm-up of the X-ray system</li>
                    <li>Check for line voltage fluctuations</li>
                    <li>Verify timer accuracy</li>
                    <li>Check for issues with the high voltage generator</li>
                    <li>Inspect filament circuit for stability</li>
                    <li>Contact service engineer if calibration is required</li>
                </ul>
                
                <div class="note">
                    <p><strong>Note:</strong> Always follow manufacturer's specific recommendations for testing procedures and acceptance criteria, as they may vary by equipment model.</p>
                </div>
            `
        }
    };
    
    // Return the requested procedure or a default message if not found
    return procedures[testType] || {
        title: 'Test Procedure',
        content: '<p>Detailed procedure not available for this test.</p>'
    };
}
